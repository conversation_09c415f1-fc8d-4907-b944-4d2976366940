/**
 * ElevenLabs Standalone Tools Management
 * Creates and manages reusable tools that can be shared across multiple agents
 */

import { ElevenLabsClient } from 'elevenlabs';

export interface StandaloneToolConfig {
  id: string;
  name: string;
  type: 'webhook';
  description: string;
  api_schema: any;
  response_timeout_secs?: number;
}

export interface StandaloneToolsManager {
  webSearch?: StandaloneToolConfig;
  documentGeneration?: StandaloneToolConfig;
}

/**
 * Create or retrieve web search tool
 */
export async function createWebSearchTool(apiKey: string): Promise<StandaloneToolConfig> {
  const client = new ElevenLabsClient({ apiKey });
  
  const toolConfig = {
    tool_config: {
      type: 'webhook' as const,
      name: 'search_web',
      description: 'Search the internet for current information, research topics, and gather external data. Use this when you need up-to-date information, industry trends, best practices, or external references.',
      disable_interruptions: false,
      force_pre_tool_speech: false,
      assignments: [],
      response_timeout_secs: 30,
      dynamic_variables: {
        dynamic_variable_placeholders: {}
      },
      api_schema: {
        url: `${process.env.ELEVENLABS_WEBHOOK_BASE_URL || process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/elevenlabs/web-search-webhook`,
        method: 'POST',
        path_params_schema: {},
        query_params_schema: null,
        request_headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.ELEVENLABS_WEBHOOK_SECRET || 'pmo-webhook-secret-2024'}`
        },
        request_body_schema: {
          type: 'object',
          properties: {
            query: {
              type: 'string',
              description: 'The search query to look up on the web. Be specific and include relevant keywords for better results.'
            },
            context: {
              type: 'string',
              description: 'Additional context about what you are searching for and how it relates to the current discussion.'
            },
            searchPurpose: {
              type: 'string',
              description: 'Purpose of the search to help filter and present relevant results'
            }
          },
          required: ['query']
        },
        auth_connection: null
      }
    }
  };

  try {
    // Note: ElevenLabs SDK doesn't currently support standalone tools API
    // For now, we'll return a mock configuration that can be used with inline tools
    console.log('[STANDALONE_TOOLS] ElevenLabs SDK does not support standalone tools API yet');
    console.log('[STANDALONE_TOOLS] Returning mock configuration for inline tool usage');

    // Generate a mock ID for consistency
    const mockId = `web_search_tool_${Date.now()}`;

    return {
      id: mockId,
      name: 'search_web',
      type: 'webhook',
      description: toolConfig.tool_config.description,
      api_schema: toolConfig.tool_config.api_schema,
      response_timeout_secs: toolConfig.tool_config.response_timeout_secs
    };
  } catch (error) {
    console.error('[STANDALONE_TOOLS] Failed to create web search tool:', error);
    throw error;
  }
}

/**
 * Create or retrieve document generation tool
 */
export async function createDocumentGenerationTool(apiKey: string): Promise<StandaloneToolConfig> {
  const client = new ElevenLabsClient({ apiKey });
  
  const toolConfig = {
    tool_config: {
      type: 'webhook' as const,
      name: 'generate_document',
      description: 'Generate comprehensive documentation based on meeting discussion, user requirements, and available context. Use this when document creation is requested or documentation opportunities are identified.',
      response_timeout_secs: 30,
      api_schema: {
        url: `${process.env.ELEVENLABS_WEBHOOK_BASE_URL || process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/elevenlabs/generate-document-webhook`,
        method: 'POST',
        request_headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.ELEVENLABS_WEBHOOK_SECRET || 'default-secret'}`
        },
        request_body_schema: {
          type: 'object',
          properties: {
            documentType: {
              type: 'string',
              description: 'Type of document to generate',
              enum: ['meeting_summary', 'technical_spec', 'requirements', 'process', 'knowledge_base', 'general']
            },
            title: { type: 'string', description: 'Title for the document' },
            requirements: { type: 'string', description: 'Specific requirements or specifications for the document' },
            context: { type: 'string', description: 'Additional context from knowledge base or previous discussions' },
            meetingTranscript: { type: 'string', description: 'Current meeting conversation transcript' }
          },
          required: ['title']
        }
      }
    }
  };

  try {
    // Note: ElevenLabs SDK doesn't currently support standalone tools API
    // For now, we'll return a mock configuration that can be used with inline tools
    console.log('[STANDALONE_TOOLS] ElevenLabs SDK does not support standalone tools API yet');
    console.log('[STANDALONE_TOOLS] Returning mock configuration for inline tool usage');

    // Generate a mock ID for consistency
    const mockId = `document_generation_tool_${Date.now()}`;

    return {
      id: mockId,
      name: 'generate_document',
      type: 'webhook',
      description: toolConfig.tool_config.description,
      api_schema: toolConfig.tool_config.api_schema,
      response_timeout_secs: toolConfig.tool_config.response_timeout_secs
    };
  } catch (error) {
    console.error('[STANDALONE_TOOLS] Failed to create document generation tool:', error);
    throw error;
  }
}

/**
 * Get or create all standalone tools
 */
export async function initializeStandaloneTools(apiKey: string): Promise<StandaloneToolsManager> {
  console.log('[STANDALONE_TOOLS] Initializing standalone tools...');
  
  const tools: StandaloneToolsManager = {};
  
  try {
    // Create web search tool (shared across all agents)
    tools.webSearch = await createWebSearchTool(apiKey);
    
    // Create document generation tool (specific to documentation agents)
    tools.documentGeneration = await createDocumentGenerationTool(apiKey);
    
    console.log('[STANDALONE_TOOLS] Successfully initialized all tools');
    return tools;
  } catch (error) {
    console.error('[STANDALONE_TOOLS] Failed to initialize tools:', error);
    throw error;
  }
}

/**
 * Get tools for specific agent type
 */
export function getToolsForAgent(agentName: string, availableTools: StandaloneToolsManager): string[] {
  const toolMappings: Record<string, string[]> = {
    DocumentationGeneration: ['webSearch', 'documentGeneration'],
    Research: ['webSearch'],
    Marketing: ['webSearch'],
    BusinessAnalysis: ['webSearch'],
    InvestigativeResearch: ['webSearch'],
    SoftwareDesign: ['webSearch'],
    Sales: ['webSearch']
  };
  
  const requiredTools = toolMappings[agentName] || [];
  const toolIds: string[] = [];
  
  requiredTools.forEach(toolName => {
    const tool = availableTools[toolName as keyof StandaloneToolsManager];
    if (tool) {
      toolIds.push(tool.id);
    }
  });
  
  return toolIds;
}

/**
 * Cache for standalone tools to avoid recreating
 */
let toolsCache: StandaloneToolsManager | null = null;

export async function getStandaloneTools(apiKey: string, forceRefresh = false): Promise<StandaloneToolsManager> {
  if (!toolsCache || forceRefresh) {
    toolsCache = await initializeStandaloneTools(apiKey);
  }
  return toolsCache;
}
