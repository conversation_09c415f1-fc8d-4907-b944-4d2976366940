import Groq from 'groq-sdk';

async function main() {
  const apiKey = process.env.GROQ_API_KEY ?? '********************************************************';
  const groq = new Groq({ apiKey });
  try {
    const resp = await groq.chat.completions.create({
      model: 'llama-3.3-70b-versatile',
      messages: [
        { role: 'system', content: 'You are a helpful assistant.' },
        { role: 'user', content: 'Smoke test: reply with OK only' }
      ],
      max_tokens: 5,
      temperature: 0
    });
    const out = resp?.choices?.[0]?.message?.content ?? '';
    console.log('GROQ_SMOKE_OK:', out.trim());
    process.exit(0);
  } catch (err) {
    console.error('GROQ_SMOKE_ERROR:', err?.status ?? '', err?.message ?? err);
    process.exit(1);
  }
}
main();

