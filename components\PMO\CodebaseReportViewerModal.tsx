'use client';

import React, { useState, useEffect } from 'react';
import { X, FileText, CheckCircle, AlertTriangle, ChevronLeft, ChevronRight, ChevronDown, ChevronUp, Search, FolderOpen, RefreshCw } from 'lucide-react';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { StoredCodebaseIndexingReport, CodebaseFileAnalysis } from '../../lib/interfaces/CodebaseIndexingReport';
import MarkdownRenderer from '../MarkdownRenderer';
import { db } from '../../app/lib/firebase/config';
import { doc, getDoc } from 'firebase/firestore';

interface CodebaseReportViewerModalProps {
  isOpen: boolean;
  onClose: () => void;
  report: StoredCodebaseIndexingReport | null;
}

export default function CodebaseReportViewerModal({ isOpen, onClose, report }: CodebaseReportViewerModalProps) {
  const [currentPage, setCurrentPage] = useState(1);
  const [filterQuery, setFilterQuery] = useState('');
  const [filteredFiles, setFilteredFiles] = useState<CodebaseFileAnalysis[]>([]);
  const [selectedDirectory, setSelectedDirectory] = useState<string>('all');
  const [directories, setDirectories] = useState<string[]>([]);
  const [expandedFile, setExpandedFile] = useState<string | null>(null);
  const [isSummaryCollapsed, setIsSummaryCollapsed] = useState(false);
  const [loadingDetails, setLoadingDetails] = useState(false);
  const [detailsError, setDetailsError] = useState<string | null>(null);

  // Helper function to format namespace display
  const formatNamespace = (namespace: string): string => {
    // Check if it's a UUID (36 characters with hyphens in specific positions)
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (uuidRegex.test(namespace)) {
      return `${namespace.substring(0, 8)}...${namespace.substring(namespace.length - 8)}`;
    }
    return namespace;
  };

  const ITEMS_PER_PAGE = 10;

  // Populate directories and filtered files when report.fileAnalysis updates
  useEffect(() => {
    if (report?.fileAnalysis && report.fileAnalysis.length > 0) {
      const uniqueDirs = Array.from(new Set(
        report.fileAnalysis.map(file => {
          const pathParts = file.filePath.split('/');
          return pathParts.length > 1 ? pathParts.slice(0, -1).join('/') : '.';
        })
      )).sort();
      setDirectories(uniqueDirs);

      let filtered = report.fileAnalysis;
      if (selectedDirectory !== 'all') {
        filtered = filtered.filter(file => {
          const fileDir = file.filePath.split('/').slice(0, -1).join('/') || '.';
          return fileDir === selectedDirectory;
        });
      }
      if (filterQuery) {
        filtered = filtered.filter(file =>
          file.filePath.toLowerCase().includes(filterQuery.toLowerCase()) ||
          file.fileName.toLowerCase().includes(filterQuery.toLowerCase()) ||
          file.language.toLowerCase().includes(filterQuery.toLowerCase()) ||
          file.llmSummary.toLowerCase().includes(filterQuery.toLowerCase())
        );
      }
      // Deduplicate by filePath to avoid duplicate React keys and repeated entries
      const uniqueFiltered = Array.from(new Map(filtered.map(f => [f.filePath, f])).values());
      setFilteredFiles(uniqueFiltered);
      setCurrentPage(1);
    }
  }, [report?.fileAnalysis, filterQuery, selectedDirectory]);

  // Hybrid detail fetch: if opening a files-based report without fileAnalysis, fetch legacy details once
  useEffect(() => {
    const fetchLegacyDetails = async () => {
      if (!isOpen || !report) return;
      const needsFetch = (!report.fileAnalysis || report.fileAnalysis.length === 0) && !!report.indexingSessionId && !!report.userId;
      if (!needsFetch) return;

      try {
        setLoadingDetails(true);
        setDetailsError(null);
        const legacyRef = doc(db, `users/${report.userId}/codebase-indexing-reports/${report.indexingSessionId}`);
        const snap = await getDoc(legacyRef);
        if (snap.exists()) {
          const data = snap.data() as StoredCodebaseIndexingReport;
          if (Array.isArray(data.fileAnalysis) && data.fileAnalysis.length > 0) {
            // Mutate the report object passed to modal to include details
            report.fileAnalysis = data.fileAnalysis as unknown as CodebaseFileAnalysis[];
            // Trigger re-render by updating filtered state
            setFilteredFiles(data.fileAnalysis);
            setDirectories(Array.from(new Set(data.fileAnalysis.map(f => {
              const parts = f.filePath.split('/');
              return parts.length > 1 ? parts.slice(0, -1).join('/') : '.';
            }))).sort());
            setCurrentPage(1);
          }
        } else {
          setDetailsError('No detailed analysis found in legacy storage.');
        }
      } catch (e) {
        console.error('Failed to fetch legacy file analysis:', e);
        setDetailsError('Failed to load detailed analysis.');
      } finally {
        setLoadingDetails(false);
      }
    };

    fetchLegacyDetails();
  }, [isOpen, report]);

  // Close modal on escape key
  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    window.addEventListener('keydown', handleEscKey);
    return () => window.removeEventListener('keydown', handleEscKey);
  }, [isOpen, onClose]);

  if (!isOpen || !report) return null;

  const hasStructuredFiles = Array.isArray(report.fileAnalysis) && report.fileAnalysis.length > 0;
  const totalPages = hasStructuredFiles ? Math.ceil(filteredFiles.length / ITEMS_PER_PAGE) : 1;
  const paginatedFiles = hasStructuredFiles
    ? filteredFiles.slice((currentPage - 1) * ITEMS_PER_PAGE, currentPage * ITEMS_PER_PAGE)
    : [];
  
  const formatBytes = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const formatTime = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${(ms / 60000).toFixed(1)}m`;
  };

  const formatDate = (timestamp: any): string => {
    if (!timestamp) return 'Unknown date';

    try {
      // Handle different timestamp formats
      const date = timestamp.toDate ? timestamp.toDate() :
                  timestamp.seconds ? new Date(timestamp.seconds * 1000) :
                  timestamp instanceof Date ? timestamp :
                  new Date(timestamp);

      return date.toLocaleString();
    } catch (e) {
      console.error('Error formatting date:', e, timestamp);
      return 'Invalid date';
    }
  };

  const loadFullDetailsForFile = async (filePath: string) => {
    try {
      if (!report?.userId || !report?.indexingSessionId) return;
      const legacyRef = doc(db, `users/${report.userId}/codebase-indexing-reports/${report.indexingSessionId}`);
      const snap = await getDoc(legacyRef);
      if (!snap.exists()) return;
      const data = snap.data() as StoredCodebaseIndexingReport;
      if (!Array.isArray(data.fileAnalysis)) return;
      const legacyFile = data.fileAnalysis.find(f => f.filePath === filePath);
      if (!legacyFile) return;

      // Merge richer legacy details into current report entry for this file
      const updated = (report.fileAnalysis || []).map(f => {
        if (f.filePath !== filePath) return f;
        return {
          ...f,
          llmSummary: legacyFile.llmSummary || f.llmSummary,
          definedEntities: Array.isArray(legacyFile.definedEntities) && legacyFile.definedEntities.length > 0 ? legacyFile.definedEntities : f.definedEntities,
          imports: Array.isArray(legacyFile.imports) && legacyFile.imports.length > 0 ? legacyFile.imports : f.imports,
          exports: Array.isArray(legacyFile.exports) && legacyFile.exports.length > 0 ? legacyFile.exports : f.exports,
          apiEndpoints: Array.isArray(legacyFile.apiEndpoints) && legacyFile.apiEndpoints.length > 0 ? legacyFile.apiEndpoints : f.apiEndpoints,
        } as any;
      });
      report.fileAnalysis = updated as any;
      // Refresh filtered list (preserve filters/dir selection)
      setFilteredFiles(prev => prev.map(f => (f.filePath === filePath ? (updated.find(u => u.filePath === filePath) as any) : f)));
    } catch (e) {
      // Best-effort enrichment; silently ignore
      console.warn('Optional full-details fetch failed for file', filePath, e);
    }
  };

  const toggleFileExpansion = (filePath: string) => {
    const next = expandedFile === filePath ? null : filePath;
    setExpandedFile(next);
    if (next) {
      // Best-effort: try to enrich this file from legacy full analysis if available
      loadFullDetailsForFile(filePath);
    }
  };

  if (!isOpen || !report) return null;

  return (
    <div className="fixed inset-0 bg-black/60 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-900 rounded-lg border border-gray-700 w-full max-w-7xl max-h-[95vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-700">
          <div>
            <h2 className="text-xl font-semibold text-white flex items-center">
              <FileText className="w-5 h-5 mr-2" />
              Codebase Indexing Report
            </h2>
            <p className="text-sm text-gray-400 mt-1">{report.projectName}</p>
            <p className="text-xs text-gray-500">
              Completed: {formatDate(report.completedAt)} • Processing Time: {formatTime(report.processingTimeMs)}
            </p>
          </div>
          <Button variant="ghost" size="icon" onClick={onClose} title="Close">
            <X className="w-6 h-6" />
          </Button>
        </div>

        {/* Collapsible Summary Section */}
        <div className="border-b border-gray-700">
          {/* Toggle Header */}
          <div
            className="flex items-center justify-between p-4 bg-gray-800/30 hover:bg-gray-800/50 cursor-pointer transition-colors"
            onClick={() => setIsSummaryCollapsed(!isSummaryCollapsed)}
          >
            <div className="flex items-center space-x-2">
              <h3 className="text-lg font-semibold text-white">Report Summary & Metadata</h3>
              <span className="text-sm text-gray-400">
                ({report.statistics.totalFiles} files, {report.summary.successfulFiles} successful)
              </span>
            </div>
            <Button variant="ghost" size="sm" className="p-1">
              {isSummaryCollapsed ? (
                <ChevronDown className="w-4 h-4 text-gray-400" />
              ) : (
                <ChevronUp className="w-4 h-4 text-gray-400" />
              )}
            </Button>
          </div>

          {/* Collapsible Content */}
          {!isSummaryCollapsed && (
            <React.Fragment>
              {/* Top-Level Summary Section */}
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4 p-4 bg-gray-800/50">
                <div className="text-center">
                  <p className="text-2xl font-bold text-white">{report.statistics.totalFiles}</p>
                  <p className="text-sm text-gray-400">Total Files</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-white">{report.statistics.totalChunks}</p>
                  <p className="text-sm text-gray-400">Total Chunks</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-white">{formatBytes(report.statistics.totalSize)}</p>
                  <p className="text-sm text-gray-400">Total Size</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-green-400">{report.summary.successfulFiles}</p>
                  <p className="text-sm text-gray-400">Successful</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-red-400">{report.summary.failedFiles}</p>
                  <p className="text-sm text-gray-400">Failed</p>
                </div>
              </div>

              {/* Detailed Metadata Section */}
              <div className="p-4 bg-gray-850">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {/* Report Information */}
                  <div>
                    <h3 className="text-sm font-semibold text-purple-300 mb-3">Report Information</h3>
                    <div className="space-y-2 text-xs text-gray-300">
                      <div><span className="text-gray-400">Report ID:</span> <span className="font-mono text-cyan-400">{report.id}</span></div>
                      <div><span className="text-gray-400">Session ID:</span> <span className="font-mono text-cyan-400">{report.indexingSessionId}</span></div>
                      <div><span className="text-gray-400">User:</span> {report.userId}</div>
                      <div><span className="text-gray-400">Started:</span> {formatDate(report.createdAt)}</div>
                      <div><span className="text-gray-400">Success Rate:</span> <span className="text-green-400">{((report.summary.successfulFiles / report.statistics.totalFiles) * 100).toFixed(1)}%</span></div>
                    </div>
                  </div>

                  {/* Selected Paths Information */}
                  {report.selectedPaths && report.selectedPaths.length > 0 && (
                    <div>
                      <h3 className="text-sm font-semibold text-purple-300 mb-3">Selected Paths</h3>
                      <div className="space-y-2 text-xs text-gray-300">
                        <div><span className="text-gray-400">Total Selected:</span> <span className="text-orange-400">{report.selectedPaths.length}</span></div>
                        <div className="max-h-24 overflow-y-auto bg-gray-800 rounded border border-gray-600 p-2">
                          <div className="space-y-1">
                            {report.selectedPaths.map((selectedPath, index) => {
                              const pathName = selectedPath.split(/[/\\]/).pop() || selectedPath;
                              return (
                                <div
                                  key={index}
                                  className="text-xs text-gray-300 bg-gray-700 px-2 py-1 rounded truncate"
                                  title={selectedPath} // Show full path on hover
                                >
                                  {pathName}
                                </div>
                              );
                            })}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Vector Embedding Details */}
                  <div>
                    <h3 className="text-sm font-semibold text-purple-300 mb-3">Vector Embedding</h3>
                    <div className="space-y-2 text-xs text-gray-300">
                      <div>
                        <span className="text-gray-400">Namespace:</span>
                        <span className="font-mono text-xs ml-1" title={report.vectorEmbedding.pineconeNamespace}>
                          {formatNamespace(report.vectorEmbedding.pineconeNamespace)}
                        </span>
                      </div>
                      <div><span className="text-gray-400">Model:</span> {report.vectorEmbedding.embeddingModel}</div>
                      <div><span className="text-gray-400">Dimensions:</span> {report.vectorEmbedding.embeddingDimensions}</div>
                      <div><span className="text-gray-400">Total Embeddings:</span> {report.vectorEmbedding.totalEmbeddings}</div>
                      <div><span className="text-gray-400">Status:</span>
                        <span className={report.vectorEmbedding.indexingComplete ? "text-green-400" : "text-red-400"}>
                          {report.vectorEmbedding.indexingComplete ? ' ✅ Complete' : ' ❌ Failed'}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Performance Metrics */}
                  <div>
                    <h3 className="text-sm font-semibold text-purple-300 mb-3">Performance Metrics</h3>
                    <div className="space-y-2 text-xs text-gray-300">
                      <div><span className="text-gray-400">Avg Chunk Size:</span> {formatBytes(report.statistics.averageChunkSize)}</div>
                      <div><span className="text-gray-400">Time per File:</span> {formatTime(report.statistics.processingTimePerFile)}</div>
                      <div><span className="text-gray-400">Processing Rate:</span> {(report.statistics.totalFiles / (report.processingTimeMs / 1000)).toFixed(2)} files/sec</div>
                      <div><span className="text-gray-400">Skipped Files:</span> {report.summary.skippedFiles}</div>
                      <div><span className="text-gray-400">Storage:</span>
                        <span className={report.firebaseStorage.storageComplete ? "text-green-400" : "text-red-400"}>
                          {report.firebaseStorage.storageComplete ? ' ✅ Complete' : ' ❌ Failed'}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Warnings and Errors Summary */}
                {(report.summary.warnings.length > 0 || report.summary.errorMessages.length > 0) && (
                  <div className="mt-4 pt-4 border-t border-gray-700">
                    <h3 className="text-sm font-semibold text-yellow-300 mb-3">Issues Summary</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {report.summary.warnings.length > 0 && (
                        <div>
                          <h4 className="text-xs font-medium text-yellow-400 mb-2">Warnings ({report.summary.warnings.length})</h4>
                          <div className="space-y-1 max-h-20 overflow-y-auto">
                            {report.summary.warnings.slice(0, 3).map((warning, index) => (
                              <div key={index} className="text-xs text-yellow-300 bg-yellow-900/20 p-2 rounded">
                                ⚠️ {warning}
                              </div>
                            ))}
                            {report.summary.warnings.length > 3 && (
                              <div className="text-xs text-gray-400">... and {report.summary.warnings.length - 3} more warnings</div>
                            )}
                          </div>
                        </div>
                      )}
                      {report.summary.errorMessages.length > 0 && (
                        <div>
                          <h4 className="text-xs font-medium text-red-400 mb-2">Errors ({report.summary.errorMessages.length})</h4>
                          <div className="space-y-1 max-h-20 overflow-y-auto">
                            {report.summary.errorMessages.slice(0, 3).map((error, index) => (
                              <div key={index} className="text-xs text-red-300 bg-red-900/20 p-2 rounded">
                                ❌ {error}
                              </div>
                            ))}
                            {report.summary.errorMessages.length > 3 && (
                              <div className="text-xs text-gray-400">... and {report.summary.errorMessages.length - 3} more errors</div>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </React.Fragment>
          )}
        </div>

        {/* Controls: Filter, Directory Selection, and Pagination */}
        {hasStructuredFiles && (
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 p-4 border-b border-gray-700">
            <div className="flex flex-col sm:flex-row gap-2 flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
                <Input
                  type="text"
                  placeholder="Search files, paths, or content..."
                  value={filterQuery}
                  onChange={(e) => setFilterQuery(e.target.value)}
                  className="pl-9 bg-gray-800 border-gray-600 w-64"
                />
              </div>
              <div className="relative">
                <FolderOpen className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
                <select
                  value={selectedDirectory}
                  onChange={(e) => setSelectedDirectory(e.target.value)}
                  className="pl-9 pr-4 py-2 bg-gray-800 border border-gray-600 rounded-md text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">All Directories ({report.fileAnalysis.length})</option>
                  {directories.map(dir => {
                    const fileCount = report.fileAnalysis.filter(file => {
                      const fileDir = file.filePath.split('/').slice(0, -1).join('/') || '.';
                      return fileDir === dir;
                    }).length;
                    return (
                      <option key={dir} value={dir}>
                        {dir === '.' ? 'Root' : dir} ({fileCount})
                      </option>
                    );
                  })}
                </select>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-400">
                {filteredFiles.length} files • Page {currentPage} of {totalPages}
              </span>
              <Button
                variant="outline"
                size="icon"
                onClick={() => setCurrentPage(p => Math.max(1, p - 1))}
                disabled={currentPage === 1}
                title="Previous page"
              >
                <ChevronLeft className="w-4 h-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={() => setCurrentPage(p => Math.min(totalPages, p + 1))}
                disabled={currentPage === totalPages}
                title="Next page"
              >
                <ChevronRight className="w-4 h-4" />
              </Button>
            </div>
          </div>
        )}

        {/* Content: Paginated File List or Legacy Fetch Fallback */}
        <div className="flex-1 overflow-y-auto">
          {!hasStructuredFiles ? (
            <div className="p-6">
              <div className="text-center py-10 text-gray-500">
                {loadingDetails ? (
                  <>
                    <RefreshCw className="w-6 h-6 animate-spin text-blue-400 mx-auto mb-3" />
                    <p>Loading detailed analysis...</p>
                  </>
                ) : detailsError ? (
                  <>
                    <FileText className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p>{detailsError}</p>
                    {report.firebaseStorage?.reportPdfUrl && (
                      <p className="text-sm mt-3">
                        View the full PDF report here:
                        <a
                          href={report.firebaseStorage.reportPdfUrl}
                          target="_blank"
                          rel="noreferrer"
                          className="ml-2 text-blue-400 hover:underline"
                        >
                          Open PDF
                        </a>
                      </p>
                    )}
                  </>
                ) : (
                  <>
                    <FileText className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p>No structured file analysis available.</p>
                    {report.firebaseStorage?.reportPdfUrl && (
                      <p className="text-sm mt-3">
                        View the full PDF report here:
                        <a
                          href={report.firebaseStorage.reportPdfUrl}
                          target="_blank"
                          rel="noreferrer"
                          className="ml-2 text-blue-400 hover:underline"
                        >
                          Open PDF
                        </a>
                      </p>
                    )}
                  </>
                )}
              </div>
            </div>
          ) : (
            <div className="p-4 space-y-3">
              {paginatedFiles.map((file) => (
                <div key={file.filePath} className="bg-gray-800 rounded-lg border border-gray-700">
                  <div
                    className="p-4 cursor-pointer hover:bg-gray-750 transition-colors"
                    onClick={() => toggleFileExpansion(file.filePath)}
                  >
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="font-mono text-sm text-cyan-400 mb-1">{file.filePath}</div>
                        <p className="text-sm text-gray-300 italic line-clamp-2">"{file.llmSummary}"</p>
                      </div>
                      <div className="flex items-center space-x-2 ml-4">
                        {file.success ? (
                          <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        ) : (
                          <AlertTriangle className="w-5 h-5 text-red-500 flex-shrink-0" />
                        )}
                      </div>
                    </div>
                    <div className="text-xs text-gray-400 mt-3 grid grid-cols-2 md:grid-cols-5 gap-2">
                      <span><strong className="font-semibold text-gray-300">Language:</strong> {file.language}</span>
                      <span><strong className="font-semibold text-gray-300">Size:</strong> {formatBytes(file.fileSize)}</span>
                      <span><strong className="font-semibold text-gray-300">Chunks:</strong> {file.chunkCount}</span>
                      <span><strong className="font-semibold text-gray-300">Entity:</strong> {file.codeEntityType}</span>
                      <span><strong className="font-semibold text-gray-300">Time:</strong> {formatTime(file.processingTimeMs)}</span>
                    </div>
                  </div>

                  {/* Expanded Details */}
                  {expandedFile === file.filePath && (
                    <div className="border-t border-gray-700 p-4 bg-gray-850">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <h4 className="text-sm font-semibold text-purple-300 mb-2">Code Intelligence</h4>
                          <div className="space-y-1 text-xs text-gray-300">
                            <div><strong>Defined Entities:</strong> {file.definedEntities.length > 0 ? file.definedEntities.join(', ') : 'None'}</div>
                            <div><strong>Imports:</strong> {file.imports.length > 0 ? file.imports.slice(0, 3).join(', ') + (file.imports.length > 3 ? '...' : '') : 'None'}</div>
                            <div><strong>Exports:</strong> {file.exports.length > 0 ? file.exports.slice(0, 3).join(', ') + (file.exports.length > 3 ? '...' : '') : 'None'}</div>
                            <div><strong>API Endpoints:</strong> {file.apiEndpoints.length > 0 ? file.apiEndpoints.join(', ') : 'None'}</div>
                          </div>
                        </div>
                        <div>
                          <h4 className="text-sm font-semibold text-purple-300 mb-2">LLM Analysis</h4>
                          <div className="text-xs text-gray-300 bg-gray-900 p-3 rounded border max-h-32 overflow-y-auto">
                            <MarkdownRenderer content={file.llmSummary} />
                          </div>
                        </div>
                      </div>
                      {file.errorMessage && (
                        <div className="mt-3 p-3 bg-red-900/20 border border-red-700 rounded">
                          <p className="text-xs text-red-400"><strong>Error:</strong> {file.errorMessage}</p>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ))}
              {filteredFiles.length === 0 && (
                <div className="text-center py-10 text-gray-500">
                  <FileText className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p>No files match your current filters.</p>
                  <p className="text-sm mt-1">Try adjusting your search or directory selection.</p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
