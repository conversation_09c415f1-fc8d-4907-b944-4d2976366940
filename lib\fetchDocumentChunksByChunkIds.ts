// fetchDocumentChunksByChunkIds.ts

import { FirestoreStore } from "lib/FirestoreStore";
import { adminDb } from "components/firebase-admin";
import { FieldPath } from "firebase-admin/firestore";
import { Document } from "langchain/document";

/**
 * Fetch chunks by explicit chunk IDs (e.g., <docId>_1, <docId>_2, ...)
 */
export async function fetchDocumentChunksByChunkIds(
  chunkIds: string[],
  firestoreStore: FirestoreStore
): Promise<Document[]> {
  const allChunks: Document[] = [];

  const chunkFetchPromises = chunkIds.map(async (chunkId) => {
    try {
      const chunk = await firestoreStore.mget([chunkId]);
      if (chunk && chunk.length > 0 && chunk[0] !== undefined) {
        allChunks.push(chunk[0]);
      } else {
        console.warn(`Chunk not found for ID: ${chunkId}`);
      }
    } catch (error) {
      console.error(`Error fetching chunk for ID: ${chunkId}`, error);
    }
  });

  await Promise.all(chunkFetchPromises);
  return allChunks;
}

/**
 * Fetch chunks by base document IDs (doc_id). Each docId may map to several chunk docs
 * stored in users/{userId}/byteStoreCollection with metadata.doc_id == docId.
 * Returns Documents in chunk order (ascending by trailing index in chunk_id when available).
 */
export async function fetchDocumentChunksByDocIds(
  docIds: string[],
  firestoreStore: FirestoreStore
): Promise<Document[]> {
  const results: Document[] = [];

  for (const docId of docIds) {
    try {
      const snap = await adminDb
        .collection(firestoreStore.collectionPath)
        .where("metadata.doc_id", "==", docId)
        .get();

      if (snap.empty) {
        // Fallback: attempt prefix scan by documentId (docId_)
        // Note: Firestore requires range queries on documentId; emulate prefix
        const prefix = `${docId}_`;
        const prefixSnap = await adminDb
          .collection(firestoreStore.collectionPath)
          .where(FieldPath.documentId(), ">=", prefix)
          .where(FieldPath.documentId(), "<", prefix + "\uf8ff")
          .get();

        const docs = prefixSnap.docs;
        const mapped = docs.map((d) => {
          const data = d.data();
          return new Document({
            pageContent: (data as any)?.content || "",
            metadata: { ...(data as any)?.metadata, chunk_id: d.id },
          });
        });

        // Sort by numeric suffix if present
        mapped.sort((a, b) => {
          const ai = Number(String((a.metadata as any)?.chunk_id || "").split("_").pop());
          const bi = Number(String((b.metadata as any)?.chunk_id || "").split("_").pop());
          return (isNaN(ai) ? 0 : ai) - (isNaN(bi) ? 0 : bi);
        });

        results.push(...mapped);
        continue;
      }

      const mapped = snap.docs.map((d) => {
        const data = d.data();
        return new Document({
          pageContent: (data as any)?.content || "",
          metadata: { ...(data as any)?.metadata, chunk_id: d.id },
        });
      });

      // Order chunks by numeric suffix if present
      mapped.sort((a, b) => {
        const ai = Number(String((a.metadata as any)?.chunk_id || "").split("_").pop());
        const bi = Number(String((b.metadata as any)?.chunk_id || "").split("_").pop());
        return (isNaN(ai) ? 0 : ai) - (isNaN(bi) ? 0 : bi);
      });

      results.push(...mapped);
    } catch (error) {
      console.error(`Error fetching chunks for doc_id ${docId}:`, error);
    }
  }

  return results;
}
