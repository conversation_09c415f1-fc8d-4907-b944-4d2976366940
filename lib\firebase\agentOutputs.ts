/**
 * Agent Outputs Collection
 *
 * This file contains functions for interacting with the Agent_Output collection in Firebase.
 * The collection stores outputs from various agents, including documents, reports, and assessments.
 */

import { db } from '../../components/firebase';
import { collection, doc, addDoc, setDoc, getDoc, getDocs, updateDoc, deleteDoc, query, where, orderBy, limit } from 'firebase/firestore';
import { v4 as uuidv4 } from 'uuid';

/**
 * Agent Output interface
 */
export interface AgentOutput {
  id: string;
  userId: string;
  agentType: string; // 'PMO', 'Marketing', 'Research', etc.
  title: string;
  content: string;
  fileUrl?: string;
  createdAt: Date;
  updatedAt?: Date;
  metadata?: Record<string, any>;
  // Additional fields from global Agent_Output collection
  pmoMetadata?: Record<string, any>;
  category?: string;
  contextOptions?: Record<string, any>;
}

/**
 * Agent Output Input interface
 */
export interface AgentOutputInput {
  userId: string;
  agentType: string;
  title: string;
  content: string;
  fileUrl?: string;
  createdAt: Date;
  metadata?: Record<string, any>;
  category?: string; // Document category for lineage tracking
}

/**
 * Add a new agent output
 * @param input - The agent output input
 * @returns - The agent output ID
 */
export async function addAgentOutput(input: AgentOutputInput): Promise<{ id: string }> {
  try {
    // Generate a UUIDv4 for the agent output ID
    const agentOutputId = uuidv4();

    // Create a reference to the Agent_Output collection with the UUIDv4 as document ID
    const agentOutputsRef = collection(db, 'users', input.userId, 'Agent_Output');
    const docRef = doc(agentOutputsRef, agentOutputId);

    // Set the document with the UUIDv4 ID
    await setDoc(docRef, {
      id: agentOutputId, // Store the UUIDv4 as the id field
      agentType: input.agentType,
      title: input.title,
      content: input.content,
      fileUrl: input.fileUrl || null,
      createdAt: input.createdAt,
      updatedAt: input.createdAt,
      metadata: input.metadata || {},
      category: input.category || null // Store the category for lineage tracking
    });

    return { id: agentOutputId };
  } catch (error) {
    console.error('Error adding agent output:', error);
    throw error;
  }
}

/**
 * Get an agent output by ID
 * @param userId - The user ID
 * @param outputId - The agent output ID
 * @returns - The agent output
 */
export async function getAgentOutput(userId: string, outputId: string): Promise<AgentOutput | null> {
  try {
    // Create a reference to the agent output document
    const outputRef = doc(db, 'users', userId, 'Agent_Output', outputId);

    // Get the document
    const outputDoc = await getDoc(outputRef);

    // Check if the document exists
    if (!outputDoc.exists()) {
      return null;
    }

    // Get the document data
    const outputData = outputDoc.data();

    // Convert Firestore timestamps to Date objects
    const createdAt = outputData.createdAt?.toDate() || new Date();
    const updatedAt = outputData.updatedAt?.toDate() || createdAt;

    // Return the agent output
    return {
      id: outputDoc.id,
      userId,
      agentType: outputData.agentType || '',
      title: outputData.title || '',
      content: outputData.content || '',
      fileUrl: outputData.fileUrl || undefined,
      createdAt,
      updatedAt,
      metadata: outputData.metadata || {}
    };
  } catch (error) {
    console.error('Error getting agent output:', error);
    throw error;
  }
}

/**
 * Get all agent outputs for a user
 * @param userId - The user ID
 * @param agentType - Optional agent type filter
 * @returns - The agent outputs
 */
export async function getAgentOutputs(userId: string, agentType?: string): Promise<AgentOutput[]> {
  try {
    // Create a reference to the Agent_Output collection
    const agentOutputsRef = collection(db, 'users', userId, 'Agent_Output');

    // Create a query
    let q = query(agentOutputsRef, orderBy('createdAt', 'desc'));

    // Add agent type filter if provided
    if (agentType) {
      q = query(q, where('agentType', '==', agentType));
    }

    // Get the documents
    const outputDocs = await getDocs(q);

    // Map the documents to agent outputs
    return outputDocs.docs.map(doc => {
      const data = doc.data();

      // Convert Firestore timestamps to Date objects
      const createdAt = data.createdAt?.toDate() || new Date();
      const updatedAt = data.updatedAt?.toDate() || createdAt;

      return {
        id: doc.id,
        userId,
        agentType: data.agentType || '',
        title: data.title || '',
        content: data.content || '',
        fileUrl: data.fileUrl || undefined,
        createdAt,
        updatedAt,
        metadata: data.metadata || {}
      };
    });
  } catch (error) {
    console.error('Error getting agent outputs:', error);
    throw error;
  }
}

/**
 * Update an agent output
 * @param userId - The user ID
 * @param outputId - The agent output ID
 * @param updates - The updates to apply
 * @returns - Success status
 */
export async function updateAgentOutput(
  userId: string,
  outputId: string,
  updates: Partial<Omit<AgentOutput, 'id' | 'userId' | 'createdAt'>>
): Promise<{ success: boolean }> {
  try {
    // Create a reference to the agent output document
    const outputRef = doc(db, 'users', userId, 'Agent_Output', outputId);

    // Update the document
    await updateDoc(outputRef, {
      ...updates,
      updatedAt: new Date()
    });

    return { success: true };
  } catch (error) {
    console.error('Error updating agent output:', error);
    throw error;
  }
}

/**
 * Delete an agent output
 * @param userId - The user ID
 * @param outputId - The agent output ID
 * @returns - Success status
 */
export async function deleteAgentOutput(userId: string, outputId: string): Promise<{ success: boolean }> {
  try {
    // Create a reference to the agent output document
    const outputRef = doc(db, 'users', userId, 'Agent_Output', outputId);

    // Delete the document
    await deleteDoc(outputRef);

    return { success: true };
  } catch (error) {
    console.error('Error deleting agent output:', error);
    throw error;
  }
}

/**
 * Get agent outputs from the global Agent_Output collection (for PMO-related outputs)
 * @param userId - The user ID to filter by
 * @param agentType - Optional agent type filter
 * @returns - The agent outputs
 */
export async function getGlobalAgentOutputs(userId: string, agentType?: string): Promise<AgentOutput[]> {
  try {
    // Use API endpoint to fetch global agent outputs
    const params = new URLSearchParams();
    params.append('userId', userId);
    if (agentType) {
      params.append('agentType', agentType);
    }

    const response = await fetch(`/api/agent-outputs/global?${params.toString()}`);

    if (!response.ok) {
      console.warn('Failed to fetch global agent outputs, falling back to user-specific collection');
      return getAgentOutputs(userId, agentType);
    }

    const data = await response.json();

    if (!data.success) {
      console.warn('Global agent outputs API returned error, falling back to user-specific collection');
      return getAgentOutputs(userId, agentType);
    }

    // Map the API response to agent outputs
    return data.outputs.map((output: any) => ({
      id: output.id,
      userId,
      agentType: output.agentType || '',
      title: output.title || 'Agent Output',
      content: output.content || '',
      fileUrl: output.fileUrl || undefined,
      createdAt: new Date(output.createdAt),
      updatedAt: new Date(output.updatedAt || output.createdAt),
      metadata: output.metadata || {},
      // Include additional fields from global collection
      pmoMetadata: output.pmoMetadata || undefined,
      category: output.category || undefined,
      contextOptions: output.contextOptions || undefined
    }));
  } catch (error) {
    console.error('Error getting global agent outputs:', error);
    // Fallback to user-specific collection
    return getAgentOutputs(userId, agentType);
  }
}
