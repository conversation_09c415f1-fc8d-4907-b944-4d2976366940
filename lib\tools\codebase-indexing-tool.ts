/**
 * Codebase Indexing Tool with Adaptive Context Analysis
 *
 * This tool intelligently analyzes codebases by using adaptive truncation strategies
 * to preserve important context while maintaining efficiency. Key features:
 *
 * - High-quality, file-level LLM analysis for accurate summaries and metadata.
 * - Raw code chunking for clean, searchable vector embeddings.
 * - Adaptive context limits based on file type (1200-2500 characters)
 * - Smart truncation at natural breakpoints (imports, comments, statements)
 * - Fallback to path-based analysis for robust classification
 * - Cost-optimized LLM usage with Gemini 2.5 Flash
 */

import { StorageTool } from './storage-tool';
import { vectorEmbeddingTool } from './vector-embeddings';
import * as fs from 'fs/promises';
import { Dirent } from 'fs';
import path from 'path';
import { RecursiveCharacterTextSplitter } from 'langchain/text_splitter';
import { v4 as uuidv4 } from 'uuid';
import { processWithGoogleAI } from './google-ai';
import { codebaseIndexingReportGenerator } from './codebase-indexing-report-generator';
import { CodebaseFileAnalysis } from '../interfaces/CodebaseIndexingReport';

export interface CodebaseIndexingOptions {
  rootPath: string;
  userId: string;
  projectName: string;
  selectedPaths?: string[]; // Specific paths selected by user for targeted analysis
  excludePatterns?: string[];
  includeExtensions?: string[];
  chunkSize?: number;
  chunkOverlap?: number;
  verbose?: boolean; // Added for optional detailed logging
  maxFiles?: number; // Maximum number of files to process (for legacy method)
  category?: string; // Document title for category naming convention
  onProgress?: (u: { step: string; message: string; percent?: number; batchIndex?: number; totalBatches?: number }) => void; // live progress
}

export interface CodebaseIndexingResult {
  success: boolean;
  totalFiles: number;
  totalChunks: number;
  documentId: string;
  chunkIds?: string[];
  reportPdfUrl?: string; // URL to generated indexing report PDF
  error?: string;
}

// Define enriched metadata interface for code chunks
export interface EnrichedCodeChunk {
  content: string; // This is now PURE code, no headers.
  metadata: {
    title: string;       // fileName
    filePath: string;
    language: string;
    chunkIndex: number;
    projectName: string;
    type: 'code_chunk';
    indexedAt: string;

    // --- STANDARDIZED CHUNK IDENTIFICATION ---
    doc_id: string;      // Document ID for the codebase session
    chunk_id: string;    // Standardized format: ${docId}_${index + 1}

    // --- ENRICHED METADATA (now based on full-file analysis) ---
    codeSummary: string;
    codeEntityType: 'Component' | 'Function' | 'Class' | 'Hook' | 'Configuration' | 'Util' | 'Unknown';
    definedEntities: string[];
    usedLibraries: string[];
    usedComponentsOrHooks: string[];
    imports: string[];
    exports: string[];
    apiEndpoints: string[];
    fileId: string; // UUID for the file (used as namespace)
  }
}

interface FileAnalysisResult {
  summary: string;
  entityType: 'Component' | 'Function' | 'Class' | 'Hook' | 'Configuration' | 'Util' | 'Unknown';
  definedEntities: string[];
  usedLibraries: string[];
  usedComponentsOrHooks: string[];
  imports: string[];
  exports: string[];
  apiEndpoints: string[];
}

export class CodebaseIndexingTool {
  private storageTool: StorageTool;
  private applicationContext: string | null = null; // Cache for application context
  private costTrackingData: { originalSize: number, truncatedSize: number }[] = []; // Track adaptive truncation savings
  private defaultExcludePatterns = [
    'node_modules',
    '.git',
    '.next',
    'dist',
    'build',
    '.vscode',
    'coverage',
    '.nuxt',
    '.output',
    '__pycache__',
    '.env',
    '.env.local',
    '.env.production',
    '.env.development',
    'package-lock.json',
    'yarn.lock',
    'pnpm-lock.yaml',
    '.DS_Store',
    'Thumbs.db',
    '.cache',
    'tmp',
    'temp'
  ];

  private defaultIncludeExtensions = [
    '.ts', '.tsx', '.js', '.jsx',
    '.py', '.java', '.cpp', '.c',
    '.cs', '.go', '.rs', '.php',
    '.rb', '.swift', '.kt', '.scala',
    '.md', '.txt', '.json', '.yaml', '.yml'
  ];

  // Exposed for orchestration streaming
  public _lastProgress?: { step: string; message: string; percent?: number; batchIndex?: number; totalBatches?: number };

  constructor() {
    this.storageTool = new StorageTool();
  }

  // Helpers for storage paths and content types
  private sanitizePathSegment(input: string): string {
    return (input || 'project')
      .replace(/[^a-zA-Z0-9-_]+/g, '_')
      .replace(/_+/g, '_')
      .replace(/^_+|_+$/g, '');
  }

  private getContentTypeFromExtension(ext: string): string {
    const map: Record<string, string> = {
      '.ts': 'text/plain', '.tsx': 'text/plain', '.js': 'text/plain', '.jsx': 'text/plain',
      '.json': 'application/json', '.md': 'text/markdown', '.txt': 'text/plain',
      '.py': 'text/x-python', '.java': 'text/x-java-source', '.c': 'text/x-c', '.cpp': 'text/x-c++', '.cs': 'text/plain',
      '.go': 'text/plain', '.rs': 'text/plain', '.php': 'text/plain', '.rb': 'text/plain', '.swift': 'text/plain', '.kt': 'text/plain', '.scala': 'text/plain',
      '.yml': 'text/yaml', '.yaml': 'text/yaml'
    };
    return map[ext.toLowerCase()] || 'text/plain';
  }


  /**
   * Index codebase directly to vector embeddings with enriched metadata
   * This is the new enhanced version for Path A: Codebase Onboarding & Enrichment
   */
  async indexCodebaseDirect(options: CodebaseIndexingOptions): Promise<CodebaseIndexingResult> {
    const processingStartTime = Date.now();
    const verbose = options.verbose ?? false; // Use verbose flag

    try {
      this.costTrackingData = [];
      console.log(`🚀 Starting enriched codebase indexing for ${options.projectName}`);

      console.log(`🧠 Analyzing application context...`);
      await this.analyzeApplicationContext(options.rootPath, options.selectedPaths);

      const excludePatterns = options.excludePatterns || this.defaultExcludePatterns;
      const includeExtensions = options.includeExtensions || this.defaultIncludeExtensions;

      // Generate a run-level indexing session id (not used for Pinecone namespaces)
      const indexingSessionId = uuidv4();
      const baseCategory = (options.category || options.projectName || 'Codebase Documentation').trim();

      const files = await this.findCodeFiles(
        options.rootPath,
        excludePatterns,
        includeExtensions,
        verbose,
        options.selectedPaths
      );

      console.log(`📁 Found ${files.length} code files to index for enrichment`);

      if (files.length === 0) {
        return {
          success: false,
          totalFiles: 0,
          totalChunks: 0,
          documentId: '',
          error: 'No files found to index'
        };
      }

      if (!vectorEmbeddingTool.isInitialized()) {
        await vectorEmbeddingTool.initialize();
      }

      const fileAnalysisData: CodebaseFileAnalysis[] = [];
      let totalChunksCount = 0;

      for (let i = 0; i < files.length; i++) {
        const filePath = files[i];
        const fileProcessingStart = Date.now();
        const relativePath = path.relative(options.rootPath, filePath);

        try {
          const fileId = uuidv4();
          const progressPercent = ((i + 1) / files.length * 100).toFixed(1);
          console.log(`🧠 Processing file ${i + 1}/${files.length} (${progressPercent}%): ${relativePath}`);
          console.log(`🔍 Debug - rootPath: "${options.rootPath}", filePath: "${filePath}", relativePath: "${relativePath}"`);

          const fileStats = await fs.stat(filePath);
          const fileName = path.basename(filePath);
          const language = this.getLanguageFromExtension(path.extname(filePath));

          // Upload original source file to Firebase Storage for downloadUrl
          let fileDownloadUrl: string | undefined = undefined;
          try {
            const relDir = path.dirname(relativePath).replace(/\\/g, '/');
            const storagePath = `code-files/${options.userId}/${this.sanitizePathSegment(options.projectName)}${relDir && relDir !== '.' ? `/${relDir}` : ''}`;
            const fileBuffer = await fs.readFile(filePath);
            fileDownloadUrl = await this.storageTool.saveToStorage(
              fileBuffer,
              storagePath,
              fileName,
              {
                contentType: this.getContentTypeFromExtension(path.extname(filePath)),
                customMetadata: {
                  type: 'application/code',
                  language,
                  indexingSessionId,
                  projectName: options.projectName,
                  relativePath: relativePath.replace(/\\/g, '/'),
                },
              } as any
            );
          } catch (uploadErr) {
            console.warn(`⚠️ Failed to upload source file to Storage: ${relativePath}` , uploadErr);
          }

          const { enrichedChunks, analysis } = await this.processAndEnrichFile(filePath, options, fileId);

          // Build per-file chunk ids, contents, and metadata
          const chunkIdsForFile: string[] = [];
          const contentsForFile: string[] = [];
          const metasForFile: any[] = [];
          for (let idx = 0; idx < enrichedChunks.length; idx++) {
            const ch = enrichedChunks[idx];
            const cid = `${fileId}_${idx + 1}`;
            chunkIdsForFile.push(cid);
            contentsForFile.push(ch.content);
            const updatedMeta = {
              ...ch.metadata,
              // Standardize filename field for byteStoreCollection schema
              // Ensure document_title is always present (preferred over legacy 'title')
              document_title: ch.metadata.title || (ch as any).metadata?.document_title || path.basename(ch.metadata.filePath || ''),
              doc_id: fileId,
              chunk_id: cid,
              category: baseCategory,
              indexingSessionId
            };
            metasForFile.push(this.sanitizeMetadataForPinecone(updatedMeta));
          }

          // Persist chunks to byteStore first (if any)
          if (chunkIdsForFile.length > 0) {
            await this.storageTool.persistCodeChunksToByteStore(
              options.userId,
              chunkIdsForFile,
              contentsForFile,
              metasForFile,
              options.onProgress
            );

            // Create embeddings per chunk under namespace = fileId
            for (let e = 0; e < contentsForFile.length; e++) {
              await vectorEmbeddingTool.createEmbedding(
                contentsForFile[e],
                chunkIdsForFile[e],
                metasForFile[e],
                fileId
              );
            }
          }

          // Accumulate analysis and stats
          if (enrichedChunks.length > 0) {
            const fileAnalysis: CodebaseFileAnalysis = {
              filePath: relativePath,
              fileName,
              language: this.getLanguageFromExtension(path.extname(filePath)),
              fileSize: fileStats.size,
              chunkCount: enrichedChunks.length,
              llmSummary: analysis.summary,
              codeEntityType: analysis.entityType,
              definedEntities: analysis.definedEntities,
              usedLibraries: analysis.usedLibraries,
              usedComponentsOrHooks: analysis.usedComponentsOrHooks,
              imports: analysis.imports,
              exports: analysis.exports,
              apiEndpoints: analysis.apiEndpoints,
              processingTimeMs: Date.now() - fileProcessingStart,
              success: true,
              chunks: enrichedChunks.map((chunk, index) => ({
                chunkId: `${fileId}_${index + 1}`,
                chunkIndex: index,
                contentPreview: chunk.content.substring(0, 200),
                summary: chunk.metadata.codeSummary
              }))
            };
            fileAnalysisData.push(fileAnalysis);
          }

          // Write the per-file Firestore document (full LLM summary)
          await this.storageTool.writeCodeFileDoc(options.userId, fileId, {
            type: 'application/code',
            category: baseCategory,
            indexingSessionId,
            namespace: fileId,
            filePath: relativePath.replace(/\\/g, '/'),
            name: fileName,
            language,
            fileSize: fileStats.size,
            chunkCount: enrichedChunks.length,
            processingTimeMs: Date.now() - fileProcessingStart,
            success: enrichedChunks.length > 0,
            llmSummary: analysis.summary,
            definedEntities: analysis.definedEntities,
            usedLibraries: analysis.usedLibraries,
            usedComponentsOrHooks: analysis.usedComponentsOrHooks,
            imports: analysis.imports,
            exports: analysis.exports,
            apiEndpoints: analysis.apiEndpoints,
            // Storage metadata for direct download
            downloadUrl: fileDownloadUrl,
            ref: `code-files/${options.userId}/${this.sanitizePathSegment(options.projectName)}/${relativePath.replace(/\\/g, '/')}`
          }, options.onProgress);

          // Track total chunk count
          totalChunksCount += enrichedChunks.length;
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          console.warn(`⚠️ Failed to process file ${relativePath}: ${errorMessage}`);
          const fileName = path.basename(filePath);
          let fileSize = 0;
          try {
            const stats = await fs.stat(filePath);
            fileSize = stats.size;
          } catch (statError) {}

          const fileAnalysis: CodebaseFileAnalysis = {
            filePath: relativePath,
            fileName,
            language: 'Unknown',
            fileSize,
            chunkCount: 0,
            llmSummary: 'Failed to process',
            codeEntityType: 'Unknown',
            definedEntities: [],
            usedLibraries: [],
            usedComponentsOrHooks: [],
            imports: [],
            exports: [],
            apiEndpoints: [],
            processingTimeMs: Date.now() - fileProcessingStart,
            success: false,
            errorMessage,
            chunks: []
          };
          fileAnalysisData.push(fileAnalysis);
        }
      }

      console.log(`✅ Successfully indexed and enriched codebase directly!`);
      console.log(`📊 Total files: ${files.length}`);
      console.log(`📄 Total chunks: ${totalChunksCount}`);

      try {
        console.log(`📋 Generating codebase indexing completion report...`);
        const indexingResult: CodebaseIndexingResult = {
          success: true,
          totalFiles: files.length,
          totalChunks: totalChunksCount,
          documentId: indexingSessionId,
        };
        const report = await codebaseIndexingReportGenerator.generateReport(
          indexingResult,
          options.projectName,
          options.userId,
          fileAnalysisData,
          processingStartTime,
          {},
          options.selectedPaths,
          baseCategory
        );
        const reportPdfUrl = await codebaseIndexingReportGenerator.saveReport(
          report,
          (u) => {
            (this as any)._lastProgress = u;
            // If caller provided live progress handler (i.e., orchestrator), invoke it
            try { options.onProgress && options.onProgress(u); } catch {}
            console.log(`[PROGRESS->TOOL] ${u.step} ${u.batchIndex ? `${u.batchIndex}/${u.totalBatches}` : ''} - ${u.message}`);
          }
        );
        console.log(`📄 Indexing completion report saved: ${reportPdfUrl}`);
        return { ...indexingResult, reportPdfUrl };
      } catch (reportError) {
        console.warn(`⚠️ Failed to generate completion report:`, reportError);
        return {
          success: true,
          totalFiles: files.length,
          totalChunks: totalChunksCount,
          documentId: indexingSessionId,
        };
      }
    } catch (error) {
      console.error('❌ Error in direct codebase indexing:', error);
      return {
        success: false, totalFiles: 0, totalChunks: 0, documentId: '',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }



  async indexCodebase(options: CodebaseIndexingOptions): Promise<CodebaseIndexingResult> {
    return this.indexCodebaseDirect(options);
  }

  async indexCodebaseLegacy(options: CodebaseIndexingOptions): Promise<CodebaseIndexingResult> {
    const verbose = options.verbose ?? false;
    try {
      console.log(`Starting codebase indexing for ${options.projectName}`);
      const excludePatterns = options.excludePatterns || this.defaultExcludePatterns;
      const includeExtensions = options.includeExtensions || this.defaultIncludeExtensions;

      const files = await this.findCodeFiles(
        options.rootPath,
        excludePatterns,
        includeExtensions,
        verbose
      );

      console.log(`Found ${files.length} code files to index`);

      const maxFiles = options.maxFiles || files.length;
      const filesToProcess = files.length > maxFiles ? files.slice(0, maxFiles) : files;
      if (files.length > maxFiles) {
        console.log(`⚠️ Large codebase detected (${files.length} files). Processing first ${maxFiles} files to prevent timeout.`);
        console.log(`💡 To process all files, increase the maxFiles option or use the direct indexing method.`);
      }

      const batchSize = 10;
      let totalChunks = 0;
      const allContent: string[] = [];
      for (let i = 0; i < filesToProcess.length; i += batchSize) {
        const batch = filesToProcess.slice(i, i + batchSize);
        const batchContent = await this.processBatch(batch, options.rootPath);
        allContent.push(...batchContent);
        totalChunks += batchContent.length;
        console.log(`Processed batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(filesToProcess.length/batchSize)}`);
      }

      const combinedContent = allContent.join('\n\n---FILE_SEPARATOR---\n\n');
      console.log('Saving to RAG system...');

      // Create category with proper naming convention: [Document Title] - [UUIDv4]
      const docId = uuidv4();
      const legacyCategory = options.category
        ? `${options.category} - ${docId}`
        : `Codebase Documentation - ${docId}`;

      const savePromise = this.storageTool.savePdfToByteStore(Buffer.from(combinedContent, 'utf-8'), `${options.projectName}_codebase`, combinedContent, legacyCategory, { projectName: options.projectName, indexedAt: new Date().toISOString(), totalFiles: files.length, rootPath: options.rootPath, type: 'codebase_index' });
      const timeoutPromise = new Promise((_, reject) => setTimeout(() => reject(new Error('Indexing timeout after 10 minutes')), 10 * 60 * 1000));
      const result = await Promise.race([savePromise, timeoutPromise]) as any;

      console.log(`✅ Successfully indexed codebase!`);
      return { success: true, totalFiles: files.length, totalChunks: result.totalChunks, documentId: result.documentId };
    } catch (error) {
      console.error('Error indexing codebase:', error);
      return { success: false, totalFiles: 0, totalChunks: 0, documentId: '', error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  async diagnoseIndexing(options: CodebaseIndexingOptions): Promise<{
    totalFilesFound: number;
    indexableFiles: string[];
    skippedDirectories: string[];
    errorPaths: string[];
    filesByExtension: Record<string, number>;
    summary: string;
  }> {
    const excludePatterns = options.excludePatterns || this.defaultExcludePatterns;
    const includeExtensions = options.includeExtensions || this.defaultIncludeExtensions;

    console.log(`🔍 Running indexing diagnostic for: ${options.rootPath}`);
    console.log(`📋 Exclude patterns: ${excludePatterns.join(', ')}`);
    console.log(`📋 Include extensions: ${includeExtensions.join(', ')}`);

    const files = await this.findCodeFiles(options.rootPath, excludePatterns, includeExtensions, true, options.selectedPaths);

    const filesByExtension: Record<string, number> = {};
    for (const file of files) {
      const ext = path.extname(file).toLowerCase();
      filesByExtension[ext] = (filesByExtension[ext] || 0) + 1;
    }

    const summary = `Found ${files.length} indexable files across ${Object.keys(filesByExtension).length} different extensions.`;

    return {
      totalFilesFound: files.length,
      indexableFiles: files.map(f => path.relative(options.rootPath, f)),
      skippedDirectories: [],
      errorPaths: [],
      filesByExtension,
      summary
    };
  }

  private async findCodeFiles(
    rootPath: string,
    excludePatterns: string[],
    includeExtensions: string[],
    verbose: boolean = false,
    selectedPaths?: string[]
  ): Promise<string[]> {
    const includeExtSet = new Set(includeExtensions.map(ext => ext.toLowerCase()));
    const files: Set<string> = new Set();
    const skippedDirectories: string[] = [];
    const errorPaths: string[] = [];
    const skippedFiles: string[] = []; // Track files skipped due to extension filtering
    const normalizedRootPath = path.resolve(rootPath);

    const traverse = async (currentPath: string, currentRelativePath: string): Promise<void> => {
      let items: Dirent[];
      try {
        items = await fs.readdir(currentPath, { withFileTypes: true });
      } catch (error) {
        const errorMsg = `Cannot read directory: ${currentPath} - ${error instanceof Error ? error.message : 'Unknown error'}`;
        errorPaths.push(errorMsg);
        if (verbose) console.warn(`⚠️  ${errorMsg}`);
        return;
      }

      const directoryPromises: Promise<void>[] = [];
      for (const item of items) {
        const itemName = item.name;
        const fullPath = path.join(currentPath, itemName);
        const relativePath = path.join(currentRelativePath, itemName);
        if (item.isDirectory()) {
          const shouldExclude = excludePatterns.some(pattern => {
            const normalizedPattern = path.normalize(pattern).toLowerCase();
            const normalizedRelativePath = path.normalize(relativePath).toLowerCase();
            if (normalizedRelativePath === normalizedPattern) return true;
            if (normalizedRelativePath.startsWith(normalizedPattern + path.sep)) return true;
            const pathSegments = normalizedRelativePath.split(path.sep);
            return pathSegments.includes(normalizedPattern);
          });
          if (shouldExclude) {
            skippedDirectories.push(relativePath);
            if (verbose) console.log(`⏭️  Skipping excluded directory: ${relativePath}`);
            continue;
          }
          directoryPromises.push(traverse(fullPath, relativePath));
        } else if (item.isFile()) {
          const ext = path.extname(itemName).toLowerCase();
          if (includeExtSet.has(ext)) {
            if (verbose) console.log(`📄 Found code file: ${relativePath}`);
            files.add(fullPath);
          } else {
            // Track files that were skipped due to extension filtering
            skippedFiles.push(relativePath);
            if (verbose) console.log(`⏭️  Skipping file (extension not included): ${relativePath}`);
          }
        }
      }
      await Promise.all(directoryPromises);
    };

    const pathsToScan = (selectedPaths && selectedPaths.length > 0) ? selectedPaths : [normalizedRootPath];

    if (verbose) {
      console.log(`🚀 Starting file discovery. Scanning ${pathsToScan.length} target location(s).`);
      console.log(selectedPaths && selectedPaths.length > 0 ? `🎯 Targeted paths:` : `📂 Full directory scan:`, pathsToScan);
    }

    const discoveryPromises = pathsToScan.map(async (startPath) => {
      const fullPath = path.isAbsolute(startPath) ? startPath : path.resolve(normalizedRootPath, startPath);
      try {
        const stats = await fs.stat(fullPath);
        if (stats.isDirectory()) {
          const relativeToRoot = path.relative(normalizedRootPath, fullPath);
          await traverse(fullPath, relativeToRoot);
        } else if (stats.isFile()) {
          const ext = path.extname(fullPath).toLowerCase();
          if (includeExtSet.has(ext)) {
            files.add(fullPath);
          }
        }
      } catch (error) {
        const errorMsg = `Cannot access target path: ${fullPath} - ${error instanceof Error ? error.message : 'Unknown error'}`;
        errorPaths.push(errorMsg);
        if (verbose) console.warn(`⚠️  ${errorMsg}`);
      }
    });
    await Promise.all(discoveryPromises);

    const finalFiles = Array.from(files);

    // Comprehensive logging for file count analysis
    console.log(`\n📊 File Discovery Summary:`);
    console.log(`   🎯 Selected paths: ${selectedPaths?.length || 0}`);
    console.log(`   📁 Paths scanned: ${pathsToScan.length}`);
    console.log(`   ✅ Files found: ${finalFiles.length}`);
    console.log(`   ⏭️  Files skipped (extension): ${skippedFiles.length}`);
    console.log(`   🚫 Directories excluded: ${skippedDirectories.length}`);
    console.log(`   ❌ Path errors: ${errorPaths.length}`);

    if (verbose && skippedFiles.length > 0) {
      console.log(`\n📋 Extension filtering details:`);
      console.log(`   Included extensions: ${includeExtensions.join(', ')}`);
      console.log(`   Sample skipped files: ${skippedFiles.slice(0, 5).join(', ')}${skippedFiles.length > 5 ? '...' : ''}`);
    }

    if (verbose && errorPaths.length > 0) {
      console.log(`\n⚠️  Path access errors:`);
      errorPaths.slice(0, 3).forEach(error => console.log(`   ${error}`));
      if (errorPaths.length > 3) console.log(`   ... and ${errorPaths.length - 3} more errors`);
    }

    console.log(`✅ File discovery complete. Found ${finalFiles.length} unique code files.`);
    return finalFiles;
  }

  private async processBatch(filePaths: string[], rootPath: string): Promise<string[]> {
    const contents: string[] = [];
    for (const filePath of filePaths) {
      try {
        const content = await fs.readFile(filePath, 'utf-8');
        const relativePath = path.relative(rootPath, filePath);
        const fileContent = `\n=== FILE: ${relativePath} ===\nLanguage: ${this.getLanguageFromExtension(path.extname(filePath))}\nPath: ${relativePath}\nSize: ${content.length} characters\n\n${content}\n\n=== END FILE: ${relativePath} ===\n`;
        contents.push(fileContent);
      } catch (error) {
        console.warn(`Could not read file: ${filePath}`);
      }
    }
    return contents;
  }

  private getLanguageFromExtension(ext: string): string {
    const languageMap: Record<string, string> = {
      '.ts': 'TypeScript', '.tsx': 'TypeScript React', '.js': 'JavaScript', '.jsx': 'JavaScript React',
      '.py': 'Python', '.java': 'Java', '.cpp': 'C++', '.c': 'C', '.cs': 'C#', '.go': 'Go', '.rs': 'Rust',
      '.php': 'PHP', '.rb': 'Ruby', '.swift': 'Swift', '.kt': 'Kotlin', '.scala': 'Scala', '.md': 'Markdown',
      '.txt': 'Text', '.json': 'JSON', '.yaml': 'YAML', '.yml': 'YAML'
    };
    return languageMap[ext] || 'Unknown';
  }

  private calculateCostSavings(originalLength: number, truncatedLength: number): { savedTokens: number, savedCost: number } {
    const CHARS_PER_TOKEN = 4;
    const COST_PER_MILLION_TOKENS = 0.30; // Gemini 2.5 Flash input cost

    const originalTokens = Math.ceil(originalLength / CHARS_PER_TOKEN);
    const truncatedTokens = Math.ceil(truncatedLength / CHARS_PER_TOKEN);
    const savedTokens = originalTokens - truncatedTokens;
    const savedCost = (savedTokens / 1_000_000) * COST_PER_MILLION_TOKENS;

    return { savedTokens, savedCost };
  }

  private logCostOptimization(files: { originalSize: number, truncatedSize: number }[]): void {
    const totalOriginal = files.reduce((sum, f) => sum + f.originalSize, 0);
    const totalTruncated = files.reduce((sum, f) => sum + f.truncatedSize, 0);
    const { savedTokens, savedCost } = this.calculateCostSavings(totalOriginal, totalTruncated);

    const reductionPercent = totalOriginal > 0 ? ((totalOriginal - totalTruncated) / totalOriginal * 100).toFixed(1) : "0.0";

    console.log(`\n💰 Adaptive Truncation Cost Optimization:`);
    console.log(`   📊 Content reduced by ${reductionPercent}% (${totalOriginal.toLocaleString()} → ${totalTruncated.toLocaleString()} chars)`);
    console.log(`   🪙 Tokens saved: ${savedTokens.toLocaleString()}`);
    console.log(`   💵 Estimated cost savings: $${savedCost.toFixed(4)}`);
    console.log(`   📈 Cost per file: ~$${files.length > 0 ? (savedCost / files.length).toFixed(6) : '0.000000'}`);
  }

  /**
   * **PHASE 1: Application Context Analysis**
   * Prefer deriving context from the area around selectedPaths (if provided)
   */
  private async analyzeApplicationContext(rootPath: string, selectedPaths?: string[]): Promise<void> {
      if (this.applicationContext) {
        console.log(`✅ Using cached application context`);
        return;
      }

      // Compute a candidate base path: common ancestor of selected paths or rootPath
      const resolveBaseFromSelected = (): string => {
        if (!selectedPaths || selectedPaths.length === 0) return rootPath;
        const abs = selectedPaths.map(p => path.isAbsolute(p) ? p : path.resolve(rootPath, p));
        // Find common ancestor
        const split = abs.map(p => p.split(path.sep));
        const minLen = Math.min(...split.map(s => s.length));
        const common: string[] = [];
        for (let i = 0; i < minLen; i++) {
          const seg = split[0][i];
          if (split.every(s => s[i] === seg)) common.push(seg); else break;
        }
        const commonPath = common.length > 0 ? common.join(path.sep) : rootPath;
        return path.isAbsolute(commonPath) ? commonPath : path.resolve(rootPath, commonPath);
      };

      try {
        const basePath = resolveBaseFromSelected();
        const scanRoots = Array.from(new Set([basePath, rootPath]));
        console.log(`📍 Context analysis roots (ordered):`, scanRoots);

        const entryVariants = [
          ['app', 'page.tsx'], ['app', 'page.jsx'],
          ['src','app','page.tsx'], ['src','app','page.jsx'],
          ['pages','index.tsx'], ['pages','index.jsx'],
          ['src','pages','index.tsx'], ['src','pages','index.jsx'],
          ['src','App.tsx'], ['src','App.jsx'],
          ['App.tsx'], ['App.jsx']
        ];

        let entryPointContent = '';
        let entryPointPath = '';

        // Search within base preferred roots first
        for (const root of scanRoots) {
          for (const variant of entryVariants) {
            const entryPoint = path.join(root, ...variant);
            try {
              entryPointContent = await fs.readFile(entryPoint, 'utf-8');
              entryPointPath = path.relative(rootPath, entryPoint);
              console.log(`📍 Found application entry point near selection: ${entryPointPath}`);
              break;
            } catch { /* continue */ }
          }
          if (entryPointContent) break;
        }

        if (!entryPointContent) {
          console.log(`⚠️  No main entry point found near selected paths, using generic application context`);
          this.applicationContext = "Generic web application - specific context unavailable";
          return;
        }

        const prompt = `
  You are analyzing the main entry point of a software application to understand its overall purpose and architecture.
  **File**: ${entryPointPath}
  **Task**: Provide a comprehensive analysis of this application's purpose, architecture, and domain.
  **Analysis Requirements:**
  1. **Application Name**: The name of the application if it can be inferred from the code (e.g., from titles, logos, or constants), or 'Unknown' if not apparent.
  2. **Application Purpose**: What is the main goal/function of this application?
  3. **Architecture Pattern**: What framework/architecture is being used? (Next.js, React, etc.)
  4. **Domain/Industry**: What business domain does this application serve?
  5. **Key Features**: What are the main features/capabilities based on imports and components?
  6. **User Interface**: What type of UI/UX patterns are evident?
  7. **Data Flow**: How does data appear to flow through the application?
  **Instructions:**
  - Focus on imports, component structure, routing, and any comments
  - Provide a 9-10 sentence summary that captures the essence of the application
  Respond with ONLY a JSON object:
  {
      "applicationName": "The name of the application if apparent",
      "applicationPurpose": "Brief description of what this application does",
      "architecture": "Framework and architectural patterns used",
      "domain": "Business domain/industry this serves",
      "keyFeatures": ["feature1", "feature2", "feature3"],
      "summary": "9-10 sentence comprehensive summary of the application's purpose and architecture"
  }
  **APPLICATION ENTRY POINT CODE:**
  \`\`\`typescript
  ${entryPointContent}
  \`\`\`
        `;

        console.log(`🧠 Analyzing application context with Google Gemini 2.5 Pro...`);
        const result = await processWithGoogleAI({ prompt, model: "gemini-2.5-pro" });
        const jsonString = result.substring(result.indexOf('{'), result.lastIndexOf('}') + 1);
        const analysis = JSON.parse(jsonString);

        this.applicationContext = `
  Application Name: ${analysis.applicationName || 'Unknown'}
  Application Purpose: ${analysis.applicationPurpose || 'Unknown'}
  Architecture: ${analysis.architecture || 'Unknown'}
  Domain: ${analysis.domain || 'Unknown'}
  Key Features: ${Array.isArray(analysis.keyFeatures) ? analysis.keyFeatures.join(', ') : 'Unknown'}
  Summary: ${analysis.summary || 'No summary available'}
        `.trim();

        console.log(`✅ Application context analysis complete`);
        console.log(`📋 Context: ${this.applicationContext.split('\n')[0]}...`);
      } catch (error) {
        console.warn(`⚠️  Application context analysis failed:`, error);
        this.applicationContext = "Application context analysis failed - using generic context";
      }
    }

  private async analyzeFileWithLLM(filePath: string, language: string, content: string): Promise<FileAnalysisResult> {
      console.log(`🔍 Debug - analyzeFileWithLLM called with filePath: "${filePath}", language: "${language}"`);
      const prompt = `
  You are an expert software architect analyzing a source code file. Provide a comprehensive, high-level analysis suitable for non-technical readers. Use simple language to explain concepts, while still mentioning technical terms like APIs, components, hooks, and frameworks where relevant.

  **Application Context:**
  ${this.applicationContext || 'Application context not available'}
  Use the provided Application Context definitively for details like application name, purpose, architecture, and domain. Do not infer or qualify these (e.g., avoid 'likely named' or 'appears to be') if they are specified in the context—state them directly.

  **File Information:**
  - File Path: ${filePath}
  - Language: ${language}

  **Analysis Requirements:**
  1. **Summary**: Provide a detailed, high-level summary of the file's purpose and functionality. The summary MUST:
    - Describe the file's primary role in the application (e.g., if it's a main page or component, explain what users can do from there).
    - Mention the overall application name, purpose, and theme directly from the provided Application Context. If not specified in the context, infer from the code if possible.
    - Highlight key features, user interactions, and accessible elements (e.g., buttons leading to assessments, chatbots, or workout plans).
    - List key defined functions/classes/components.
    - Mention important imported libraries and frameworks (e.g., React for building UI, Next.js for server-side rendering, Firebase for authentication).
    - Note specific hooks/components used (e.g., useState for managing state, useEffect for side effects).
    - Detail any APIs, endpoints, or external services interacted with (e.g., Firebase functions for user data).
    - Keep the description engaging and easy to understand for non-technical audiences, avoiding deep code jargon.

  2. **Entity Type**: Classify the file's primary purpose from: Component, Function, Class, Hook, Configuration, Util, Unknown.

  3. **Defined Entities**: List the most important functions, components, classes, interfaces, or constants DEFINED IN THIS FILE.

  4. **Used Libraries**: List the core libraries/packages imported (e.g., "react", "axios", "path"). Do not include relative file imports.

  5. **Used Components/Hooks**: List the specific components or hooks imported and used from libraries (e.g., "useState", "useEffect", "useRouter").

  6. **Imports & Exports (REQUIRED)**:
     - Extract import sources as strings (e.g., "react", "next/router", "./local", "../utils").
     - Extract export statements as strings (e.g., "default", "MyComponent", "useThing", "CONST_X").
     - If none found, return empty arrays, not null.

  7. **API Endpoints**: List any API endpoint URLs found in fetch calls, axios calls, or other HTTP clients. List the full string literal. Also include any inferred API interactions from imported functions if they involve external services.

  Respond with ONLY a valid JSON object:
  {
      "summary": "...",
      "entityType": "Component",
      "definedEntities": ["Home", "handleGenerateWorkoutPlan"],
      "usedLibraries": ["react", "next/image"],
      "usedComponentsOrHooks": ["useState", "useEffect"],
      "imports": ["react", "next/router", "./local"],
      "exports": ["default", "MyComponent"],
      "apiEndpoints": ["/api/users/:id"]
  }

  **FULL FILE CONTENT:**
  \`\`\`${language}
  ${content}
  \`\`\`
      `;

      try {
        const result = await processWithGoogleAI({ prompt, model: "gemini-2.5-pro" });
        const jsonString = result.substring(result.indexOf('{'), result.lastIndexOf('}') + 1);
        const parsed = JSON.parse(jsonString);
        return this.validateAndSanitizeAnalysisResponse(parsed);
      } catch (error) {
        console.warn(`CodebaseIndexingTool: Full file analysis failed for ${filePath}:`, error);
        return this.getDefaultAnalysisResponse();
      }
    }

  private async processAndEnrichFile(filePath: string, options: CodebaseIndexingOptions, fileId: string): Promise<{
    enrichedChunks: EnrichedCodeChunk[],
    analysis: FileAnalysisResult
  }> {
    let content: string;
    try {
      content = await fs.readFile(filePath, 'utf-8');
    } catch (error) {
      console.warn(`⚠️  Failed to read file: ${path.relative(options.rootPath, filePath)} - ${error instanceof Error ? error.message : 'Unknown error'}`);
      return { enrichedChunks: [], analysis: this.getDefaultAnalysisResponse() };
    }

    const relativePath = path.relative(options.rootPath, filePath);
    const fileName = path.basename(filePath);
    const language = this.getLanguageFromExtension(path.extname(filePath));

    const trimmedContent = content.trim();
    if (!trimmedContent || trimmedContent.length < 20) {
      if (options.verbose) {
        console.log(`⏭️  Skipping file with insufficient content: ${relativePath} (${trimmedContent.length} chars)`);
      }
      return { enrichedChunks: [], analysis: this.getDefaultAnalysisResponse() };
    }

    console.log(`🔬 Performing full-file analysis for: ${fileName}`);
    const fileLevelAnalysis = await this.analyzeFileWithLLM(relativePath, language, content);

    const textSplitter = new RecursiveCharacterTextSplitter({
      chunkSize: options.chunkSize || 2500,
      chunkOverlap: options.chunkOverlap || 300,
      separators: ["\n\n", "\n", ". ", " ", ""]
    });
    const codeChunks = await textSplitter.splitText(content);

    const meaningfulCodeChunks = codeChunks.filter(chunk => chunk.trim().length > 50);

    if (meaningfulCodeChunks.length === 0) {
        console.warn(`⚠️ No meaningful code chunks generated for ${relativePath}, likely a very small file. Skipping.`);
        return { enrichedChunks: [], analysis: this.getDefaultAnalysisResponse() };
    }

    const enrichedChunks: EnrichedCodeChunk[] = meaningfulCodeChunks.map((chunkContent, index) => {
      return {
        content: chunkContent,
        metadata: {
          title: fileName,
          filePath: relativePath,
          language: language,
          chunkIndex: index,
          projectName: options.projectName,
          type: 'code_chunk' as const,
          indexedAt: new Date().toISOString(),
          doc_id: '',
          chunk_id: '',
          fileId: fileId,
          codeSummary: fileLevelAnalysis.summary,
          codeEntityType: fileLevelAnalysis.entityType,
          definedEntities: fileLevelAnalysis.definedEntities,
          usedLibraries: fileLevelAnalysis.usedLibraries,
          usedComponentsOrHooks: fileLevelAnalysis.usedComponentsOrHooks,
          imports: fileLevelAnalysis.imports,
          exports: fileLevelAnalysis.exports,
          apiEndpoints: fileLevelAnalysis.apiEndpoints,
        }
      };
    });

    return { enrichedChunks, analysis: fileLevelAnalysis };
  }

  private validateAndSanitizeAnalysisResponse(parsed: any): FileAnalysisResult {
    const validEntityTypes = ['Component', 'Function', 'Class', 'Hook', 'Configuration', 'Util', 'Unknown'];
    const sanitizeStringArray = (arr: any): string[] =>
      Array.isArray(arr) ? arr.filter((e: any) => typeof e === 'string' && e.trim()).map((e: string) => e.trim()) : [];

    return {
      summary: (typeof parsed?.summary === 'string' && parsed.summary.trim()) ? parsed.summary.trim() : "Code analysis summary not available.",
      entityType: (typeof parsed?.entityType === 'string' && validEntityTypes.includes(parsed.entityType)) ? parsed.entityType as any : 'Unknown',
      definedEntities: sanitizeStringArray(parsed?.definedEntities),
      usedLibraries: sanitizeStringArray(parsed?.usedLibraries),
      usedComponentsOrHooks: sanitizeStringArray(parsed?.usedComponentsOrHooks),
      imports: sanitizeStringArray(parsed?.imports),
      exports: sanitizeStringArray(parsed?.exports),
      apiEndpoints: sanitizeStringArray(parsed?.apiEndpoints)
    };
  }

  private getDefaultAnalysisResponse(): FileAnalysisResult {
    return {
      summary: "No summary available due to LLM analysis failures.",
      entityType: "Unknown",
      definedEntities: [],
      usedLibraries: [],
      usedComponentsOrHooks: [],
      imports: [],
      exports: [],
      apiEndpoints: []
    };
  }

  private sanitizeMetadataForPinecone(metadata: any): any {
    const sanitized: any = {};
    for (const [key, value] of Object.entries(metadata)) {
      if (value === null || value === undefined) {
        if (key === 'codeEntityType') sanitized[key] = 'Unknown';
        else if (Array.isArray(value) || key.includes('Entities') || key.includes('Libraries') || key.includes('Hooks') || key.includes('Endpoints') || key.includes('Imports') || key.includes('Exports')) sanitized[key] = [];
        else sanitized[key] = '';
      } else if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {
        sanitized[key] = value;
      } else if (Array.isArray(value)) {
        sanitized[key] = value.filter(item => typeof item === 'string' && item.trim()).map(item => String(item).trim());
      } else if (typeof value === 'object') {
        sanitized[key] = JSON.stringify(value);
      } else {
        sanitized[key] = String(value);
      }
    }
    return sanitized;
  }
}

export const codebaseIndexingTool = new CodebaseIndexingTool();