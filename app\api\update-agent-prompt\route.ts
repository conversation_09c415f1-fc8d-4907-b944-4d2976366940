import { NextRequest, NextResponse } from 'next/server';
import { ElevenLabsClient } from 'elevenlabs';

/**
 * API endpoint to update existing ElevenLabs agent with new prompt instructions
 * This ensures the agent uses the exact document category from knowledge base
 */
export async function POST(request: NextRequest) {
  try {
    console.log('[UPDATE_AGENT_PROMPT] Starting agent prompt update...');

    const apiKey = process.env.NEXT_PUBLIC_ELEVENLABS_COMPANY_API_KEY;
    const agentId = 'agent_1001k5b2k5e0f98tphggas7twfrf'; // Marketing agent ID from logs

    if (!apiKey) {
      return NextResponse.json({ 
        error: 'Missing NEXT_PUBLIC_ELEVENLABS_COMPANY_API_KEY' 
      }, { status: 500 });
    }

    const client = new ElevenLabsClient({ apiKey });

    console.log('[UPDATE_AGENT_PROMPT] Fetching current agent configuration...');
    const currentAgent = await client.conversationalAi.getAgent(agentId);
    
    console.log(`[UPDATE_AGENT_PROMPT] Current agent: ${currentAgent.name}`);

    // Get the current prompt with null checking
    if (!currentAgent.conversation_config?.agent?.prompt?.prompt) {
      return NextResponse.json({
        error: 'Agent prompt configuration not found'
      }, { status: 500 });
    }

    const currentPrompt = currentAgent.conversation_config.agent.prompt.prompt;
    
    // Update the prompt to include the new instructions
    const updatedPrompt = currentPrompt.replace(
      /\*\*Available Tools:\*\*[\s\S]*?- CRITICAL: The documentCategory must be the EXACT category string from the document metadata, not a generic category you create/,
      `**Available Tools:**
- Use 'save_meeting_summary' tool ONLY when the user explicitly requests to save the meeting transcript or generate a meeting summary document. Do not use this tool automatically.
- When using 'save_meeting_summary', ALWAYS include any web search results, search queries, and research findings from the meeting to ensure all research data is preserved.
- When calling 'save_meeting_summary', you MUST extract the EXACT document category from your knowledge base documents and use it in transcript_metadata:
  * agentType: "Marketing" (your agent type)
  * documentCategory: Use the EXACT category from the document in your knowledge base (e.g., "PMO - Single Independent women will be the least prepared for the AI onslaught - 3541080b-ee62-4451-8315-4cd0a0131319") - DO NOT make up generic categories like "Market Research"
  * selectedDocuments: Array of document titles or IDs referenced during the conversation
- CRITICAL: The documentCategory must be the EXACT category string from the document metadata, not a generic category you create`
    );

    console.log('[UPDATE_AGENT_PROMPT] Updating agent prompt...');
    
    // Update the agent with the new prompt
    const updateConfig = {
      conversation_config: {
        ...currentAgent.conversation_config,
        agent: {
          ...currentAgent.conversation_config.agent,
          prompt: {
            ...currentAgent.conversation_config.agent.prompt,
            prompt: updatedPrompt
          }
        }
      }
    };

    await client.conversationalAi.updateAgent(agentId, updateConfig);
    
    console.log('[UPDATE_AGENT_PROMPT] Agent prompt updated successfully!');
    
    return NextResponse.json({
      success: true,
      message: 'Agent prompt updated successfully',
      agentId,
      agentName: currentAgent.name,
      note: 'The agent will now use the EXACT document category from the knowledge base instead of making up generic categories'
    });
    
  } catch (error) {
    console.error('[UPDATE_AGENT_PROMPT] Error updating agent:', error);
    return NextResponse.json({ 
      error: 'Failed to update agent prompt',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
