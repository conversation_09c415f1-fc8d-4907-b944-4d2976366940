import { NextRequest, NextResponse } from 'next/server';
import * as fs from 'fs/promises';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { storageTool } from 'lib/tools/storage-tool';

function detectLanguage(ext: string): string {
  const map: Record<string, string> = {
    '.ts': 'TypeScript',
    '.tsx': 'TypeScript React',
    '.js': 'JavaScript',
    '.jsx': 'JavaScript React',
  };
  return map[ext.toLowerCase()] || 'Unknown';
}

function extractImportsExports(content: string) {
  const importRegex = /import\s+[^{]*?(?:\{[^}]*\}\s+)?from\s+['"]([^'"]+)['"]/gm;
  const exportDefaultRegex = /export\s+default\s+/gm;
  const exportNamedRegex = /export\s+\{([^}]*)\}/gm;

  const imports: string[] = [];
  const usedLibraries: string[] = [];
  const usedComponentsOrHooks: string[] = [];
  const exportsArr: string[] = [];

  let m: RegExpExecArray | null;
  while ((m = importRegex.exec(content)) !== null) {
    const src = m[1];
    imports.push(src);
    if (!src.startsWith('.') && !src.startsWith('/')) usedLibraries.push(src);
    // Try to capture specifiers within braces before 'from'
    const lineStart = content.lastIndexOf('\n', m.index) + 1;
    const lineEnd = content.indexOf('\n', m.index);
    const line = content.slice(lineStart, lineEnd === -1 ? undefined : lineEnd);
    const braceMatch = line.match(/\{([^}]*)\}/);
    if (braceMatch && braceMatch[1]) {
      braceMatch[1].split(',').forEach(t => {
        const name = t.trim();
        if (name) usedComponentsOrHooks.push(name);
      });
    }
  }
  if (exportDefaultRegex.test(content)) exportsArr.push('default');
  let en: RegExpExecArray | null;
  while ((en = exportNamedRegex.exec(content)) !== null) {
    en[1].split(',').forEach(t => {
      const name = t.trim();
      if (name) exportsArr.push(name);
    });
  }

  return {
    imports: Array.from(new Set(imports)),
    exportsArr: Array.from(new Set(exportsArr)),
    usedLibraries: Array.from(new Set(usedLibraries)),
    usedComponentsOrHooks: Array.from(new Set(usedComponentsOrHooks))
  };
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { userId, category, files } = body || {};

    if (!userId) return NextResponse.json({ success: false, error: 'userId is required' }, { status: 400 });
    if (!category) return NextResponse.json({ success: false, error: 'category is required' }, { status: 400 });
    if (!Array.isArray(files) || files.length === 0) {
      return NextResponse.json({ success: false, error: 'files[] (absolute paths) is required' }, { status: 400 });
    }

    const results: any[] = [];
    for (const abs of files) {
      try {
        const filePath = String(abs);
        const content = await fs.readFile(filePath, 'utf-8');
        const fileName = path.basename(filePath);
        const ext = path.extname(fileName);
        const language = detectLanguage(ext);
        const fileId = uuidv4();

        const { imports, exportsArr, usedLibraries, usedComponentsOrHooks } = extractImportsExports(content);

        // Create a lightweight summary for testing purposes
        const summary = `Test analysis for ${fileName}. This is a smoke run to ensure byteStoreCollection writes succeed.`;

        await storageTool.writeCodeFileDoc(
          userId,
          fileId,
          {
            type: 'application/code',
            category,
            namespace: fileId,
            filePath,
            name: fileName,
            language,
            fileSize: content.length,
            chunkCount: 0,
            processingTimeMs: 0,
            success: true,
            llmSummary: summary,
            definedEntities: [],
            usedLibraries,
            usedComponentsOrHooks,
            imports,
            exports: exportsArr,
            apiEndpoints: []
          },
          (u) => {
            console.log(`[SMOKE] ${u.step} - ${u.message}`);
          }
        );

        results.push({ fileId, fileName, written: true });
      } catch (fileErr: any) {
        results.push({ filePath: abs, written: false, error: fileErr?.message || String(fileErr) });
      }
    }

    return NextResponse.json({ success: true, results });
  } catch (err: any) {
    console.error('smoke-write error:', err);
    return NextResponse.json({ success: false, error: err?.message || 'internal error' }, { status: 500 });
  }
}

