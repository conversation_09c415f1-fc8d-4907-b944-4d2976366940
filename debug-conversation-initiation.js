/**
 * Debug script to understand why conversation_initiation_client_data is empty
 */

console.log('🔍 Debugging Conversation Initiation Data Issue');
console.log('='.repeat(60));

// From the logs, we can see:
const actualWebhookPayload = {
  "agent_name": null,
  "agent_id": null,
  "transcript_metadata": {
    "agentType": "Marketing",
    "selectedDocuments": [
      "Scene Mate Viral Video Ad Concept"
    ],
    "documentCategory": "Strategic Planning"
  },
  "generate_document": true,
  "document_title": "Meeting Summary - Scene Mate Viral Video Ad Concept Discussion",
  "conversation_id": null,
  "action_items": null,
  "research_findings": null,
  "search_queries": null,
  "summary": "We discussed the Scene Mate Viral Video Ad Concept...",
  "web_search_results": null,
  // ❌ MISSING: conversation_initiation_client_data
};

console.log('❌ PROBLEM IDENTIFIED:');
console.log('1. conversation_initiation_client_data is missing from webhook payload');
console.log('2. agent_id is null (should be ElevenLabs agent ID)');
console.log('3. conversation_id is null');
console.log('');

console.log('🔍 ANALYSIS:');
console.log('The webhook payload suggests that:');
console.log('- ElevenLabs is not sending conversation_initiation_client_data');
console.log('- The agent_id and conversation_id are null');
console.log('- Only transcript_metadata is being populated');
console.log('');

console.log('🎯 ROOT CAUSE:');
console.log('The conversation initiation data fix in usePMOVoiceConversation.ts');
console.log('is not being transmitted to ElevenLabs, OR ElevenLabs is not');
console.log('including it in the webhook payload.');
console.log('');

console.log('📋 EXPECTED vs ACTUAL:');
console.log('');
console.log('EXPECTED webhook payload:');
const expectedPayload = {
  agent_id: 'agent_abc123',
  conversation_id: 'conv_xyz789',
  conversation_initiation_client_data: {
    agentType: 'Marketing Director',
    documentCategory: 'PMO - Scene Mate campaign - 76501041-422b-4a71-9e5d-37df1dba2a88',
    selectedDocuments: [{
      id: 'doc_123',
      title: 'Scene Mate Viral Video Ad Concept',
      category: 'PMO - Scene Mate campaign - 76501041-422b-4a71-9e5d-37df1dba2a88'
    }]
  },
  transcript_metadata: {
    agentType: 'Marketing',
    selectedDocuments: ['Scene Mate Viral Video Ad Concept'],
    documentCategory: 'PMO - Scene Mate campaign - 76501041-422b-4a71-9e5d-37df1dba2a88'
  }
};

console.log(JSON.stringify(expectedPayload, null, 2));
console.log('');

console.log('ACTUAL webhook payload:');
console.log(JSON.stringify(actualWebhookPayload, null, 2));
console.log('');

console.log('🚨 CRITICAL ISSUES:');
console.log('1. ❌ conversation_initiation_client_data: MISSING');
console.log('2. ❌ agent_id: null (should be ElevenLabs agent ID)');
console.log('3. ❌ conversation_id: null');
console.log('4. ❌ transcript_metadata.documentCategory: "Strategic Planning" (should be full PMO category)');
console.log('');

console.log('🔧 POTENTIAL FIXES:');
console.log('1. Verify usePMOVoiceConversation.ts is actually being used');
console.log('2. Check if ElevenLabs conversation.startSession() is working correctly');
console.log('3. Verify ElevenLabs webhook configuration includes conversation_initiation_metadata');
console.log('4. Check if transcript_metadata is being generated correctly');
console.log('');

console.log('🎯 IMMEDIATE ACTION:');
console.log('Since conversation_initiation_client_data is empty, we need to:');
console.log('1. Use transcript_metadata.documentCategory as fallback');
console.log('2. BUT fix transcript_metadata to contain the correct category');
console.log('3. The category should be the full PMO category from the UI');
console.log('');

console.log('📍 CATEGORY MISMATCH:');
console.log('UI shows: "PMO - Scene Mate campaign - 76501041-422b-4a71-9e5d-37df1dba2a88"');
console.log('Webhook receives: "Strategic Planning"');
console.log('This suggests transcript_metadata is not getting the correct category');
