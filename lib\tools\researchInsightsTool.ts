/**
 * Research Insights Tool
 *
 * This tool provides an interface for the StrategicDirectorAgent to call methods
 * on the ResearchInsightsAgent for research-related requests.
 */

import { ResearchInsightsAgent } from '../agents/marketing/ResearchInsightsAgent';

export interface ResearchInsightsToolOptions {
  userId: string;
}

export interface ResearchMarketingTopicOptions {
  topic: string;
  researchQuestions?: string[];
  useInternetSearch?: boolean;
}

export interface ConductMarketingResearchOptions {
  topic: string;
  context?: string;
  researchQuestions?: string[];
}

export interface ConductCompetitiveAnalysisOptions {
  productName: string;
  industry: string;
}

export interface ConductMarketResearchOptions {
  productName: string;
  targetAudience: string;
  uniqueSellingPoints: string;
}

/**
 * Tool for accessing ResearchInsightsAgent functionality
 */
export class ResearchInsightsTool {
  private userId: string;
  private researchAgent: ResearchInsightsAgent | null = null;

  constructor(options: ResearchInsightsToolOptions) {
    this.userId = options.userId;
  }

  /**
   * Get or initialize the ResearchInsightsAgent
   */
  private async getResearchAgent(): Promise<ResearchInsightsAgent> {
    if (this.researchAgent) {
      return this.researchAgent;
    }

    // Initialize a new ResearchInsightsAgent
    this.researchAgent = new ResearchInsightsAgent(
      'research-insights',
      'Research & Insights',
      this.userId
    );

    return this.researchAgent;
  }

  /**
   * Research a marketing topic using the ResearchInsightsAgent
   */
  async researchMarketingTopic(options: ResearchMarketingTopicOptions): Promise<any> {
    try {
      console.log(`[ResearchInsightsTool] Calling ResearchInsightsAgent.researchMarketingTopic with topic: "${options.topic}"`);
      console.log(`[ResearchInsightsTool] Research questions: ${JSON.stringify(options.researchQuestions || [])}`);
      console.log(`[ResearchInsightsTool] Using internet search: ${options.useInternetSearch || false}`);

      const agent = await this.getResearchAgent();
      const result = await agent.researchMarketingTopic(
        options.topic,
        options.researchQuestions || [],
        options.useInternetSearch || false
      );

      console.log(`[ResearchInsightsTool] ResearchInsightsAgent.researchMarketingTopic completed successfully`);
      return result;
    } catch (error) {
      console.error('[ResearchInsightsTool] Error in researchMarketingTopic:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Conduct marketing research using the ResearchInsightsAgent
   */
  async conductMarketingResearch(options: ConductMarketingResearchOptions): Promise<any> {
    try {
      console.log(`[ResearchInsightsTool] Calling ResearchInsightsAgent.conductMarketingResearch with topic: "${options.topic}"`);
      console.log(`[ResearchInsightsTool] Context: ${options.context || 'None provided'}`);
      console.log(`[ResearchInsightsTool] Research questions: ${JSON.stringify(options.researchQuestions || [])}`);

      const agent = await this.getResearchAgent();
      const result = await agent.conductMarketingResearch(
        options.topic,
        options.context || '',
        options.researchQuestions || []
      );

      console.log(`[ResearchInsightsTool] ResearchInsightsAgent.conductMarketingResearch completed successfully`);
      return result;
    } catch (error) {
      console.error('[ResearchInsightsTool] Error in conductMarketingResearch:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Conduct competitive analysis using the ResearchInsightsAgent
   */
  async conductCompetitiveAnalysis(options: ConductCompetitiveAnalysisOptions): Promise<any> {
    try {
      console.log(`[ResearchInsightsTool] Calling ResearchInsightsAgent.conductCompetitiveAnalysis for product: "${options.productName}"`);
      console.log(`[ResearchInsightsTool] Industry: ${options.industry}`);

      const agent = await this.getResearchAgent();
      const result = await agent.conductCompetitiveAnalysis(
        options.productName,
        options.industry
      );

      console.log(`[ResearchInsightsTool] ResearchInsightsAgent.conductCompetitiveAnalysis completed successfully`);
      return result;
    } catch (error) {
      console.error('[ResearchInsightsTool] Error in conductCompetitiveAnalysis:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Conduct market research using the ResearchInsightsAgent
   */
  async conductMarketResearch(options: ConductMarketResearchOptions): Promise<any> {
    try {
      console.log(`[ResearchInsightsTool] Calling ResearchInsightsAgent.conductMarketResearch for product: "${options.productName}"`);
      console.log(`[ResearchInsightsTool] Target audience: ${options.targetAudience}`);
      console.log(`[ResearchInsightsTool] Unique selling points: ${options.uniqueSellingPoints}`);

      const agent = await this.getResearchAgent();
      const result = await agent.conductMarketResearch(
        options.productName,
        options.targetAudience,
        options.uniqueSellingPoints
      );

      console.log(`[ResearchInsightsTool] ResearchInsightsAgent.conductMarketResearch completed successfully`);
      return result;
    } catch (error) {
      console.error('[ResearchInsightsTool] Error in conductMarketResearch:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Get the tool definition for function calling
   */
  getToolDefinition() {
    return {
      type: "function",
      function: {
        name: "researchInsightsTool",
        description: "Access research and insights capabilities for marketing topics, competitive analysis, and market research",
        parameters: {
          type: "object",
          properties: {
            operation: {
              type: "string",
              enum: ["researchMarketingTopic", "conductMarketingResearch", "conductCompetitiveAnalysis", "conductMarketResearch"],
              description: "The research operation to perform"
            },
            topic: {
              type: "string",
              description: "The marketing topic to research (for researchMarketingTopic and conductMarketingResearch)"
            },
            researchQuestions: {
              type: "array",
              items: {
                type: "string"
              },
              description: "Specific questions to address in the research (optional)"
            },
            useInternetSearch: {
              type: "boolean",
              description: "Whether to use internet search for additional information (optional)"
            },
            context: {
              type: "string",
              description: "Additional context for the research (optional)"
            },
            productName: {
              type: "string",
              description: "The name of the product for competitive analysis or market research"
            },
            industry: {
              type: "string",
              description: "The industry for competitive analysis"
            },
            targetAudience: {
              type: "string",
              description: "The target audience for market research"
            },
            uniqueSellingPoints: {
              type: "string",
              description: "The unique selling points for market research"
            }
          },
          required: ["operation"]
        }
      }
    };
  }
}

// Export a function to create the tool with the user ID
export function createResearchInsightsTool(userId: string): ResearchInsightsTool {
  return new ResearchInsightsTool({ userId });
}
