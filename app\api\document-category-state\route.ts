/**
 * API endpoint for storing and retrieving document category state
 * This allows the UI to store the exact category values and webhooks to retrieve them
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]/authOptions';
import { adminDb } from '../../../components/firebase-admin';
import { Timestamp } from 'firebase-admin/firestore';

// ✅ FIX: Use Firestore instead of in-memory storage for serverless compatibility
const CATEGORY_STATE_COLLECTION = 'document_category_sessions';

/**
 * Store document category state from UI
 */
export async function POST(request: NextRequest) {
  try {
    // For internal category state storage, we allow unauthenticated requests
    // since this is used by the UI hook to store state for webhook access
    const body = await request.json();
    const { userId, selectedDocuments, primaryCategory, conversationId, agentType, timestamp } = body;

    // Validate required fields
    if (!userId || !Array.isArray(selectedDocuments)) {
      return NextResponse.json({ 
        error: 'Invalid request body. userId and selectedDocuments are required.' 
      }, { status: 400 });
    }

    // ✅ FIX: Store the state in Firestore instead of in-memory
    const categoryState = {
      userId,
      selectedDocuments,
      primaryCategory,
      conversationId,
      agentType,
      timestamp: timestamp || Date.now(),
      createdAt: Timestamp.now(), // For TTL policy
      storedAt: new Date().toISOString()
    };

    // Store under multiple keys for reliable access
    const storeKeys = [
      userId,
      '<EMAIL>', // Fallback key
      ...(conversationId ? [conversationId] : []),
      ...(agentType ? [`agent-type-${agentType}`] : [])
    ].filter((key, index, arr) => key && arr.indexOf(key) === index);

    console.log('[CATEGORY_STATE_API] 🔍 Storing category state under keys:', storeKeys);

    // Use Firestore batch write for atomicity
    const batch = adminDb.batch();
    for (const key of storeKeys) {
      const docRef = adminDb.collection(CATEGORY_STATE_COLLECTION).doc(key);
      batch.set(docRef, categoryState);
    }
    await batch.commit();

    console.log('[CATEGORY_STATE_API] ✅ Stored category state:', {
      userId,
      primaryCategory,
      selectedDocumentsCount: selectedDocuments.length,
      conversationId,
      agentType
    });

    return NextResponse.json({ 
      success: true, 
      message: 'Category state stored successfully',
      primaryCategory 
    });

  } catch (error) {
    console.error('[CATEGORY_STATE_API] Error storing category state:', error);
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 });
  }
}

/**
 * Retrieve document category state for webhook
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const conversationId = searchParams.get('conversationId');
    const agentId = searchParams.get('agentId');

    if (!userId) {
      return NextResponse.json({ 
        error: 'userId parameter is required' 
      }, { status: 400 });
    }

    // ✅ FIX: Get the stored state from Firestore with priority-based lookup
    const lookupKeys = [
      userId,
      '<EMAIL>', // Fallback key
      ...(conversationId ? [conversationId] : []),
      ...(agentId ? [agentId] : [])
    ].filter(Boolean);

    console.log('[CATEGORY_STATE_API] 🔍 Looking up category state with keys:', lookupKeys);

    let categoryState = null;
    let foundKey = null;

    // Try each key in priority order
    for (const key of lookupKeys) {
      try {
        const docSnapshot = await adminDb.collection(CATEGORY_STATE_COLLECTION).doc(key).get();
        if (docSnapshot.exists) {
          categoryState = docSnapshot.data();
          foundKey = key;
          console.log('[CATEGORY_STATE_API] ✅ Found category state using key:', key);
          break;
        }
      } catch (error) {
        console.error('[CATEGORY_STATE_API] Error querying Firestore for key:', key, error);
      }
    }

    if (!categoryState) {
      console.log('[CATEGORY_STATE_API] ⚠️ No category state found for user:', userId, 'with any of the lookup keys:', lookupKeys);
      return NextResponse.json({
        success: false,
        message: 'No category state found for user',
        categoryState: null
      });
    }

    // Check if the state is recent (within last 30 minutes)
    const stateAge = Date.now() - categoryState.timestamp;
    const maxAge = 30 * 60 * 1000; // 30 minutes

    if (stateAge > maxAge) {
      console.log('[CATEGORY_STATE_API] ⚠️ Category state is too old:', {
        userId,
        stateAge: Math.round(stateAge / 1000 / 60),
        maxAgeMinutes: 30
      });
      return NextResponse.json({ 
        success: false,
        message: 'Category state is too old',
        categoryState: null
      });
    }

    console.log('[CATEGORY_STATE_API] ✅ Retrieved category state:', {
      userId,
      foundKey,
      primaryCategory: categoryState.primaryCategory,
      selectedDocumentsCount: categoryState.selectedDocuments?.length || 0,
      conversationId: categoryState.conversationId,
      agentType: categoryState.agentType,
      stateAgeMinutes: Math.round(stateAge / 1000 / 60)
    });

    return NextResponse.json({ 
      success: true,
      categoryState: {
        primaryCategory: categoryState.primaryCategory,
        selectedDocuments: categoryState.selectedDocuments,
        agentType: categoryState.agentType,
        conversationId: categoryState.conversationId,
        timestamp: categoryState.timestamp
      }
    });

  } catch (error) {
    console.error('[CATEGORY_STATE_API] Error retrieving category state:', error);
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 });
  }
}

/**
 * Clear document category state
 */
export async function DELETE(request: NextRequest) {
  try {
    // For internal category state management, we allow unauthenticated requests
    // since this is used by the system to clean up state
    const body = await request.json();
    const { userId } = body;

    if (!userId) {
      return NextResponse.json({ 
        error: 'userId is required' 
      }, { status: 400 });
    }

    // ✅ FIX: Clear the state from Firestore
    const deleteKeys = [
      userId,
      '<EMAIL>'
    ].filter(Boolean);

    console.log('[CATEGORY_STATE_API] 🗑️ Deleting category state for keys:', deleteKeys);

    // Use batch delete for atomicity
    const batch = adminDb.batch();
    let deletedCount = 0;

    for (const key of deleteKeys) {
      try {
        const docRef = adminDb.collection(CATEGORY_STATE_COLLECTION).doc(key);
        const docSnapshot = await docRef.get();
        if (docSnapshot.exists) {
          batch.delete(docRef);
          deletedCount++;
        }
      } catch (error) {
        console.error('[CATEGORY_STATE_API] Error checking document for deletion:', key, error);
      }
    }

    if (deletedCount > 0) {
      await batch.commit();
    }

    const existed = deletedCount > 0;

    console.log('[CATEGORY_STATE_API] ✅ Cleared category state:', {
      userId,
      existed
    });

    return NextResponse.json({ 
      success: true, 
      message: 'Category state cleared successfully' 
    });

  } catch (error) {
    console.error('[CATEGORY_STATE_API] Error clearing category state:', error);
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 });
  }
}
