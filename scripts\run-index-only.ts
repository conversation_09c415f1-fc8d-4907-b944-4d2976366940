import { codebaseIndexingTool } from '../lib/tools/codebase-indexing-tool';

async function main() {
  const userId = process.env.TEST_USER_ID || 'test-user-index-only';
  const projectName = process.env.TEST_PROJECT_NAME || 'index-only';
  const rootPath = process.cwd();

  try {
    console.log('🚀 Running index-only smoke test');
    console.log({ userId, projectName, rootPath });

    const result = await codebaseIndexingTool.indexCodebase({
      rootPath,
      userId,
      projectName,
      excludePatterns: [
        'node_modules', '.git', '.next', 'dist', 'build', 'coverage', 'tmp', 'temp'
      ],
      includeExtensions: ['.ts', '.tsx'],
      chunkSize: 500,
      chunkOverlap: 50,
      category: 'IndexOnly Test',
      verbose: true,
      // Limit scope to a single known file to keep this test fast and safe
      selectedPaths: ['lib/tools/storage-tool.ts'],
      onProgress: (u) => {
        const batch = u.batchIndex && u.totalBatches ? ` (${u.batchIndex}/${u.totalBatches})` : '';
        console.log(`[PROGRESS] ${u.step}${batch} - ${u.message}`);
      }
    });

    console.log('✅ Index-only result:', result);
    process.exit(result.success ? 0 : 1);
  } catch (err) {
    console.error('❌ Index-only test failed:', err);
    process.exit(1);
  }
}

main();

