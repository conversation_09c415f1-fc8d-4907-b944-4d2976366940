/**
 * createDocumentationTool.ts
 * 
 * A wrapper tool that encapsulates the entire DB Query workflow for agentic automation.
 * This tool replicates the functionality of the manual DB Query page but can be called programmatically.
 * 
 * Default configuration:
 * - Strategy: 'standalone'
 * - topK: 2
 * - Provider: 'google'
 * - Model: 'gemini-2.5-pro'
 */

import { queryCodebase, abTestQuery } from '../services/tools/db-query';
import type { QueryCodebaseRequest, StrategyRun, ABTestResult } from '../services/tools/db-query/types';
import { processWithGroq } from './groq-ai';
import { processPMODocument } from '../pmo/processPMODocument';

// Interface for the tool input
export interface CreateDocumentationToolOptions {
  userId: string;
  category: string;
  query: string;
  namespaces?: string[];
  strategy?: 'standalone' | 'content-selector' | 'two-stage' | 'hybrid-inverted';
  topK?: number;
  modelProvider?: 'openai' | 'anthropic' | 'groq' | 'google';
  modelName?: string;
  filters?: Record<string, any>;
  abTest?: boolean;
  abStrategies?: string[];
  generatePDF?: boolean;
  customTitle?: string;
  removeTechnicalMetadata?: boolean; // Default: true - Remove technical sections, keep only Answer content
}

// Interface for the tool output
export interface CreateDocumentationToolResult {
  success: boolean;
  query: string;
  category: string;
  strategy: string;
  markdownContent: string;
  rawData: StrategyRun | ABTestResult;
  generatedTitle?: string;
  pdfResult?: {
    documentId: string;
    downloadUrl: string;
  };
  error?: string;
  metrics?: {
    queryDurationMs: number;
    totalMatches: number;
    namespacesQueried: string[];
  };
}

/**
 * Format DB Query result to Markdown (replicates frontend logic)
 */
function formatDbQueryToMarkdown(payload: any, defaultModel = 'gemini-2.5-pro', removeTechnicalMetadata = true): string {
  try {
    if (!payload) return 'No data.';
    const mode = payload.mode || 'single';

    if (mode === 'single' && payload.run && payload.run.response) {
      const run = payload.run;
      const resp = run.response || {};
      const stats = resp.stats || {};
      const lines: string[] = [];

      // If removeTechnicalMetadata is true, only include the Answer section
      if (removeTechnicalMetadata) {
        if (resp.llmAnalysis) {
          lines.push(resp.llmAnalysis);
        }
        return lines.join('\n').trim() + '\n';
      }

      // Full technical output (original behavior)
      lines.push('# DB Query Result');
      lines.push('');
      lines.push(`- Mode: single`);
      if (run.strategyId) lines.push(`- Strategy: ${run.strategyId}`);
      if (stats.namespacesQueried?.length) lines.push(`- Namespaces: ${stats.namespacesQueried.join(', ')}`);
      if (typeof stats.totalMatches === 'number') lines.push(`- Total matches: ${stats.totalMatches}`);
      if (typeof stats.timingsMs?.total === 'number') lines.push(`- Total time: ${stats.timingsMs.total} ms`);
      if (resp.llmAnalysis) lines.push(`- Provider/Model: ${(resp.llmProvider || 'unknown') + ' / ' + (resp.llmModel || defaultModel)}`);

      if (resp.llmAnalysis) {
        lines.push('');
        lines.push('## Answer');
        lines.push('');
        lines.push(resp.llmAnalysis);
      }

      const results = Array.isArray(resp.results) ? resp.results.slice(0, 10) : [];
      if (results.length) {
        lines.push('');
        lines.push('## Top Matches');
        lines.push('');
        for (const r of results) {
          const path = r?.firestoreMetadata?.filePath || r?.pineconeMetadata?.filePath || r?.chunkId || '';
          const score = typeof r?.score === 'number' ? r.score.toFixed(3) : (r?.score ?? '');
          const snippet = (r?.content || '').slice(0, 280).replace(/\s+/g, ' ').trim();
          lines.push(`- ${path} — score ${score}`);
          if (snippet) {
            lines.push('');
            lines.push(`  ${snippet}`);
          }
        }
      }

      return lines.join('\n').trim() + '\n';
    }

    if (mode === 'ab' && payload.result && Array.isArray(payload.result.runs)) {
      const res = payload.result;
      const lines: string[] = [];

      // If removeTechnicalMetadata is true, only include the Answer sections
      if (removeTechnicalMetadata) {
        res.runs.forEach((run: any, idx: number) => {
          const r = run.response || {};
          if (r.llmAnalysis) {
            if (idx > 0) lines.push(''); // Add spacing between strategies
            lines.push(`**Strategy ${idx + 1} (${run.strategyId}):**`);
            lines.push('');
            lines.push(r.llmAnalysis);
          }
        });
        return lines.join('\n').trim() + '\n';
      }

      // Full technical output (original behavior)
      lines.push('# DB Query A/B Comparison');
      lines.push('');
      lines.push(`- Query: ${res.query || ''}`);
      lines.push(`- Category: ${res.category || ''}`);
      lines.push(`- Strategies: ${res.runs.map((r: any) => r.strategyId).join(', ')}`);

      res.runs.forEach((run: any, idx: number) => {
        const r = run.response || {};
        const stats = r.stats || {};
        lines.push('');
        lines.push(`## ${idx + 1}. Strategy: ${run.strategyId}`);
        if (typeof run.metrics?.durationMs === 'number') lines.push(`- Duration: ${run.metrics.durationMs} ms`);
        if (typeof run.quality?.avgScore === 'number') lines.push(`- Avg score: ${run.quality.avgScore.toFixed(3)}`);
        if (typeof run.quality?.diversity === 'number') lines.push(`- Diversity: ${run.quality.diversity}`);
        if (typeof run.quality?.coverage === 'number') lines.push(`- Coverage: ${run.quality.coverage}`);
        if (typeof stats.totalMatches === 'number') lines.push(`- Matches: ${stats.totalMatches}`);
        if (stats.namespacesQueried?.length) lines.push(`- Namespaces: ${stats.namespacesQueried.join(', ')}`);
        if (r.llmAnalysis) lines.push(`- Provider/Model: ${(r.llmProvider || 'unknown') + ' / ' + (r.llmModel || defaultModel)}`);

        if (r.llmAnalysis) {
          lines.push('');
          lines.push('### Model Answer');
          lines.push('');
          lines.push(r.llmAnalysis);
        }

        const top = Array.isArray(r.results) ? r.results.slice(0, 5) : [];
        if (top.length) {
          lines.push('');
          lines.push('### Top Matches');
          lines.push('');
          for (const hit of top) {
            const path = hit?.firestoreMetadata?.filePath || hit?.pineconeMetadata?.filePath || hit?.chunkId || '';
            const score = typeof hit?.score === 'number' ? hit.score.toFixed(3) : (hit?.score ?? '');
            const snippet = (hit?.content || '').slice(0, 200).replace(/\s+/g, ' ').trim();
            lines.push(`- ${path} — score ${score}`);
            if (snippet) {
              lines.push('');
              lines.push(`  ${snippet}`);
            }
          }
        }
      });

      return lines.join('\n').trim() + '\n';
    }

    return '```json\n' + JSON.stringify(payload, null, 2) + '\n```\n';
  } catch {
    return 'Failed to format result.';
  }
}

/**
 * Generate a concise title for the documentation
 */
async function generateTitle(query: string, category: string): Promise<string> {
  try {
    const prompt = `You are a helpful assistant that converts a user query into a concise, professional, title-cased document title.

- Keep it under 12 words (ideally 6–10).
- Use Title Case.
- Do not include quotes, emojis, brackets, or trailing punctuation.
- Avoid leading words like "Create", "Generate", "Document", "Explain".
- If a category is provided, you may incorporate it naturally.

User Query: "${query}"
Context Category: ${category}

Return only the title text.`;

    let raw = await processWithGroq({
      prompt,
      model: 'llama-3.3-70b-versatile',
      modelOptions: { temperature: 0.3, maxTokens: 128 }
    });

    // Basic cleanup: take first line, strip quotes and excessive whitespace
    raw = (raw || '').split('\n')[0].trim();
    let title = raw.replace(/^"|"$/g, '').replace(/^'|'$/g, '').replace(/[\t\r\n]+/g, ' ').trim();
    // Remove enclosing markdown formatting if any
    title = title.replace(/^#+\s*/, '').replace(/[`*]+/g, '').trim();
    // Remove trailing punctuation
    title = title.replace(/[\s]+[\-–—]+\s*$/, '').replace(/[\s]*[\.!?:]+$/, '');

    if (!title) {
      // Improved fallback: create a better title from the query
      let fallbackTitle = query.trim();

      // Remove common query prefixes
      fallbackTitle = fallbackTitle.replace(/^(explain|describe|summarize|analyze|show|find|create|generate|document|tell me about|what is|how does|how to)\s+/i, '');

      // Capitalize first letter and limit length
      fallbackTitle = fallbackTitle.charAt(0).toUpperCase() + fallbackTitle.slice(1);

      // Limit to reasonable length and add category context if available
      fallbackTitle = fallbackTitle.slice(0, 80);
      if (category && category !== 'Codebase') {
        fallbackTitle = `${category}: ${fallbackTitle}`;
      }

      title = fallbackTitle;
    }

    return title;
  } catch (error) {
    console.error('Title generation failed:', error);
    // Fallback to query-based title
    return query.trim().replace(/^[a-z]/, (c) => c.toUpperCase()).slice(0, 100);
  }
}

/**
 * Main tool function that encapsulates the entire DB Query workflow
 */
export async function createDocumentationTool(options: CreateDocumentationToolOptions): Promise<CreateDocumentationToolResult> {
  const startTime = Date.now();
  
  try {
    // Set defaults
    const {
      userId,
      category,
      query,
      namespaces,
      strategy = 'standalone',
      topK = 5,
      modelProvider = 'google',
      modelName = 'gemini-2.5-pro',
      filters = {},
      abTest = false,
      abStrategies,
      generatePDF = true,
      customTitle,
      removeTechnicalMetadata = true
    } = options;

    // Validate required parameters
    if (!userId || !category || !query) {
      throw new Error('Missing required parameters: userId, category, and query are required');
    }

    // Build the query request
    const queryRequest: QueryCodebaseRequest = {
      userId,
      category,
      query,
      namespaces,
      topKPerNamespace: topK,
      filters,
      modelProvider,
      modelName
    };

    // Execute the query
    let rawData: StrategyRun | ABTestResult;
    let payload: any;

    if (abTest) {
      rawData = await abTestQuery(queryRequest, abStrategies as any);
      payload = { mode: 'ab', result: rawData };
    } else {
      rawData = await queryCodebase(queryRequest, strategy);
      payload = { mode: 'single', run: rawData };
    }

    // Format to markdown
    const markdownContent = formatDbQueryToMarkdown(payload, modelName, removeTechnicalMetadata);

    // Extract metrics
    const metrics = abTest 
      ? {
          queryDurationMs: Date.now() - startTime,
          totalMatches: (rawData as ABTestResult).runs.reduce((sum, run) => sum + (run.response?.stats?.totalMatches || 0), 0),
          namespacesQueried: (rawData as ABTestResult).runs[0]?.response?.stats?.namespacesQueried || []
        }
      : {
          queryDurationMs: (rawData as StrategyRun).metrics?.durationMs || (Date.now() - startTime),
          totalMatches: (rawData as StrategyRun).response?.stats?.totalMatches || 0,
          namespacesQueried: (rawData as StrategyRun).response?.stats?.namespacesQueried || []
        };

    const result: CreateDocumentationToolResult = {
      success: true,
      query,
      category,
      strategy: abTest ? 'ab-test' : strategy,
      markdownContent,
      rawData,
      metrics
    };

    // Generate PDF if requested
    if (generatePDF) {
      try {
        // Generate title
        const generatedTitle = customTitle || await generateTitle(query, category);
        result.generatedTitle = generatedTitle;

        // Generate unique PMO ID
        const pmoId = (globalThis as any).crypto?.randomUUID ? (globalThis as any).crypto.randomUUID() : Math.random().toString(36).slice(2);

        // Process the document
        const pdfResult = await processPMODocument({
          title: generatedTitle,
          content: markdownContent,
          pmoId,
          userId,
          category,
          metadata: {
            source: 'DB Query Tool',
            provider: modelProvider,
            model: modelName,
            strategy: abTest ? 'ab' : strategy,
            namespaces: namespaces?.join(',') || '',
            query,
            filters: JSON.stringify(filters),
            generatedAt: new Date().toISOString(),
            isPMOGenerated: true,
            toolGenerated: true // Flag to identify tool-generated documents
          }
        });

        if (pdfResult.success && pdfResult.documentId && pdfResult.downloadUrl) {
          result.pdfResult = {
            documentId: pdfResult.documentId,
            downloadUrl: pdfResult.downloadUrl
          };
        } else {
          console.warn('PDF generation failed:', pdfResult.error);
        }
      } catch (pdfError: any) {
        console.error('PDF generation error:', pdfError);
        // Don't fail the entire operation if PDF generation fails
      }
    }

    return result;

  } catch (error: any) {
    console.error('createDocumentationTool error:', error);
    return {
      success: false,
      query: options.query,
      category: options.category,
      strategy: options.strategy || 'standalone',
      markdownContent: '',
      rawData: {} as any,
      error: error?.message || 'Unknown error occurred'
    };
  }
}

/**
 * Convenience function for simple documentation generation with defaults
 */
export async function generateDocumentation(
  userId: string,
  category: string,
  query: string,
  options?: Partial<CreateDocumentationToolOptions>
): Promise<CreateDocumentationToolResult> {
  return createDocumentationTool({
    userId,
    category,
    query,
    ...options
  });
}
