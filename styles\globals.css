@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@layer components {
  .infoText {
    @apply p-4 bg-gray-700/50 rounded-lg max-w-[300]
  }

  .bg-ike-dark-purple {
    background-color: #1e1a2e;
  }
}

.chatRow{
  @apply rounded-lg px-5 py-3 text-sm flex items-center
  justify-center space-x-2 hover:bg-gray-700/70 cursor-pointer
  text-gray-300 transition-all duration-200 ease-out

}

.current-message {
  background-color: rgba(255, 182, 193, 0.5); /* Light pink with 50% opacity */
}


.dots {
  display: inline-block;
  width: 1em;
  height: 1em;
  border-radius: 50%;
  background-color: rgb(245, 253, 11); /* Purple dots */
  margin: 0 0.1em;
  animation: dotBounce 1.4s infinite ease-in-out both;
}

.dots:nth-child(1) {
  animation-delay: -0.32s;
}

.dots:nth-child(2) {
  animation-delay: -0.16s;
}

.dots:nth-child(3) {
  animation-delay: 0;
}

.dots-container {
  background-color: transparent;
  padding: 0; /* No padding */
  display: flex;
  align-items: center;
  justify-content: center;
}


@keyframes dotBounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* Dots animation styles moved to the top */

@keyframes move-full-left-right {
  0% {
    transform: translateX(0); /* Start at the right */
  }
  20% {
    transform: translateX(-100%); /* Move to the left */
  }
  100% {
    transform: translateX(0); /* Move back to the right */
  }
}

@keyframes voiceWave {
  0% {
    opacity: 0.8;
    transform: scale(0.2);
  }
  100% {
    opacity: 0;
    transform: scale(2);
  }
}

.animate-voiceWave {
  animation: voiceWave 2s infinite;
}

.animate-voiceWave-delayed {
  animation: voiceWave 2s infinite 0.5s;
}

/* Custom scrollbar styling */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(31, 41, 55, 0.8);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(75, 85, 99, 0.8);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(107, 114, 128, 0.9);
}

.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(75, 85, 99, 0.8) rgba(31, 41, 55, 0.8);
}