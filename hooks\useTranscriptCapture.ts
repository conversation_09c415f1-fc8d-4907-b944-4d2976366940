/**
 * Hook for capturing and storing transcript state from the UI
 * This provides a reliable way to capture the actual conversation transcript
 * and make it available to webhooks via API endpoint
 */

import { useState, useCallback } from 'react';
import { useSession } from 'next-auth/react';

// Define TranscriptMessage interface locally to avoid JSX import issues
interface TranscriptMessage {
  role: 'user' | 'assistant' | 'agent';
  content: string;
  timestamp: Date;
}

interface TranscriptState {
  userId: string;
  agentId?: string;
  conversationId?: string;
  dialogue: TranscriptMessage[];
  currentResponse?: string;
  agentName?: string;
  agentType?: string;
  timestamp: number;
}

export function useTranscriptCapture() {
  const { data: session } = useSession();
  const [transcriptState, setTranscriptState] = useState<TranscriptState | null>(null);

  /**
   * Capture the current transcript state
   */
  const captureTranscriptState = useCallback(async (
    dialogue: TranscriptMessage[],
    options?: {
      agentId?: string;
      conversationId?: string;
      currentResponse?: string;
      agentName?: string;
      agentType?: string;
    }
  ) => {
    if (!session?.user?.email) {
      console.warn('[TRANSCRIPT_CAPTURE] No user session available');
      return;
    }

    const newState: TranscriptState = {
      userId: session.user.email,
      agentId: options?.agentId,
      conversationId: options?.conversationId,
      dialogue,
      currentResponse: options?.currentResponse,
      agentName: options?.agentName,
      agentType: options?.agentType,
      timestamp: Date.now()
    };

    setTranscriptState(newState);

    // Store in API for webhook access
    try {
      const response = await fetch('/api/transcript-state', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newState)
      });

      if (!response.ok) {
        console.error('[TRANSCRIPT_CAPTURE] Failed to store transcript state:', response.statusText);
      } else {
        const result = await response.json();
        console.log('[TRANSCRIPT_CAPTURE] ✅ Transcript state stored successfully:', {
          dialogueLength: dialogue.length,
          agentId: options?.agentId,
          conversationId: options?.conversationId,
          storeKeys: result.storeKeys
        });
      }
    } catch (error) {
      console.error('[TRANSCRIPT_CAPTURE] Error storing transcript state:', error);
    }
  }, [session?.user?.email]);

  /**
   * Update conversation ID when conversation starts
   */
  const updateConversationId = useCallback(async (conversationId: string) => {
    const userEmail = session?.user?.email;
    if (!userEmail) return;
    const ensuredUserEmail: string = userEmail as string;

    setTranscriptState((prevState): TranscriptState => {
      // ✅ Create initial state if none exists
      const updatedState: TranscriptState = prevState ? {
        ...prevState,
        conversationId,
        timestamp: Date.now()
      } : {
        userId: ensuredUserEmail,
        conversationId,
        dialogue: [],
        timestamp: Date.now()
      };

      // Send to API in the background
      fetch('/api/transcript-state', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updatedState)
      }).then(() => {
        console.log('[TRANSCRIPT_CAPTURE] ✅ Conversation ID updated:', conversationId);
      }).catch(error => {
        console.error('[TRANSCRIPT_CAPTURE] Error updating conversation ID:', error);
      });

      return updatedState;
    });
  }, [session?.user?.email]);

  /**
   * Update agent information
   */
  const updateAgentInfo = useCallback(async (agentId: string, agentName?: string, agentType?: string) => {
    const userEmail = session?.user?.email;
    if (!userEmail) return;
    const ensuredUserEmail: string = userEmail as string;

    setTranscriptState((prevState): TranscriptState => {
      // ✅ Create initial state if none exists
      const updatedState: TranscriptState = prevState ? {
        ...prevState,
        agentId,
        agentName,
        agentType,
        timestamp: Date.now()
      } : {
        userId: ensuredUserEmail,
        agentId,
        agentName,
        agentType,
        dialogue: [],
        timestamp: Date.now()
      };

      // Send to API in the background
      fetch('/api/transcript-state', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updatedState)
      }).then(() => {
        console.log('[TRANSCRIPT_CAPTURE] ✅ Agent info updated:', { agentId, agentName, agentType });
      }).catch(error => {
        console.error('[TRANSCRIPT_CAPTURE] Error updating agent info:', error);
      });

      return updatedState;
    });
  }, [session?.user?.email]);

  /**
   * Clear the captured transcript state
   */
  const clearTranscriptState = useCallback(async () => {
    if (!session?.user?.email) return;

    const userEmail = session.user.email;

    // Capture current state before clearing
    setTranscriptState(prevState => {
      // Send delete request in the background
      fetch('/api/transcript-state', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: userEmail,
          agentId: prevState?.agentId,
          conversationId: prevState?.conversationId
        })
      }).then(() => {
        console.log('[TRANSCRIPT_CAPTURE] ✅ Transcript state cleared');
      }).catch(error => {
        console.error('[TRANSCRIPT_CAPTURE] Error clearing transcript state:', error);
      });

      return null;
    });
  }, [session?.user?.email]);

  /**
   * Continuous capture - call this whenever the dialogue changes
   */
  const continuousCapture = useCallback(async (
    dialogue: TranscriptMessage[],
    currentResponse?: string
  ) => {
    setTranscriptState(prevState => {
      if (!prevState) return prevState;

      const updatedState = {
        ...prevState,
        dialogue,
        currentResponse,
        timestamp: Date.now()
      };

      // Throttle API calls - only update every 5 seconds or when dialogue length changes significantly
      const shouldUpdate =
        !prevState.dialogue ||
        dialogue.length !== prevState.dialogue.length ||
        Date.now() - prevState.timestamp > 5000;

      if (shouldUpdate) {
        // Send to API in the background
        fetch('/api/transcript-state', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(updatedState)
        }).then(() => {
          console.log('[TRANSCRIPT_CAPTURE] ✅ Continuous capture updated:', {
            dialogueLength: dialogue.length,
            hasCurrentResponse: !!currentResponse
          });
        }).catch(error => {
          console.error('[TRANSCRIPT_CAPTURE] Error in continuous capture:', error);
        });
      }

      return updatedState;
    });
  }, []);

  return {
    transcriptState,
    captureTranscriptState,
    updateConversationId,
    updateAgentInfo,
    clearTranscriptState,
    continuousCapture
  };
}
