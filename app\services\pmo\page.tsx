"use client";

// @refresh reset
import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import {
  RefreshCw,
  Search,
  Sparkles,
  CheckCircle,
  ChevronDown,
  ShieldAlert,
  Maximize2,
  X,
  FileText,
} from 'lucide-react';
import { simplePromptOptimizer } from '../../../lib/tools/simplePromptOptimizer';
import { useAuth } from '../../context/AuthContext';
import { usePlanner } from '../../context/PlannerContext';
import { useSession } from 'next-auth/react';
import { PMORecord, PMORecordPriority,  ModelProvider } from '../../../lib/agents/pmo/PMOInterfaces';
import { getPMORecords } from '../../../lib/firebase/pmoCollection';
import PMORecordList from '../../../components/PMO/PMORecordList';
import { Button } from '../../../components/ui/button';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '../../../components/ui/tabs';
import AgentOutputsTab from '../../../components/PMO/AgentOutputsTab';
import PMODocumentsTab from '../../../components/PMO/PMODocumentsTab';
import SystemsDocumentationTab from '../../../components/PMO/SystemsDocumentationTab';
import { CodebaseIndexingReportsTab } from '../../../components/PMO/CodebaseIndexingReportsTab';
import AgentMeetingRoom from '../../../components/PMO/AgentMeetingRoom';
import PMOAssessmentModal from 'components/PMO/PMOAssessmentModal';
import { toast } from '../../../components/ui/use-toast';
import PMOErrorBoundary from './components/PMOErrorBoundary';

// Firebase imports
import { db } from 'components/firebase';
import { collection, query, getDocs, doc, getDoc } from 'firebase/firestore';

// Investigative Research constants
import { DEFAULT_COMPARISON_MODELS, AVAILABLE_COMPARISON_MODELS, DEFAULT_SYSTEM_MODELS } from '../../../lib/agents/investigative/constants';

// Utility function to detect if current request is investigative research
const detectInvestigativeResearch = (description: string): boolean => {
  const descriptionLower = description.toLowerCase();
  return descriptionLower.includes('investigat') || descriptionLower.includes('deep dive') ||
         descriptionLower.includes('uncover') || descriptionLower.includes('expose') ||
         descriptionLower.includes('scandal') || descriptionLower.includes('corruption') ||
         descriptionLower.includes('financial fraud') || descriptionLower.includes('whistleblow') ||
         descriptionLower.includes('journalistic') || descriptionLower.includes('fact-check') ||
         descriptionLower.includes('comprehensive analysis') || descriptionLower.includes('multi-source') ||
         (descriptionLower.includes('research') && (descriptionLower.includes('thorough') || descriptionLower.includes('detailed') || descriptionLower.includes('comprehensive')));
};

// ModelProvider type is imported from PMOInterfaces

// Document interface for Firestore documents
interface Document {
  id: string;
  name: string;
  category: string;
  namespace: string;
}

// Define props for PmoFeatureInternalFormNew
interface PmoFeatureInternalFormNewProps {
  projects: any[];
  onSubmit: (data: {
    projectName: string;
    description: string;
    isInvestigativeRefined?: boolean;
    refinementMetadata?: {
      type: 'standard' | 'investigative';
      timestamp: string;
    };
  }) => void;
  onCancel: () => void;
  inlineMode?: boolean;
  isSubmitting?: boolean; // To disable form elements during assessment generation
  initialProjectName?: string;
  initialDescription?: string;
  isDocumentationMode?: boolean; // To show different labels and prompts
}

const PmoFeatureInternalFormNew: React.FC<PmoFeatureInternalFormNewProps> = ({
  projects,
  onSubmit,
  onCancel,
  inlineMode,
  isSubmitting,
  initialProjectName = '',
  initialDescription = '',
  isDocumentationMode = false,
}) => {
  const [projectName, setProjectName] = useState(initialProjectName);
  const [description, setDescription] = useState(initialDescription);
  const [isOptimizingDescription, setIsOptimizingDescription] = useState(false);
  const [isOptimized, setIsOptimized] = useState(false);
  const [isInvestigativeRefined, setIsInvestigativeRefined] = useState(false);
  const [isRefiningForInvestigation, setIsRefiningForInvestigation] = useState(false);

  useEffect(() => {
    setProjectName(initialProjectName);
  }, [initialProjectName]);

  useEffect(() => {
    setDescription(initialDescription);
  }, [initialDescription]);


  const handleDescriptionChange = (newDescription: string) => {
    setDescription(newDescription);
    setIsOptimized(false);
    setIsInvestigativeRefined(false);
  };

  const handleQuickStartClick = (promptText: string) => {
    setDescription(promptText);
    setIsOptimized(false);
    setIsInvestigativeRefined(false);
  };

  const handleOptimizeDescriptionClick = async () => {
    if (!description.trim() || isOptimizingDescription || isSubmitting) return;

    setIsOptimizingDescription(true);
    setIsOptimized(false);

    try {
      const result = await simplePromptOptimizer.optimizePrompt({
        originalPrompt: description,
        includeExplanation: false,
        modelOptions: {
          temperature: 0.7,
          maxTokens: 2000
        }
      });

      if (result.success && result.optimizedPrompt) {
        setDescription(result.optimizedPrompt);
        setIsOptimized(true);
      } else {
        console.error("Failed to optimize description or got empty result:", result.error);
        alert("Optimization failed or returned an empty result. Please try again or adjust your description.");
      }
    } catch (error) {
      console.error("Error during description optimization:", error);
      alert("An error occurred while optimizing the description. Please try again.");
    } finally {
      setIsOptimizingDescription(false);
    }
  };

  const handleRefineForInvestigationClick = async () => {
    if (!description.trim() || isRefiningForInvestigation || isSubmitting) return;

    setIsRefiningForInvestigation(true);
    setIsInvestigativeRefined(false);

    try {
      // Use the LLM comparison endpoint for investigative research optimization
      const response = await fetch('/api/llm-comparison', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'optimize',
          prompt: description,
          optimizationType: 'investigative-research',
          criteria: {
            investigativeClarity: 'Clear investigation objectives and scope',
            evidenceSpecification: 'Specific types of evidence and sources needed',
            verificationRequirements: 'Multi-source verification methodology',
            factCheckingStandards: 'Professional fact-checking protocols',
            reportingStandards: 'Investigative journalism reporting standards',
            ethicalConsiderations: 'Ethical guidelines for investigative research'
          },
          models: {
            criteriaModel: 'claude-sonnet-4-0',
            optimizationModel: 'gpt-4o',
            assessmentModel: 'claude-sonnet-4-0',
            consolidationModel: 'claude-sonnet-4-0'
          },
          comparisonModels: DEFAULT_COMPARISON_MODELS
        })
      });

      const result = await response.json();

      if (result.success && result.optimizedPrompt) {
        setDescription(result.optimizedPrompt);
        setIsInvestigativeRefined(true);
        setIsOptimized(false); // Reset regular optimization flag
      } else {
        console.error("Failed to refine for investigation:", result.error);
        alert("Investigation refinement failed. Please try again or adjust your description.");
      }
    } catch (error) {
      console.error("Error during investigative refinement:", error);
      alert("An error occurred while refining for investigation. Please try again.");
    } finally {
      setIsRefiningForInvestigation(false);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // In documentation mode, only description is required (title is optional)
    // In PMO mode, both title and description are required
    if (isDocumentationMode) {
      if (!description.trim()) {
        alert("Description is required to create documentation.");
        return;
      }
    } else {
      if (!projectName.trim() || !description.trim()) {
        alert("Project Name and Description are required.");
        return;
      }
    }

    const submissionData = {
      projectName,
      description,
      isInvestigativeRefined,
      refinementMetadata: (isInvestigativeRefined || isOptimized) ? {
        type: isInvestigativeRefined ? 'investigative' as const : 'standard' as const,
        timestamp: new Date().toISOString()
      } : undefined
    };

    onSubmit(submissionData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <label htmlFor="pmo-projectName" className="block text-sm font-medium text-gray-300 mb-1">
          {isDocumentationMode ? 'Document Title' : 'PMO Title'}
          {isDocumentationMode ? (
            <span className="text-gray-400"> (optional)</span>
          ) : (
            <span className="text-red-400">*</span>
          )}
        </label>
        <input
          type="text"
          id="pmo-projectName"
          value={projectName}
          onChange={(e) => setProjectName(e.target.value)}
          required
          className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
          placeholder={isDocumentationMode ? "Enter a title for your document" : "Enter a title for your PMO request"}
          disabled={isSubmitting}
        />
      </div>
      <div>
        <h4 className="text-sm font-medium text-gray-400 mb-2">Quick Start Prompts:</h4>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
          {(isDocumentationMode ? [
            "Launch a new internal tool for tracking employee feedback.",
            "Organize a company-wide hackathon for Q3.",
            "Research and implement a new CRM system for the sales team.",
            "Improve the onboarding process for new software engineers.",
            "Investigate potential financial irregularities in vendor contracts.",
            "Conduct comprehensive investigative research on market competitors.",
            "Deep dive analysis of industry trends and emerging threats.",
            "Conduct live analysis of industry trends and emerging threats."
          ] : [
            "Launch a new internal tool for tracking employee feedback.",
            "Organize a company-wide hackathon for Q3.",
            "Develop a marketing campaign for our new SaaS product feature.",
            "Research and implement a new CRM system for the sales team.",
            "Improve the onboarding process for new software engineers.",
            "Conduct comprehensive investigative research on market competitors.",
            "Investigate potential financial irregularities in vendor contracts.",
            "Deep dive analysis of industry trends and emerging threats."
          ]).map(promptText => (
            <Button
              key={promptText}
              type="button"
              variant="outline"
              className="text-xs text-left h-auto py-2 px-3 bg-gray-700 border-gray-600 hover:bg-gray-600/70 text-gray-300 rounded-md"
              onClick={() => handleQuickStartClick(promptText)}
              disabled={isSubmitting || isOptimizingDescription}
            >
              {promptText}
            </Button>
          ))}
        </div>
      </div>

      <div>
        <div className="flex items-center justify-between mb-1">
          <label
            htmlFor="pmo-description"
            className={`block text-sm font-medium ${
              isInvestigativeRefined
                ? 'text-blue-400'
                : isOptimized
                  ? 'text-green-400'
                  : 'text-gray-300'
            }`}
          >
            {isInvestigativeRefined
              ? 'Investigative Research Request (Converted)'
              : isOptimized
                ? 'Optimized Request'
                : isDocumentationMode
                  ? 'Refined Request Description'
                  : 'Refined Request Description'
            } <span className={
              isInvestigativeRefined
                ? 'text-blue-400'
                : isOptimized
                  ? 'text-green-400'
                  : 'text-red-400'
            }>*</span>
          </label>
          <div className="flex items-center space-x-2">
            <Button
              type="button"
              onClick={handleRefineForInvestigationClick}
              variant="ghost"
              size="sm"
              className="px-2 py-1 text-xs text-blue-400 hover:text-blue-300 hover:bg-blue-900/30 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
              disabled={!description.trim() || isRefiningForInvestigation || isSubmitting}
            >
              {isRefiningForInvestigation ? (
                <RefreshCw className="w-3 h-3 animate-spin" />
              ) : (
                <Search className="w-3 h-3" />
              )}
              <span className="ml-1">{isRefiningForInvestigation ? 'Converting...' : 'Convert to Investigation'}</span>
            </Button>
            <Button
              type="button"
              onClick={handleOptimizeDescriptionClick}
              variant="ghost"
              size="sm"
              className="px-2 py-1 text-xs text-purple-400 hover:text-purple-300 hover:bg-purple-900/30 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
              disabled={!description.trim() || isOptimizingDescription || isSubmitting}
            >
              {isOptimizingDescription ? (
                <RefreshCw className="w-3 h-3 animate-spin" />
              ) : (
                <Sparkles className="w-3 h-3" />
              )}
              <span className="ml-1">{isOptimizingDescription ? 'Optimizing...' : 'Optimize'}</span>
            </Button>
          </div>
        </div>
        <textarea
          id="pmo-description"
          value={description}
          onChange={(e) => handleDescriptionChange(e.target.value)}
          required
          rows={6}
          className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
          placeholder="Describe the task or project you need help with..."
          disabled={isSubmitting}
        />
        {isOptimizingDescription && (
          <div className="mt-2 text-xs text-purple-400 flex items-center">
            <RefreshCw className="w-3 h-3 mr-1 animate-spin" /> Optimizing your description...
          </div>
        )}
        {isRefiningForInvestigation && (
          <div className="mt-2 text-xs text-blue-400 flex items-center">
            <RefreshCw className="w-3 h-3 mr-1 animate-spin" /> Converting to investigative research format using LLM comparison...
          </div>
        )}
      </div>

      <div className="pt-2 flex justify-end space-x-3">
        <Button
          type="button"
          onClick={onCancel}
          variant="outline"
          className="px-4 py-2 border-gray-600 text-gray-300 hover:bg-gray-700 hover:text-white"
          disabled={isOptimizingDescription || isSubmitting}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white"
          disabled={
            isOptimizingDescription ||
            isSubmitting ||
            !description.trim() ||
            (!isDocumentationMode && !projectName.trim()) // Only require title in PMO mode
          }
        >
          {isSubmitting ? 'Generating...' : (isDocumentationMode ? 'Create Documentation' : 'Generate PMO Assessment')}
        </Button>
      </div>
    </form>
  );
};

interface PmoModelSelectionFeatureProps {
  availableProjects: any[];
  userId: string | undefined;
  onPmoSubmit: (data: {
    projectName: string;
    description: string;
    selectedProvider: ModelProvider;
    selectedModel: string;
    customContext?: string;
    selectedDocumentId?: string;
    selectedCategory?: string;
    generatedPmoAssessment?: string; // Added for the assessment text
    // Investigative Research Options
    isInvestigativeResearch?: boolean;
    investigativeOptions?: {
      comparisonModels?: string[];
      criteriaModel?: string;
      optimizationModel?: string;
      assessmentModel?: string;
      consolidationModel?: string;
    };
  }) => void;
  onPmoCancel: () => void;
  onRefreshRecords?: () => void; // Added for refreshing PMO records after documentation creation
}

const PmoModelSelectionFeature: React.FC<PmoModelSelectionFeatureProps> = ({
    availableProjects,
    userId,
    onPmoSubmit,
    onPmoCancel,
    onRefreshRecords
}) => {
  const providerModels: Record<ModelProvider, string[]> = {
    'openai': ['gpt-5-2025-08-07', 'gpt-4o', 'gpt-4.1-2025-04-14', 'o3-2025-04-16', 'o3-mini-2025-01-31', 'o1-mini-2024-09-12'],
    'anthropic': ['claude-sonnet-4-0', 'claude-opus-4-0'],
    'groq': ['llama-3.3-70b-versatile', 'meta-llama/llama-4-maverick-17b-128e-instruct', 'deepseek-r1-distill-llama-70b', 'openai/gpt-oss-20b', 'openai/gpt-oss-120b'],
    'google': ['gemini-1.5-pro', 'gemini-1.5-flash', 'gemini-2.5-flash', 'gemini-2.5-pro']
  };

  const [selectedProvider, setSelectedProvider] = useState<ModelProvider>('openai');
  const [selectedModel, setSelectedModel] = useState<string>(providerModels.openai[0]);
  const [showToast, setShowToast] = useState(false);

  const [customContext, setCustomContext] = useState<string>('');
  const [allUserDocuments, setAllUserDocuments] = useState<Document[]>([]);
  const [filesForDropdown, setFilesForDropdown] = useState<Document[]>([]);
  const [categoriesForDropdown, setCategoriesForDropdown] = useState<string[]>([]);

  const [selectedDocumentNamespace, setSelectedDocumentNamespace] = useState<string>('');
  const [selectedCategoryName, setSelectedCategoryName] = useState<string>('');

  const [documentSearchQuery, setDocumentSearchQuery] = useState<string>('');
  const [isDocDropdownOpen, setIsDocDropdownOpen] = useState<boolean>(false);
  const [isCatDropdownOpen, setIsCatDropdownOpen] = useState<boolean>(false);
  const [loadingDocuments, setLoadingDocuments] = useState<boolean>(true);
  const docDropdownRef = useRef<HTMLDivElement>(null);
  const catDropdownRef = useRef<HTMLDivElement>(null);

  // State for the assessment step
  const [pmoRequestData, setPmoRequestData] = useState<{ projectName: string; description: string } | null>(null);
  const [generatedAssessment, setGeneratedAssessment] = useState<string | null>(null);
  const [isGeneratingAssessment, setIsGeneratingAssessment] = useState<boolean>(false);
  const [isSubmittingRequest, setIsSubmittingRequest] = useState<boolean>(false);

  // Investigative Research Options
  const [isInvestigativeResearch, setIsInvestigativeResearch] = useState<boolean>(false);
  const [comparisonModels, setComparisonModels] = useState<string[]>(DEFAULT_COMPARISON_MODELS);
  const [criteriaModel, setCriteriaModel] = useState<string>(DEFAULT_SYSTEM_MODELS.CRITERIA);
  const [optimizationModel, setOptimizationModel] = useState<string>(DEFAULT_SYSTEM_MODELS.OPTIMIZATION);
  const [assessmentModel, setAssessmentModel] = useState<string>(DEFAULT_SYSTEM_MODELS.ASSESSMENT);
  const [consolidationModel, setConsolidationModel] = useState<string>(DEFAULT_SYSTEM_MODELS.CONSOLIDATION);
  const [assessmentError, setAssessmentError] = useState<string | null>(null);

  // State for the assessment modal
  const [isAssessmentModalOpen, setIsAssessmentModalOpen] = useState<boolean>(false);

  // State for documentation mode toggle
  const [isDocumentationMode, setIsDocumentationMode] = useState<boolean>(false);




  useEffect(() => {
    async function fetchDocumentsAndCategories() {
      if (!userId) {
        setLoadingDocuments(false);
        setAllUserDocuments([]);
        setFilesForDropdown([]);
        setCategoriesForDropdown([]);
        return;
      }
      try {
        setLoadingDocuments(true);
        const filesRef = collection(db, `users/${userId}/files`);
        const q = query(filesRef);
        const querySnapshot = await getDocs(q);

        const allDocs: Document[] = [];
        const unknownCategoryDocs: Document[] = [];
        const nonUnknownCategories = new Set<string>();

        querySnapshot.forEach((docSnap) => { // Changed doc to docSnap to avoid conflict
          const data = docSnap.data();
          const category = data.category || 'Uncategorized';
          const documentObj: Document = {
            id: docSnap.id,
            name: data.name || 'Unnamed Document',
            category: category,
            namespace: data.namespace || docSnap.id
          };

          allDocs.push(documentObj);
          if (category === 'Unknown' || category === 'Uncategorized') {
            unknownCategoryDocs.push(documentObj);
          } else {
            nonUnknownCategories.add(category);
          }
        });

        setAllUserDocuments(allDocs);
        setFilesForDropdown(unknownCategoryDocs);
        setCategoriesForDropdown(Array.from(nonUnknownCategories).sort());

      } catch (err) {
        console.error('PMO: Error fetching documents for context:', err);
        setAllUserDocuments([]);
        setFilesForDropdown([]);
        setCategoriesForDropdown([]);
      } finally {
        setLoadingDocuments(false);
      }
    }
    fetchDocumentsAndCategories();
  }, [userId]);

  useEffect(() => {
    if (documentSearchQuery.trim() === '') {
      const unknownCategoryDocs = allUserDocuments.filter(doc => doc.category === 'Unknown' || doc.category === 'Uncategorized');
      setFilesForDropdown(unknownCategoryDocs);
      return;
    }
    const queryVal = documentSearchQuery.toLowerCase();
    const filtered = allUserDocuments.filter(doc =>
      (doc.category === 'Unknown' || doc.category === 'Uncategorized') &&
      doc.name.toLowerCase().includes(queryVal)
    );
    setFilesForDropdown(filtered);
  }, [documentSearchQuery, allUserDocuments]);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (docDropdownRef.current && !docDropdownRef.current.contains(event.target as Node)) setIsDocDropdownOpen(false);
      if (catDropdownRef.current && !catDropdownRef.current.contains(event.target as Node)) setIsCatDropdownOpen(false);
    }
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);


  const handleProviderChange = (provider: ModelProvider) => {
    setSelectedProvider(provider);
    setSelectedModel(providerModels[provider][0]);
    setShowToast(true);
  };

  const handleModelChange = (model: string) => {
    setSelectedModel(model);
    setShowToast(true);
  };

  useEffect(() => {
    if (showToast) {
      const timer = setTimeout(() => setShowToast(false), 3000);
      return () => clearTimeout(timer);
    }
  }, [showToast]);

  const handleInitialFormSubmit = async (formDataFromInternalForm: {
    projectName: string;
    description: string;
    isInvestigativeRefined?: boolean;
    refinementMetadata?: {
      type: 'standard' | 'investigative';
      timestamp: string;
    };
  }) => {
    setPmoRequestData(formDataFromInternalForm);
    setGeneratedAssessment(null);
    setAssessmentError(null);
    setIsGeneratingAssessment(true);

    // Detect if this is an investigative research request
    const isInvestigativeRequest = detectInvestigativeResearch(formDataFromInternalForm.description);
    setIsInvestigativeResearch(isInvestigativeRequest);

    if (!userId) {
      setAssessmentError("User ID is missing. Cannot generate assessment.");
      setIsGeneratingAssessment(false);
      return;
    }

    try {
      const apiRequestBody = {
        userId: userId,
        title: formDataFromInternalForm.projectName,
        description: formDataFromInternalForm.description,
        // These can be made configurable in the UI if needed
        priority: 'Medium',
        category: 'PMO Request Assessment',
        contextOptions: {
          customContext: customContext.trim() || undefined,
          fileIds: selectedDocumentNamespace ? [selectedDocumentNamespace] : [],
          categoryIds: selectedCategoryName ? [selectedCategoryName] : [],
        },
        modelProvider: selectedProvider,
        modelName: selectedModel,
      };

      const response = await fetch('/api/pmo/generate-assessment', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(apiRequestBody),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to generate assessment from API');
      }

      setGeneratedAssessment(result.assessmentText);

    } catch (error: any) {
      console.error("Error calling assessment API:", error);
      setAssessmentError(`Failed to generate assessment: ${error.message}`);
    } finally {
      setIsGeneratingAssessment(false);
    }
  };

  const handleFinalSubmit = async () => {
    if (!pmoRequestData || !generatedAssessment) {
      alert("Cannot submit: initial request data or assessment is missing.");
      return;
    }

    setIsSubmittingRequest(true);
    try {
      await onPmoSubmit({
        ...pmoRequestData,
        selectedProvider: selectedProvider,
        selectedModel: selectedModel,
        customContext: customContext.trim() || undefined,
        selectedDocumentId: selectedDocumentNamespace || undefined,
        selectedCategory: selectedCategoryName || undefined,
        generatedPmoAssessment: generatedAssessment,
        isInvestigativeResearch: isInvestigativeResearch,
        investigativeOptions: isInvestigativeResearch ? {
          comparisonModels: comparisonModels,
          criteriaModel: criteriaModel,
          optimizationModel: optimizationModel,
          assessmentModel: assessmentModel,
          consolidationModel: consolidationModel
        } : undefined,
      });
    } catch (error) {
      console.error('Error submitting PMO request:', error);
      // The error handling is done in the parent component
    } finally {
      setIsSubmittingRequest(false);
    }
  };

  const handleDocumentationSubmit = async (formDataFromInternalForm: {
    projectName: string;
    description: string;
    isInvestigativeRefined?: boolean;
    refinementMetadata?: {
      type: 'standard' | 'investigative';
      timestamp: string;
    };
  }) => {
    if (!userId) {
      alert("User ID is missing. Cannot create documentation.");
      return;
    }

    setIsGeneratingAssessment(true);
    try {
      const apiRequestBody = {
        userId: userId,
        category: selectedCategoryName || 'Codebase',
        query: formDataFromInternalForm.description,
        customTitle: formDataFromInternalForm.projectName,
        strategy: 'standalone',
        topK: 5, // Default topK per namespace for comprehensive results
        filters: {}, // Default empty filters
        generatePDF: true,
        modelProvider: selectedProvider,
        modelName: selectedModel,
        removeTechnicalMetadata: false, // Keep all metadata for PMO record tracking
        // Sub-agent options (MANDATORY)
        useSubAgents: true, // Enable sub-agents for enhanced PMO documentation
        maxSubAgents: 3,
        subAgentComplexity: 'moderate'
      };

      const response = await fetch('/api/create-documentation', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(apiRequestBody),
      });

      const result = await response.json();

      if (!response.ok || !result.success) {
        throw new Error(result.error || 'Failed to create documentation');
      }

      // Build a detailed PMO assessment string for documentation runs
      const buildDocumentationAssessment = async (documentationData: any, formDataFromInternalForm: any) => {
        try {
          const rawData = documentationData.rawData;
          const mode = rawData?.runs ? 'ab' : 'single';
          const strategy = documentationData.strategy || 'standalone';
          let provider = 'unknown';
          let model = 'unknown';
          let totalMatches = 0;
          let totalTime = 0;
          let namespacesQueried: string[] = [];
          let topMatchSummary = '';

          if (mode === 'single' && rawData?.response) {
            provider = rawData.response.llmProvider || 'unknown';
            model = rawData.response.llmModel || 'unknown';
            totalMatches = rawData.response.stats?.totalMatches || 0;
            totalTime = rawData.metrics?.durationMs || 0;
            namespacesQueried = rawData.response.stats?.namespacesQueried || [];
            if (rawData.response.results && rawData.response.results.length > 0) {
              const topMatch = rawData.response.results[0];
              topMatchSummary = `Score: ${topMatch.score?.toFixed(3) || 'N/A'}, File: ${topMatch.firestoreMetadata?.filePath || topMatch.pineconeMetadata?.filePath || 'Unknown'}`;
            }
          } else if (mode === 'ab' && rawData?.runs && rawData.runs.length > 0) {
            const firstRun = rawData.runs[0];
            provider = firstRun.response?.llmProvider || 'unknown';
            model = firstRun.response?.llmModel || 'unknown';
            totalMatches = rawData.runs.reduce((sum: number, run: any) => sum + (run.response?.stats?.totalMatches || 0), 0);
            totalTime = rawData.runs.reduce((sum: number, run: any) => sum + (run.metrics?.durationMs || 0), 0);
            namespacesQueried = firstRun.response?.stats?.namespacesQueried || [];
            const bestRun = rawData.runs.find((run: any) => run.response?.results?.length > 0) || firstRun;
            if (bestRun?.response?.results && bestRun.response.results.length > 0) {
              const topMatch = bestRun.response.results[0];
              topMatchSummary = `Score: ${topMatch.score?.toFixed(3) || 'N/A'}, File: ${topMatch.firestoreMetadata?.filePath || topMatch.pineconeMetadata?.filePath || 'Unknown'}`;
            }
          }

          // Build Top Matches markdown from raw results
          let topMatchesMd = '';
          if (mode === 'single' && rawData?.response?.results?.length) {
            const top = rawData.response.results.slice(0, 10);
            topMatchesMd = top.map((r: any) => {
              const path = r?.firestoreMetadata?.filePath || r?.pineconeMetadata?.filePath || r?.chunkId || '';
              const score = typeof r?.score === 'number' ? r.score.toFixed(3) : (r?.score ?? '');
              const snippet = (r?.content || '').slice(0, 280).replace(/\s+/g, ' ').trim();
              return `- ${path} — score ${score}${snippet ? `\n\n  ${snippet}` : ''}`;
            }).join('\n');
          } else if (mode === 'ab' && rawData?.runs?.length) {
            const bestRun = rawData.runs.find((run: any) => run.response?.results?.length > 0) || rawData.runs[0];
            const top = bestRun?.response?.results?.slice(0, 10) || [];
            topMatchesMd = top.map((r: any) => {
              const path = r?.firestoreMetadata?.filePath || r?.pineconeMetadata?.filePath || r?.chunkId || '';
              const score = typeof r?.score === 'number' ? r.score.toFixed(3) : (r?.score ?? '');
              const snippet = (r?.content || '').slice(0, 280).replace(/\s+/g, ' ').trim();
              return `- ${path} — score ${score}${snippet ? `\n\n  ${snippet}` : ''}`;
            }).join('\n');
          }

          return `# Documentation Creation Report

**Document Title:** ${documentationData.generatedTitle || formDataFromInternalForm.projectName}
**Mode:** ${mode}
**Strategy:** ${strategy}
**Namespaces:** ${namespacesQueried.join(', ') || 'None'}
**Total Matches:** ${totalMatches}
**Total Time:** ${totalTime}ms
**Provider/Model:** ${provider} / ${model}
**Top Match Summary:** ${topMatchSummary || 'No matches found'}

## DB Query Result
- Mode: ${mode}
- Strategy: ${strategy}
- Namespaces: ${namespacesQueried.join(', ') || 'None'}
- Total matches: ${totalMatches}
- Total time: ${totalTime} ms
- Provider/Model: ${provider} / ${model}

## Top Matches
${topMatchesMd || 'No matches available'}

## Original Request
${formDataFromInternalForm.description}

## Documentation Summary
${documentationData.markdownContent ? documentationData.markdownContent.substring(0, 500) + '...' : 'Documentation generated successfully'}

${documentationData.pdfResult ? `## PDF Generated
- Document ID: ${documentationData.pdfResult.documentId}
- Download URL: ${documentationData.pdfResult.downloadUrl}` : ''}`;
        } catch (e) {
          return `Documentation created. Metadata unavailable due to parsing error.`;
        }
      };

      // Persist PMO tracking
      try {
        // If the agent already created a PMO record (with team assignment), do not create a duplicate.
        const existingAgentPMOId = result.pmoRecord?.pmoRecordId || result.data?.pmoRecord?.pmoRecordId;
        if (existingAgentPMOId) {
          // Optionally enrich the existing record with a detailed assessment
          const detailedAssessment = await buildDocumentationAssessment(result.data, formDataFromInternalForm);
          await fetch('/api/pmo-record-update', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              recordId: existingAgentPMOId,
              pmoAssessment: detailedAssessment
            })
          });
          console.log('Updated existing PMO record created by agent:', existingAgentPMOId);
        } else {
          // Fall back: create a PMO record from the client if the agent did not create one
          await savePMORecordFromDocumentation(result.data, formDataFromInternalForm);
          console.log('PMO record saved successfully after documentation creation');
        }
      } catch (pmoError) {
        console.error('Failed to persist PMO tracking, but documentation was created:', pmoError);
        // Don't fail the entire operation if PMO persistence fails
      }

      toast({
        title: "Documentation Created Successfully!",
        description: `Your document "${formDataFromInternalForm.projectName}" has been created and saved.`,
        variant: "default"
      });

      // Reset form
      handleInternalFormCancel();


      // Refresh PMO records to show the new record
      if (onRefreshRecords) {
        console.log('Refreshing PMO records after documentation creation');
        onRefreshRecords();
      }

    } catch (error: any) {
      console.error("Error creating documentation:", error);
      toast({
        title: "Error",
        description: error.message || "Failed to create documentation. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsGeneratingAssessment(false);
    }
  };

  const savePMORecordFromDocumentation = async (documentationData: any, formData: any) => {
    try {
      console.log('Saving PMO record for documentation:', { documentationData, formData });

      // Extract metadata from the documentation result
      const rawData = documentationData.rawData;
      const metrics = documentationData.metrics;

      // Determine mode and strategy
      const mode = rawData?.runs ? 'ab' : 'single';
      const strategy = documentationData.strategy || 'standalone';

      // Extract provider/model information
      let provider = 'unknown';
      let model = 'unknown';
      let totalMatches = 0;
      let totalTime = 0;
      let namespacesQueried: string[] = [];
      let topMatchSummary = '';

      if (mode === 'single' && rawData?.response) {
        // Single strategy run
        provider = rawData.response.llmProvider || 'unknown';
        model = rawData.response.llmModel || 'unknown';
        totalMatches = rawData.response.stats?.totalMatches || 0;
        totalTime = rawData.metrics?.durationMs || 0;
        namespacesQueried = rawData.response.stats?.namespacesQueried || [];

        // Get top match summary
        if (rawData.response.results && rawData.response.results.length > 0) {
          const topMatch = rawData.response.results[0];
          topMatchSummary = `Score: ${topMatch.score?.toFixed(3) || 'N/A'}, File: ${topMatch.firestoreMetadata?.filePath || topMatch.pineconeMetadata?.filePath || 'Unknown'}`;
        }
      } else if (mode === 'ab' && rawData?.runs && rawData.runs.length > 0) {
        // A/B test run - use the first run's data
        const firstRun = rawData.runs[0];
        provider = firstRun.response?.llmProvider || 'unknown';
        model = firstRun.response?.llmModel || 'unknown';
        totalMatches = rawData.runs.reduce((sum: number, run: any) => sum + (run.response?.stats?.totalMatches || 0), 0);
        totalTime = rawData.runs.reduce((sum: number, run: any) => sum + (run.metrics?.durationMs || 0), 0);
        namespacesQueried = firstRun.response?.stats?.namespacesQueried || [];

        // Get top match summary from best run
        const bestRun = rawData.runs.find((run: any) => run.response?.results?.length > 0) || firstRun;
        if (bestRun?.response?.results && bestRun.response.results.length > 0) {
          const topMatch = bestRun.response.results[0];
          topMatchSummary = `Score: ${topMatch.score?.toFixed(3) || 'N/A'}, File: ${topMatch.firestoreMetadata?.filePath || topMatch.pineconeMetadata?.filePath || 'Unknown'}`;
        }
      }

      // Create detailed PMO assessment with all metadata
      // Build Top Matches markdown from raw results
      let topMatchesMd = '';
      if (mode === 'single' && rawData?.response?.results?.length) {
        const top = rawData.response.results.slice(0, 10);
        topMatchesMd = top.map((r: any) => {
          const path = r?.firestoreMetadata?.filePath || r?.pineconeMetadata?.filePath || r?.chunkId || '';
          const score = typeof r?.score === 'number' ? r.score.toFixed(3) : (r?.score ?? '');
          const snippet = (r?.content || '').slice(0, 280).replace(/\s+/g, ' ').trim();
          return `- ${path} — score ${score}${snippet ? `\n\n  ${snippet}` : ''}`;
        }).join('\n');
      } else if (mode === 'ab' && rawData?.runs?.length) {
        const bestRun = rawData.runs.find((run: any) => run.response?.results?.length > 0) || rawData.runs[0];
        const top = bestRun?.response?.results?.slice(0, 10) || [];
        topMatchesMd = top.map((r: any) => {
          const path = r?.firestoreMetadata?.filePath || r?.pineconeMetadata?.filePath || r?.chunkId || '';
          const score = typeof r?.score === 'number' ? r.score.toFixed(3) : (r?.score ?? '');
          const snippet = (r?.content || '').slice(0, 280).replace(/\s+/g, ' ').trim();
          return `- ${path} — score ${score}${snippet ? `\n\n  ${snippet}` : ''}`;
        }).join('\n');
      }

      const pmoAssessment = `# Documentation Creation Report

**Document Title:** ${documentationData.generatedTitle || formData.projectName}
**Mode:** ${mode}
**Strategy:** ${strategy}
**Namespaces:** ${namespacesQueried.join(', ') || 'None'}
**Total Matches:** ${totalMatches}
**Total Time:** ${totalTime}ms
**Provider/Model:** ${provider} / ${model}
**Top Match Summary:** ${topMatchSummary || 'No matches found'}

## DB Query Result
- Mode: ${mode}
- Strategy: ${strategy}
- Namespaces: ${namespacesQueried.join(', ') || 'None'}
- Total matches: ${totalMatches}
- Total time: ${totalTime} ms
- Provider/Model: ${provider} / ${model}

## Top Matches
${topMatchesMd || 'No matches available'}

## Original Request
${formData.description}

## Documentation Summary
${documentationData.markdownContent ? documentationData.markdownContent.substring(0, 500) + '...' : 'Documentation generated successfully'}

${documentationData.pdfResult ? `## PDF Generated
- Document ID: ${documentationData.pdfResult.documentId}
- Download URL: ${documentationData.pdfResult.downloadUrl}` : ''}`;

      const pmoRecord = {
        title: documentationData.generatedTitle || formData.projectName,
        description: formData.description,
        priority: 'Medium' as const,
        category: selectedCategoryName || 'Codebase',
        customContext: customContext.trim() || undefined,
        selectedFileId: selectedDocumentNamespace || undefined,
        selectedCategory: selectedCategoryName || undefined,
        pmoAssessment: pmoAssessment
      };

      console.log('PMO record to save:', pmoRecord);

      const response = await fetch('/api/pmo-record', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ formData: pmoRecord }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Failed to save PMO record for documentation:', errorText);
        throw new Error('Failed to save PMO record for documentation');
      }

      const result = await response.json();
      console.log('PMO record saved successfully:', result);
    } catch (error) {
      console.error('Error saving PMO record for documentation:', error);
      // Re-throw the error so it can be caught by the calling function
      throw error;
    }
  };

  const handleInternalFormCancel = () => {
    if (generatedAssessment || isGeneratingAssessment || assessmentError) {
        setGeneratedAssessment(null);
        // pmoRequestData is kept so user doesn't lose title/description if they want to re-trigger assessment
        setAssessmentError(null);
        setIsGeneratingAssessment(false); // Stop loading if it was active
    } else {
        onPmoCancel(); // Full cancel back to records list
    }
  };

  const handleDocumentSelect = (docName: string) => {
    const selectedDoc = allUserDocuments.find(d => d.name === docName && (d.category === 'Unknown' || d.category === 'Uncategorized'));
    if (selectedDoc) {
      setSelectedDocumentNamespace(selectedDoc.namespace);
      setSelectedCategoryName(''); // Mutually exclusive
    } else {
      setSelectedDocumentNamespace('');
    }
    setIsDocDropdownOpen(false);
  };

  const handleCategorySelect = (categoryName: string) => {
    if (categoryName) {
      setSelectedCategoryName(categoryName);
      setSelectedDocumentNamespace(''); // Mutually exclusive
    } else {
      setSelectedCategoryName('');
    }
    setIsCatDropdownOpen(false);
  };


  useEffect(() => {
    const styleSheetId = 'custom-animations-toast-pmo-page';
    if (!document.getElementById(styleSheetId)) {
      const style = document.createElement('style');
      style.id = styleSheetId;
      style.innerHTML = `
        @keyframes fadeInPmoPage { 0% { opacity: 0; transform: translateY(-10px); } 100% { opacity: 1; transform: translateY(0); } }
        .animate-fadeInPmoPage { animation: fadeInPmoPage 0.3s ease-out forwards; }
      `;
      document.head.appendChild(style);
    }
  }, []);

  return (
    <div className="bg-gray-800 rounded-lg shadow-lg">
      {showToast && (
        <div className="fixed top-5 right-5 z-[100] flex items-center p-4 bg-green-600 text-white rounded-lg shadow-xl animate-fadeInPmoPage">
          <CheckCircle className="w-5 h-5 mr-3 flex-shrink-0" />
          <span>Model updated to {selectedProvider.charAt(0).toUpperCase() + selectedProvider.slice(1)} / {selectedModel}</span>
        </div>
      )}
      <div className="p-6 border-b border-gray-700">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-4">
          <h3 className="text-lg font-medium text-purple-300">Model Selection for PMO Request</h3>
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-300">Using:</span>
            <span className="bg-purple-700 text-white text-sm px-2 py-1 rounded-md">
              {selectedProvider.charAt(0).toUpperCase() + selectedProvider.slice(1)} / {selectedModel}
            </span>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-gray-900/50 p-3 rounded-lg border border-gray-700/50">
            <h4 className="text-sm font-medium mb-2 text-purple-400">Provider</h4>
            <div className="flex flex-wrap gap-2">
              {(Object.keys(providerModels) as ModelProvider[]).map((provider) => (
                <button key={provider} onClick={() => handleProviderChange(provider)}
                  className={`px-3 py-1 text-xs rounded transition-colors ${selectedProvider === provider ? 'bg-purple-600 text-white' : 'bg-gray-700 hover:bg-gray-600 text-gray-200'}`}
                  disabled={isGeneratingAssessment || !!generatedAssessment}
                >
                  {provider.charAt(0).toUpperCase() + provider.slice(1)}
                </button>
              ))}
            </div>
          </div>
          <div className="bg-gray-900/50 p-3 rounded-lg border border-gray-700/50">
            <h4 className="text-sm font-medium mb-2 text-purple-400">Model</h4>
            <div className="flex flex-wrap gap-2 max-h-24 overflow-y-auto custom-scrollbar-pmo-page pr-1">
              {providerModels[selectedProvider].map((model) => (
                <button key={model} onClick={() => handleModelChange(model)}
                  className={`px-3 py-1 text-xs rounded transition-colors ${selectedModel === model ? 'bg-purple-600 text-white' : 'bg-[#2d2a3a] hover:bg-[#3d2e5a] text-gray-200'}`}
                  disabled={isGeneratingAssessment || !!generatedAssessment}
                >
                  {model}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      <div className="p-6">
        {/* Mode Toggle */}
        <div className="mb-6">
          <div className="flex items-center justify-center">
            <div className="bg-gray-800 p-1 rounded-lg border border-gray-600">
              <button
                onClick={() => setIsDocumentationMode(false)}
                className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                  !isDocumentationMode
                    ? 'bg-purple-600 text-white'
                    : 'text-gray-300 hover:text-white hover:bg-gray-700'
                }`}
                disabled={isGeneratingAssessment || !!generatedAssessment}
              >
                PMO Assessment
              </button>
              <button
                onClick={() => setIsDocumentationMode(true)}
                className={`mx-2 px-4 py-2 text-sm font-medium mr-2 rounded-md transition-colors ${
                  isDocumentationMode
                    ? 'bg-black text-white'
                    : 'text-gray-300 hover:text-white hover:bg-gray-700'
                }`}
                disabled={isGeneratingAssessment || !!generatedAssessment}
              >
                Create Documentation
              </button>
            </div>
          </div>
        </div>

        <div className="flex items-center mb-1">
            <h2 className="text-xl font-semibold text-purple-200">
            {isDocumentationMode ? 'Create Documentation' : 'New PMO Request'} ({selectedProvider.charAt(0).toUpperCase() + selectedProvider.slice(1)} - {selectedModel})
            </h2>
        </div>
        <p className="text-sm text-gray-400 mb-6">
          {isDocumentationMode
            ? 'Fill in the details below to create documentation using the codebase query system.'
            : 'Fill in the details below to create a new PMO request.'
          }
        </p>

        <div className="mb-8">
          <h3 className="text-lg font-medium mb-3 text-purple-300">Context Options</h3>
          <p className="text-xs text-gray-400 mb-3">
            All context options are optional. You can provide custom context and combine it with either a document OR a category selection. Document and category selections are mutually exclusive.
          </p>
          <div className="bg-gray-900/50 rounded-lg p-4 border border-gray-700/50 space-y-4">
            <div>
              <label htmlFor="pmo-customContext" className="block text-sm font-medium text-gray-300 mb-1">Custom Context (Optional)</label>
              <textarea
                id="pmo-customContext"
                value={customContext}
                onChange={(e) => setCustomContext(e.target.value)}
                className="w-full p-3 bg-gray-700 text-gray-200 border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                rows={3}
                placeholder="Enter custom context for the agent (e.g., specific requirements, background info)..."
                disabled={isGeneratingAssessment || !!generatedAssessment}
              />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="relative" ref={docDropdownRef}>
                <label className="block text-sm font-medium text-gray-300 mb-1">Files</label>
                <button type="button"
                  className="flex items-center justify-between w-full p-3 bg-gray-700 border border-gray-600 rounded-md cursor-pointer text-left disabled:opacity-70 disabled:cursor-not-allowed"
                  onClick={() => setIsDocDropdownOpen(!isDocDropdownOpen)}
                  aria-haspopup="listbox"
                  aria-expanded={isDocDropdownOpen}
                  disabled={isGeneratingAssessment || !!generatedAssessment}
                >
                  <span className={`truncate ${selectedDocumentNamespace ? "text-white" : "text-gray-400"}`}>
                    {allUserDocuments.find(d => d.namespace === selectedDocumentNamespace)?.name || "Select a file (optional)"}
                  </span>
                  <ChevronDown className={`w-5 h-5 text-gray-400 transition-transform flex-shrink-0 ${isDocDropdownOpen ? 'rotate-180' : ''}`} />
                </button>
                {isDocDropdownOpen && (
                  <div className="absolute z-20 w-full mt-1 bg-gray-700 border border-gray-600 rounded-md shadow-lg max-h-60 overflow-auto custom-scrollbar-pmo-page">
                    <div className="sticky top-0 bg-gray-800 p-2 border-b border-gray-600 z-10">
                      <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                        <input type="text" placeholder="Search documents..." value={documentSearchQuery} onChange={(e) => setDocumentSearchQuery(e.target.value)}
                          className="w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-purple-500 text-sm" />
                      </div>
                    </div>
                    <div className="py-1">
                      <div className="px-4 py-2 text-xs text-amber-400 border-b border-gray-600">
                        Showing only files with category 'Unknown' or 'Uncategorized'
                      </div>
                      <ul role="listbox" className="py-1">
                        {loadingDocuments ? <li className="px-4 py-3 text-gray-400 text-center text-sm">Loading files...</li>
                          : filesForDropdown.length > 0 ? filesForDropdown.map((doc) => (
                            <li key={doc.id} role="option" aria-selected={selectedDocumentNamespace === doc.namespace}
                                className="px-4 py-2 hover:bg-purple-900/50 cursor-pointer text-sm" onClick={() => handleDocumentSelect(doc.name)}>
                              <div className="font-medium truncate" title={doc.name}>{doc.name}</div>
                              <div className="text-xs text-gray-400">{doc.category}</div>
                            </li>))
                          : <li className="px-4 py-3 text-gray-400 text-center text-sm">No files found</li>}
                      </ul>
                    </div>
                  </div>
                )}
              </div>
              <div className="relative" ref={catDropdownRef}>
                <label className="block text-sm font-medium text-gray-300 mb-1">Categories</label>
                <button type="button"
                  className="flex items-center justify-between w-full p-3 bg-gray-700 border border-gray-600 rounded-md cursor-pointer text-left disabled:opacity-70 disabled:cursor-not-allowed"
                  onClick={() => setIsCatDropdownOpen(!isCatDropdownOpen)}
                  aria-haspopup="listbox"
                  aria-expanded={isCatDropdownOpen}
                  disabled={isGeneratingAssessment || !!generatedAssessment}
                >
                  <span className={`truncate ${selectedCategoryName ? "text-white" : "text-gray-400"}`}>{selectedCategoryName || "Select a category (optional)"}</span>
                  <ChevronDown className={`w-5 h-5 text-gray-400 transition-transform flex-shrink-0 ${isCatDropdownOpen ? 'rotate-180' : ''}`} />
                </button>
                {isCatDropdownOpen && (
                  <div className="absolute z-20 w-full mt-1 bg-gray-700 border border-gray-600 rounded-md shadow-lg max-h-60 overflow-auto custom-scrollbar-pmo-page">
                    <div className="py-1">
                      <div className="px-4 py-2 text-xs text-amber-400 border-b border-gray-600">
                        Showing all categories except 'Unknown'/'Uncategorized'
                      </div>
                      <ul role="listbox" className="py-1">
                        {loadingDocuments ? <li className="px-4 py-3 text-gray-400 text-center text-sm">Loading categories...</li>
                          : categoriesForDropdown.length > 0 ? categoriesForDropdown.map((category, index) => (
                            <li key={index} role="option" aria-selected={selectedCategoryName === category}
                                className="px-4 py-2 hover:bg-purple-900/50 cursor-pointer text-sm" onClick={() => handleCategorySelect(category)}>
                              <div className="font-medium truncate" title={category}>{category}</div>
                              <div className="text-xs text-gray-400">{allUserDocuments.filter(doc => doc.category === category).length} file(s)</div>
                            </li>))
                          : <li className="px-4 py-3 text-gray-400 text-center text-sm">No categories available</li>}
                      </ul>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {!generatedAssessment && !isGeneratingAssessment && !assessmentError && (
          <PmoFeatureInternalFormNew
            projects={availableProjects}
            onSubmit={isDocumentationMode ? handleDocumentationSubmit : handleInitialFormSubmit}
            onCancel={handleInternalFormCancel}
            inlineMode={true}
            isSubmitting={isGeneratingAssessment}
            initialProjectName={pmoRequestData?.projectName || ''}
            initialDescription={pmoRequestData?.description || ''}
            isDocumentationMode={isDocumentationMode}
          />
        )}
         {isGeneratingAssessment && (
          <div className="mt-6 p-6 bg-gray-900/50 rounded-lg border border-gray-700/50 text-center">
            <RefreshCw className="w-8 h-8 animate-spin text-purple-400 mx-auto mb-3" />
            <p className="text-lg font-medium text-gray-300">Generating PMO Assessment...</p>
            <p className="text-sm text-gray-400">The PMO Assessment Agent is analyzing your request and context. This may take a moment.</p>
          </div>
        )}

        {assessmentError && !isGeneratingAssessment && (
            <div className="mt-6 p-4 bg-red-900/30 border border-red-700 rounded-lg text-red-300">
                <h4 className="font-semibold mb-2">Assessment Generation Failed</h4>
                <p className="text-sm">{assessmentError}</p>
                <Button
                    onClick={handleInternalFormCancel} // This will reset error and allow re-entry to form
                    variant="outline"
                    className="mt-3 text-xs border-red-500 hover:bg-red-800/50 text-red-200 hover:text-white"
                >
                    Edit Request and Try Again
                </Button>
            </div>
        )}

        {generatedAssessment && !isGeneratingAssessment && pmoRequestData && (
          <div className="mt-8 pt-6 border-t border-gray-700">
            <h3 className="text-xl font-semibold text-purple-200 mb-3">Generated PMO Assessment & Requirements</h3>
            <p className="text-sm text-gray-400 mb-4">
              Review the assessment generated by the PMO Assessment Agent. If it looks good, submit the request.
              You can also edit your initial request details by clicking "Edit Request".
            </p>
            <div className="relative">
              <textarea
                readOnly
                value={generatedAssessment}
                className="w-full p-3 bg-gray-700 text-gray-200 border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 h-64 custom-scrollbar-pmo-page cursor-pointer"
                rows={15}
                onClick={() => setIsAssessmentModalOpen(true)}
              />
              <button
                className="absolute top-2 right-2 p-1.5 bg-gray-800 hover:bg-gray-700 rounded-md transition-colors"
                onClick={() => setIsAssessmentModalOpen(true)}
                title="Expand Assessment"
              >
                <Maximize2 className="h-4 w-4 text-purple-300" />
              </button>
            </div>

            {/* Assessment Modal */}
            <PMOAssessmentModal
              isOpen={isAssessmentModalOpen}
              onClose={() => setIsAssessmentModalOpen(false)}
              title={pmoRequestData?.projectName || "PMO Assessment"}
              content={generatedAssessment || ""}
              onEdit={handleInternalFormCancel}
              onSubmit={handleFinalSubmit}
              isSubmitting={isSubmittingRequest}
            />

            {/* Investigative Research Configuration */}
            {isInvestigativeResearch && (
              <div className="mt-6 p-4 bg-blue-900/20 border border-blue-700 rounded-lg">
                <h4 className="text-lg font-medium text-blue-300 mb-3 flex items-center">
                  <Search className="mr-2 h-5 w-5" />
                  Investigative Research Configuration
                </h4>
                <p className="text-sm text-blue-200 mb-4">
                  This request has been identified as investigative research. Configure the specialized models for optimal results.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-blue-300 mb-2">
                      LLM Comparison Models
                    </label>
                    <div className="space-y-2">
                      {AVAILABLE_COMPARISON_MODELS.map(model => (
                        <label key={model} className="flex items-center">
                          <input
                            type="checkbox"
                            checked={comparisonModels.includes(model)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setComparisonModels([...comparisonModels, model]);
                              } else {
                                setComparisonModels(comparisonModels.filter(m => m !== model));
                              }
                            }}
                            className="mr-2 text-blue-600"
                          />
                          <span className="text-sm text-blue-200">{model}</span>
                        </label>
                      ))}
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-blue-300 mb-1">
                        Criteria Model
                      </label>
                      <select
                        value={criteriaModel}
                        onChange={(e) => setCriteriaModel(e.target.value)}
                        className="w-full px-3 py-2 bg-gray-700 border border-blue-600 rounded-md text-white"
                      >
                        <option value="claude-sonnet-4-0">Claude Sonnet 4.0</option>
                        <option value="o3-2025-04-16">o3-2025-04-16</option>
                        <option value="gemini-2.5-pro">Gemini 2.5 Pro</option>
                          <option value="deepseek-r1-distill-llama-70b">deepseek-r1</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-blue-300 mb-1">
                        Optimization Model
                      </label>
                      <select
                        value={optimizationModel}
                        onChange={(e) => setOptimizationModel(e.target.value)}
                        className="w-full px-3 py-2 bg-gray-700 border border-blue-600 rounded-md text-white"
                      >
                        <option value="gpt-4.1-2025-04-14">gpt-4.1-2025-04-14</option>
                        <option value="claude-opus-4-0">Claude Opus 4.0</option>
                        <option value="gemini-2.5-pro">Gemini 2.5 Pro</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-blue-300 mb-1">
                        Assessment Model
                      </label>
                      <select
                        value={assessmentModel}
                        onChange={(e) => setAssessmentModel(e.target.value)}
                        className="w-full px-3 py-2 bg-gray-700 border border-blue-600 rounded-md text-white"
                      >
                        <option value="claude-sonnet-4-0">Claude Sonnet 4.0</option>
                        <option value="o3-2025-04-16">o3-2025-04-16</option>
                        <option value="gemini-2.5-pro">Gemini 2.5 Pro</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-blue-300 mb-1">
                        Consolidation Model
                      </label>
                      <select
                        value={consolidationModel}
                        onChange={(e) => setConsolidationModel(e.target.value)}
                        className="w-full px-3 py-2 bg-gray-700 border border-blue-600 rounded-md text-white"
                      >
                        <option value="claude-sonnet-4-0">Claude Sonnet 4.0</option>
                        <option value="o3-2025-04-16">o3-2025-04-16</option>
                        <option value="gemini-2.5-pro">Gemini 2.5 Pro</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            )}

            <div className="mt-6 pt-2 flex justify-end space-x-3">
              <Button
                type="button"
                onClick={handleInternalFormCancel}
                variant="outline"
                className="px-4 py-2 border-gray-600 text-gray-300 hover:bg-gray-700 hover:text-white"
              >
                Edit Request
              </Button>
              <Button
                type="button"
                onClick={handleFinalSubmit}
                disabled={isSubmittingRequest}
                className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
              >
                {isSubmittingRequest ? (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                    Submitting...
                  </>
                ) : (
                  'Submit PMO Request'
                )}
              </Button>
            </div>
          </div>
        )}
      </div>
       <style jsx global>{`
        .custom-scrollbar-pmo-page::-webkit-scrollbar { width: 6px; }
        .custom-scrollbar-pmo-page::-webkit-scrollbar-track { background: #1f2937; border-radius: 10px; }
        .custom-scrollbar-pmo-page::-webkit-scrollbar-thumb { background: #4b5563; border-radius: 10px; }
        .custom-scrollbar-pmo-page::-webkit-scrollbar-thumb:hover { background: #6b7280; }
      `}</style>
    </div>
  );
};

async function checkPMOAccess(email: string): Promise<boolean> {
    console.log(`Checking PMO access for ${email}...`);
    if (!email) return false;

    // --- PRODUCTION AUTHORIZATION LOGIC ---
    try {
      const userDocRef = doc(db, "pmo_access", email);
      const userDocSnap = await getDoc(userDocRef);
      if (userDocSnap.exists()) {
        const data = userDocSnap.data();
        if (data && data.hasAccess === true) {
          console.log(`PMO Access granted for ${email} based on Firestore record.`);
          return true;
        }
      }
    } catch (error) {
      console.error("Error checking PMO access in Firestore:", error);
    }
    // --- END PRODUCTION AUTHORIZATION LOGIC ---

    // For DEVELOPMENT/TESTING ONLY:
    console.warn(`PMOPage: DEVELOPMENT MODE - PMO access granted by default for ${email}. Ensure this is changed for production.`);
    return true; // Remove or secure this for production
}


export default function PMOPage() {
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();
  const { projects, loading: plannerLoading } = usePlanner();
  const { data: session } = useSession();

  const [pmoRecords, setPMORecords] = useState<PMORecord[]>([]);
  const [isLoadingRecords, setIsLoadingRecords] = useState(false); // Start false, will be set true when fetching
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [priorityFilter, setPriorityFilter] = useState<PMORecordPriority | 'all'>('all');

  // This state now controls if the form OR the records list is shown within the "records" tab
  // Start with records list for better initial loading experience
  const [showCreationForm, setShowCreationForm] = useState(false);


  const [isCheckingAuth, setIsCheckingAuth] = useState(true);
  const [isAuthorized, setIsAuthorized] = useState(false);

  // Default to the "records" tab with the creation form visible
  const [activeTab, setActiveTab] = useState("records");

  // Deep-link support: switch tab via ?tab=documents|records|outputs|systems-docs|meeting
  useEffect(() => {
    try {
      const sp = new URLSearchParams(window.location.search);
      const tab = sp.get('tab');
      if (tab && ['records','documents','outputs','systems-docs','meeting'].includes(tab)) {
        setActiveTab(tab);
      }
    } catch {}
  }, []);


  // Sliding panel state for Codebase Indexing Reports
  const [isReportsPanelOpen, setIsReportsPanelOpen] = useState(false);


  useEffect(() => {
    const authCheck = async () => {
      setIsCheckingAuth(true);
      if (authLoading) return;
      if (!user) {
        console.log("PMOPage: User not authenticated, redirecting to login.");
        router.push('/login');
        return;
      }
      try {
        console.log(`PMOPage: User authenticated (${user.email}), checking PMO authorization...`);
        const hasAccess = await checkPMOAccess(user.email as string);
        setIsAuthorized(hasAccess);
        if (!hasAccess) console.log(`PMOPage: User ${user.email} is NOT authorized for PMO.`);
        else console.log(`PMOPage: User ${user.email} IS authorized for PMO.`);
      } catch (err) {
        console.error('PMOPage: Error during PMO authorization check:', err);
        setIsAuthorized(false);
      } finally {
        setIsCheckingAuth(false);
      }
    };
    authCheck();
  }, [user, authLoading, router]);


  const fetchPMORecords = useCallback(async () => {
    if (!user || !user.email) {
        console.warn("fetchPMORecords: User not available to fetch records.");
        setError("User not available to fetch records.");
        setIsLoadingRecords(false);
        return;
    }

    console.log(`fetchPMORecords: Starting fetch for user ${user.email}`);
    setIsLoadingRecords(true);
    setError(null);

    try {
      // Add a timeout to prevent infinite loading
      const timeoutPromise = new Promise<PMORecord[]>((_, reject) => {
        setTimeout(() => reject(new Error('Request timed out after 15 seconds')), 15000);
      });

      // Race between the actual fetch and the timeout
      const records = await Promise.race([
        getPMORecords(user.email as string),
        timeoutPromise
      ]);

      console.log(`fetchPMORecords: Successfully fetched ${records.length} records`);
      setPMORecords(records);
    } catch (err: any) {
      console.error('Error fetching PMO records:', err);
      setError(err.message || 'Failed to fetch PMO records');
      setPMORecords([]);
    } finally {
      setIsLoadingRecords(false);
    }
  }, [user]);

  useEffect(() => {
    // Fetch records when user is authorized and on records tab
    // This will pre-load data even when showing creation form for better UX
    if (isAuthorized && !authLoading && user && activeTab === "records") {
      console.log("useEffect: Conditions met for fetching PMO records");

      // Only fetch if we don't have records and aren't already loading
      if (pmoRecords.length === 0 && !isLoadingRecords && !error) {
        console.log("useEffect: Triggering fetchPMORecords");
        fetchPMORecords();
      } else {
        console.log("useEffect: Records already exist or loading, skipping fetch");
      }
    }
  }, [authLoading, user, activeTab, isAuthorized]); // Removed fetchPMORecords to prevent unnecessary re-renders

  // Memoize event handlers to prevent recreation on every render
  const handleResearchCompleted = useCallback((event: CustomEvent) => {
    const { success, requestId, pmoId, error } = event.detail;

    console.log(`[PMO Page] Research completed event received:`, event.detail);

    if (success) {
      console.log(`[PMO Page] Research processing completed successfully - switching to PMO Output tab`);
      setActiveTab('outputs');

      toast({
        title: "Research Completed",
        description: `Research analysis has been completed successfully. Switching to PMO Output tab to view results.`,
      });
    } else {
      console.log(`[PMO Page] Research processing failed:`, error);
      toast({
        title: "Research Processing Failed",
        description: error || "Research processing encountered an error. Please check the details and try again.",
        variant: "destructive"
      });
    }
  }, [toast]);

  const handleMarketingCompleted = useCallback((event: CustomEvent) => {
    const { success, requestId, pmoId, error } = event.detail;

    console.log(`[PMO Page] Marketing completed event received:`, event.detail);

    if (success) {
      console.log(`[PMO Page] Marketing processing completed successfully - switching to PMO Output tab`);
      setActiveTab('outputs');

      toast({
        title: "Marketing Analysis Completed",
        description: `Marketing strategy analysis has been completed successfully. Switching to PMO Output tab to view results.`,
      });
    } else {
      console.log(`[PMO Page] Marketing processing failed:`, error);
      toast({
        title: "Marketing Processing Failed",
        description: error || "Marketing processing encountered an error. Please check the details and try again.",
        variant: "destructive"
      });
    }
  }, [toast]);

  const handleInvestigativeResearchCompleted = useCallback((event: CustomEvent) => {
    const { success, requestId, pmoId, error } = event.detail;

    console.log(`[PMO Page] Investigative research completed event received:`, event.detail);

    if (success) {
      console.log(`[PMO Page] Investigative research processing completed successfully - switching to PMO Output tab`);
      setActiveTab('outputs');

      toast({
        title: "Investigative Research Completed",
        description: `Investigative research analysis has been completed successfully. Switching to PMO Output tab to view results.`,
      });
    } else {
      console.log(`[PMO Page] Investigative research processing failed:`, error);
      toast({
        title: "Investigative Research Failed",
        description: error || "Investigative research processing encountered an error. Please check the details and try again.",
        variant: "destructive"
      });
    }
  }, [toast]);

  const handleCodebaseIndexingCompleted = useCallback((event: CustomEvent) => {
    const { success, requestId, pmoId, error } = event.detail;
    console.log(`[PMO Page] Codebase indexing completed event received:`, event.detail);
    if (success) {
      setActiveTab('records');
      setShowCreationForm(false);
      toast({
        title: 'Codebase Indexing Complete',
        description: 'The codebase indexing report was saved. Switched to PMO Requests for confirmation.'
      });
      fetchPMORecords();
    } else {
      toast({ title: 'Codebase Indexing Failed', description: error || 'An error occurred.', variant: 'destructive' });
    }
  }, [toast, fetchPMORecords]);

  // Automatic tab navigation for PMO workflow
  useEffect(() => {
    // Add event listeners
    window.addEventListener('pmo-research-completed', handleResearchCompleted as EventListener);
    window.addEventListener('pmo-marketing-completed', handleMarketingCompleted as EventListener);
    window.addEventListener('pmo-investigative-research-completed', handleInvestigativeResearchCompleted as EventListener);
    window.addEventListener('pmo-codebase-indexing-completed', handleCodebaseIndexingCompleted as EventListener);

    // Cleanup
    return () => {
      window.removeEventListener('pmo-research-completed', handleResearchCompleted as EventListener);
      window.removeEventListener('pmo-marketing-completed', handleMarketingCompleted as EventListener);
      window.removeEventListener('pmo-investigative-research-completed', handleInvestigativeResearchCompleted as EventListener);
      window.removeEventListener('pmo-codebase-indexing-completed', handleCodebaseIndexingCompleted as EventListener);
    };
  }, [handleResearchCompleted, handleMarketingCompleted, handleInvestigativeResearchCompleted, handleCodebaseIndexingCompleted]);

  // Prevent auto-refresh on tab visibility changes
  useEffect(() => {
    const handleVisibilityChange = () => {
      // Log visibility changes for debugging but don't trigger any refreshes
      console.log('PMO Page visibility changed:', document.visibilityState);
      // Explicitly prevent any automatic refresh behavior
    };

    const handleFocusChange = () => {
      // Log focus changes for debugging but don't trigger any refreshes
      console.log('PMO Page focus changed');
      // Explicitly prevent any automatic refresh behavior
    };

    // Add event listeners to monitor but not react to visibility/focus changes
    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleFocusChange);
    window.addEventListener('blur', handleFocusChange);

    // Cleanup
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocusChange);
      window.removeEventListener('blur', handleFocusChange);
    };
  }, []);


  const filteredRecords = pmoRecords.filter(record => {
    const recordTitle = record.title || "";
    const recordDescription = record.description || "";
    const matchesSearch =
      searchQuery === '' ||
      recordTitle.toLowerCase().includes(searchQuery.toLowerCase()) ||
      recordDescription.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesPriority =
      priorityFilter === 'all' ||
      record.priority === priorityFilter;
    return matchesSearch && matchesPriority;
  });

  const handlePmoFeatureFormSubmit = async (data: {
    projectName: string;
    description: string;
    selectedProvider: ModelProvider;
    selectedModel: string;
    customContext?: string;
    selectedDocumentId?: string;
    selectedCategory?: string;
    generatedPmoAssessment?: string; // Added
    isInvestigativeResearch?: boolean;
    investigativeOptions?: {
      comparisonModels?: string[];
      criteriaModel?: string;
      optimizationModel?: string;
      assessmentModel?: string;
      consolidationModel?: string;
    };
  }) => {
    console.log("PMO Feature Form (final submission) with data:", data);
    if (!user || !user.email) {
        alert("User information is missing. Cannot submit PMO request.");
        return;
    }

    // Consider a specific loading state for the submission process itself if it's long
    // For now, we'll rely on the visual switch and alert.

    try {
      const response = await fetch('/api/pmo-submit', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ ...data, userEmail: user.email })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to submit PMO request');
      }

      const result = await response.json();
      console.log("PMO request submitted successfully:", result);

      // Show success toast instead of alert
      toast({
        title: "PMO Request Submitted Successfully!",
        description: `Your request "${data.projectName}" has been submitted and is being processed.`,
        variant: "default"
      });

      setShowCreationForm(false); // Switch back to records list view
      setActiveTab("records"); // Ensure records tab is active
      if (isAuthorized) fetchPMORecords(); // Refresh records
    } catch (err: any) {
      console.error('Error submitting PMO request:', err);
      toast({
        title: "Error Submitting PMO Request",
        description: err.message || 'An unknown error occurred. Please try again.',
        variant: "destructive"
      });
    }
  };

  const handlePmoFeatureFormCancel = () => {
    // This is called when the user clicks "Cancel" on the form
    // In the main PMO page, we just want to switch back to records view
    // The PmoModelSelectionFeature component handles its own internal state
    setShowCreationForm(false);
    setActiveTab("records");
  };

  if (authLoading || plannerLoading || isCheckingAuth) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-900">
        <div className="text-white text-xl flex flex-col items-center">
          <RefreshCw className="animate-spin mr-2 h-8 w-8 mb-2" />
          <span>Loading PMO Page...</span>
          {authLoading && <span className="text-sm text-gray-400">Authenticating...</span>}
          {plannerLoading && <span className="text-sm text-gray-400">Loading planner data...</span>}
          {isCheckingAuth && !authLoading && <span className="text-sm text-gray-400">Checking authorization...</span>}
        </div>
      </div>
    );
  }

  if (!isAuthorized) {
    return (
      <div className="min-h-screen bg-gray-900 text-gray-200 flex items-center justify-center p-4">
        <div className="p-8 bg-gray-800 rounded-lg border border-red-700 max-w-md text-center shadow-xl">
          <ShieldAlert className="h-16 w-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-red-300 mb-2">Access Denied</h2>
          <p className="text-gray-300 mb-6">
            You do not have permission to access the PMO page.
            {user?.email && <span className="block text-sm mt-1">Attempted as: {user.email}</span>}
            Please contact an administrator if you believe this is an error.
          </p>
          <Button
            onClick={() => router.push('/')}
            className="bg-purple-600 hover:bg-purple-700 text-white"
          >
            Return to Home
          </Button>
        </div>
      </div>
    );
  }


  return (
    <PMOErrorBoundary>
      <div className="min-h-screen bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
          <div>
            <h1 className="text-2xl font-bold mb-1">Project Management Office (PMO)</h1>
            <p className="text-gray-400 text-sm">
              Manage and coordinate tasks across Agentic Teams.
            </p>
          </div>
          <div className="flex items-center gap-4">
             {user && (
              <div className="flex items-center space-x-2  bg-gray-800/50 p-2 rounded-lg border border-gray-700/50">
                {session?.user?.image || user.photoURL || user.avatar ? (
                  <img
                    src={session?.user?.image || user.photoURL || user.avatar}
                    alt={user.displayName || user.name || user.email || "User"}
                    className="w-8 h-8 rounded-full object-cover"
                    referrerPolicy="no-referrer"
                    onError={(e) => {
                      // Fallback to initials if image fails to load
                      e.currentTarget.style.display = 'none';
                      const fallbackDiv = e.currentTarget.nextElementSibling as HTMLElement;
                      if (fallbackDiv) {
                        fallbackDiv.style.display = 'flex';
                      }
                    }}
                  />
                ) : null}
                <div
                  className="w-8 h-8 rounded-full bg-purple-600 flex items-center justify-center text-white text-sm font-semibold"
                  style={{ display: session?.user?.image || user.photoURL || user.avatar ? 'none' : 'flex' }}
                >
                  {(user.displayName || user.name || user.email || "U")[0].toUpperCase()}
                </div>
                <div>
                  <p className="text-xs font-medium text-gray-400 truncate max-w-[150px]">{user.displayName || user.name || "User"}</p>
                  <p className="text-xs text-amber-500 truncate max-w-[150px]">{user.email}</p>
                </div>
              </div>
            )}
            {activeTab === 'records' && (
              <Button
                onClick={() => {
                  setShowCreationForm(!showCreationForm);
                  // Let the useEffect handle fetching when needed
                }}
                variant="outline"
                className={`border-gray-700 text-white ${
                  showCreationForm ? 'bg-amber-700 hover:bg-amber-600' : 'bg-teal-700 hover:bg-teal-600'
                }`}
              >
                {showCreationForm ? 'View PMO Records' : 'Create New Request'}
              </Button>
            )}
          </div>
        </div>

        <Tabs
          value={activeTab}
          onValueChange={(value) => {
            setActiveTab(value);
            if (value !== "records") {
              setShowCreationForm(false); // Ensure form is hidden on other tabs
            }
            // Remove the fetch logic from here - let the useEffect handle it
          }}
          className="mb-6"
        >
          <TabsList className="bg-gray-800 border border-gray-700">
            <TabsTrigger value="records" className="data-[state=active]:bg-purple-600">PMO Requests</TabsTrigger>
            <TabsTrigger value="documents" className="data-[state=active]:bg-amber-600">PMO Documents</TabsTrigger>
            <TabsTrigger value="systems-docs" className="data-[state=active]:bg-orange-600">Systems Documentation</TabsTrigger>
            <TabsTrigger value="outputs" className="data-[state=active]:bg-green-600">Agent Outputs</TabsTrigger>
            <TabsTrigger value="meeting" className="data-[state=active]:bg-blue-600">Meeting Room</TabsTrigger>
          </TabsList>



          <TabsContent value="records" className="mt-6">
            {showCreationForm ? (
              <PmoModelSelectionFeature
                availableProjects={projects || []}
                userId={user?.email}
                onPmoSubmit={handlePmoFeatureFormSubmit}
                onPmoCancel={handlePmoFeatureFormCancel}
                onRefreshRecords={isAuthorized ? fetchPMORecords : undefined}
              />
            ) : (
              <>
                <div className="bg-gray-800 rounded-lg p-4 mb-6 shadow">
                  <div className="flex flex-col md:flex-row gap-4">
                    <div className="relative flex-grow">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                      <input
                        type="text"
                        placeholder="Search PMO records by title or description..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
                      />
                    </div>
                    <div className="flex-shrink-0">
                      <select
                        value={priorityFilter}
                        onChange={(e) => setPriorityFilter(e.target.value as PMORecordPriority | 'all')}
                        className="w-full md:w-auto px-4 py-2.5 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-purple-500 h-full"
                      >
                        <option value="all">All Priorities</option>
                        <option value="Low">Low</option>
                        <option value="Medium">Medium</option>
                        <option value="High">High</option>
                        <option value="Critical">Critical</option>
                      </select>
                    </div>
                    <Button
                      onClick={fetchPMORecords}
                      className="flex items-center justify-center px-4 py-2 bg-gray-700 border border-gray-600 rounded-md text-white hover:bg-gray-600 transition-colors"
                      disabled={isLoadingRecords}
                    >
                      <RefreshCw className={`w-5 h-5 mr-2 ${isLoadingRecords ? 'animate-spin' : ''}`} />
                      Refresh
                    </Button>
                  </div>
                </div>

                {isLoadingRecords ? (
                  <div className="flex flex-col justify-center items-center py-12">
                    <div className="flex items-center mb-4">
                      <RefreshCw className="w-6 h-6 animate-spin text-purple-400 mr-3" />
                      <span className="text-gray-300">Loading PMO records...</span>
                    </div>
                    <Button
                      onClick={() => {
                        // Force reset loading state and try again
                        setIsLoadingRecords(false);
                        setTimeout(() => fetchPMORecords(), 500);
                      }}
                      className="mt-4 px-4 py-2 bg-gray-700 border border-gray-600 rounded-md text-white hover:bg-gray-600 transition-colors"
                    >
                      Force Refresh
                    </Button>
                  </div>
                ) : error ? (
                  <div className="bg-red-900/30 border border-red-700 rounded-lg p-6 text-center my-6 shadow-md">
                    <p className="text-red-300 font-semibold mb-3">Error loading records:</p>
                    <p className="text-red-400 mb-4">{error}</p>
                    <Button
                      onClick={fetchPMORecords}
                      className="px-4 py-2 bg-red-600 rounded-md text-white hover:bg-red-500 transition-colors"
                      disabled={isLoadingRecords}
                    >
                      <RefreshCw className={`w-4 h-4 mr-2 ${isLoadingRecords ? 'animate-spin' : ''}`} />
                      Try Again
                    </Button>
                  </div>
                ) : filteredRecords.length === 0 ? (
                  <div className="bg-gray-800 rounded-lg p-8 text-center my-6 shadow">
                    <Sparkles className="w-12 h-12 text-purple-400 mx-auto mb-4" />
                    <h3 className="text-xl font-medium mb-2 text-gray-200">No PMO Records Found</h3>
                    <p className="text-gray-400 mb-4">
                      {searchQuery || priorityFilter !== 'all'
                        ? 'No records match your current search or filter criteria.'
                        : 'There are no PMO requests yet. Get started by creating one!'}
                    </p>
                    {searchQuery || priorityFilter !== 'all' ? (
                      <Button
                        onClick={() => {
                          setSearchQuery('');
                          setPriorityFilter('all');
                        }}
                        variant="outline"
                        className="px-4 py-2 bg-gray-700 border-gray-600 rounded-md text-white hover:bg-gray-600 transition-colors"
                      >
                        Clear Filters
                      </Button>
                    ) : (
                      <Button
                        onClick={() => setShowCreationForm(true)}
                        className="px-4 py-2 bg-purple-600 rounded-md text-white hover:bg-purple-700 transition-colors"
                      >
                        Create New PMO Request
                      </Button>
                    )}
                  </div>
                ) : (
                  <PMORecordList records={filteredRecords} onRefresh={fetchPMORecords} />
                )}
              </>
            )}
          </TabsContent>

          <TabsContent value="outputs" className="mt-6">
            <AgentOutputsTab />
          </TabsContent>

          <TabsContent value="documents" className="mt-6">
            <PMODocumentsTab />
          </TabsContent>

          <TabsContent value="systems-docs" className="mt-6 relative">
            <div className="relative">
              {/* Toggle button for reports panel */}
              <div className="absolute top-0 right-0 z-10">
                <Button
                  onClick={() => setIsReportsPanelOpen(!isReportsPanelOpen)}
                  variant="outline"
                  size="sm"
                  className="bg-cyan-600/20 border-cyan-500/50 text-cyan-300 hover:bg-cyan-600/30 hover:border-cyan-400"
                >
                  <FileText className="w-4 h-4 mr-2" />
                  Indexing Reports
                  {isReportsPanelOpen ? (
                    <X className="w-4 h-4 ml-2" />
                  ) : (
                    <ChevronDown className="w-4 h-4 ml-2" />
                  )}
                </Button>
              </div>

              {/* Main Systems Documentation content */}
              <SystemsDocumentationTab />

              {/* Sliding panel for Codebase Indexing Reports */}
              <div
                className={`fixed top-0 right-0 h-full w-1/2 bg-gray-900 border-l border-gray-700 shadow-2xl transform transition-transform duration-300 ease-in-out z-50 ${
                  isReportsPanelOpen ? 'translate-x-0' : 'translate-x-full'
                }`}
              >
                <div className="h-full flex flex-col">
                  {/* Panel header */}
                  <div className="flex items-center justify-between p-4 border-b border-gray-700 bg-gray-800">
                    <h3 className="text-lg font-semibold text-white flex items-center">
                      <FileText className="w-5 h-5 mr-2 text-cyan-400" />
                      Codebase Indexing Reports
                    </h3>
                    <Button
                      onClick={() => setIsReportsPanelOpen(false)}
                      variant="ghost"
                      size="sm"
                      className="text-gray-400 hover:text-white hover:bg-gray-700"
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>

                  {/* Panel content */}
                  <div className="flex-1 overflow-hidden">
                    <CodebaseIndexingReportsTab />
                  </div>
                </div>
              </div>

              {/* Overlay when panel is open */}
              {isReportsPanelOpen && (
                <div
                  className="fixed inset-0 bg-black bg-opacity-50 z-40"
                  onClick={() => setIsReportsPanelOpen(false)}
                />
              )}
            </div>
          </TabsContent>

          <TabsContent value="meeting" className="mt-6">
            <AgentMeetingRoom />
          </TabsContent>
        </Tabs>
      </div>
    </div>
    </PMOErrorBoundary>
  );
}
