# Transcript State API - Firebase Migration Summary

## Problem Solved

The transcript state API was using in-memory storage (`Map`) which doesn't work in serverless environments like Vercel. When the UI stored transcript data, it was handled by one server instance. When the ElevenLabs webhook tried to retrieve that data seconds later, it was handled by a different server instance where the `Map` was empty.

## Solution Implemented

Migrated from in-memory storage to **Firebase Firestore** for persistent, shared storage that works across all server instances.

## Changes Made

### 1. Updated `app/api/transcript-state/route.ts`

**Before:**
- Used `Map<string, TranscriptState>` for in-memory storage
- Complex user-scoped key generation
- Data lost between server instances

**After:**
- Uses Firebase Firestore collection `transcript_sessions`
- Simple, reliable document-based storage
- Persistent across all server instances
- Automatic TTL cleanup support

### 2. Key Improvements

#### POST (Store Transcript)
- Stores transcript data in Firestore under multiple document IDs
- Uses batch writes for atomic operations
- Includes `createdAt` timestamp for TTL policy

#### GET (Retrieve Transcript)
- Queries Firestore documents by priority (agentId → conversationId → userId → fallback)
- Handles Firestore timestamp conversion for JSON serialization
- Returns detailed debugging information

#### DELETE (Clear Transcript)
- Uses batch deletes to remove all related documents
- Cleans up multiple storage keys atomically

### 3. Storage Strategy

Documents are stored under multiple IDs for reliable webhook access:
```
Document IDs:
- {userId}
- {agentId} (if available)
- {conversationId} (if available)
- "<EMAIL>" (fallback)
```

## Required Configuration

### 1. Firebase TTL Policy (MANDATORY)

You must configure a TTL policy in Firebase Console to prevent database bloat:

1. Go to Firebase Console → Firestore Database → TTL Policies
2. Create policy:
   - Collection Group: `transcript_sessions`
   - Timestamp Field: `createdAt`
   - TTL Action: Delete entire documents
   - Duration: 1 day (recommended)

See `docs/firebase-ttl-setup.md` for detailed instructions.

### 2. ElevenLabs Webhook Configuration (CRITICAL)

The webhook payload must include `agent_id` and `conversation_id`:

```json
{
  "agent_id": "{{agent_id}}",
  "conversation_id": "{{conversation_id}}",
  "summary": "{{summary}}",
  "generate_document": true,
  "transcript_metadata": {
    "agentType": "Marketing",
    "documentCategory": "PMO - Category Name"
  }
}
```

**Without these IDs, the webhook cannot retrieve the transcript data.**

## Testing the Fix

### 1. Verify Storage
```bash
# Check if transcripts are being stored
curl -X POST http://localhost:3000/api/transcript-state \
  -H "Content-Type: application/json" \
  -d '{"userId":"test","dialogue":[{"role":"user","content":"test","timestamp":"2024-01-01T00:00:00Z"}]}'
```

### 2. Verify Retrieval
```bash
# Check if transcripts can be retrieved
curl "http://localhost:3000/api/transcript-state?userId=test"
```

### 3. Monitor Logs
Look for these log messages:
- `✅ Transcript state stored successfully in Firestore`
- `✅ Retrieved transcript state from Firestore`
- `⚠️ No transcript state found in Firestore for keys`

## Expected Results

After implementing this fix:

1. **Persistent Storage**: Transcript data survives server restarts and instance changes
2. **Reliable Webhooks**: ElevenLabs webhooks can consistently find stored transcript data
3. **Complete PDFs**: Generated documents will contain full conversation transcripts instead of just summaries
4. **Automatic Cleanup**: Old transcripts are automatically deleted by Firebase TTL policy

## Rollback Plan

If issues occur, you can temporarily revert by:
1. Restoring the previous `app/api/transcript-state/route.ts` from git
2. The webhook will fall back to using ElevenLabs conversation history
3. PDFs will be generated from summaries instead of full transcripts

## Next Steps

1. **Deploy the changes** to your Vercel environment
2. **Configure Firebase TTL policy** (see docs/firebase-ttl-setup.md)
3. **Update ElevenLabs webhook configuration** to include agent_id and conversation_id
4. **Test with a real voice conversation** to verify end-to-end functionality
5. **Monitor logs** for successful transcript storage and retrieval

## Files Modified

- `app/api/transcript-state/route.ts` - Complete rewrite to use Firestore
- `docs/firebase-ttl-setup.md` - New documentation for TTL setup
- `docs/transcript-state-firebase-migration.md` - This summary document
