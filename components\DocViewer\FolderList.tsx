import React, { useState, useMemo } from 'react';
import { Folder, ChevronDown, ChevronRight, FilesIcon, FoldersIcon, Trash } from 'lucide-react';
import { Timestamp } from 'firebase/firestore';
import { FileData } from './FileManagerstructure';
import FileList from './FileList';
import { SortConfig } from './FileManagerDirectory';
import FileManagerPagination from './FileManagerPagination';

interface FolderListProps {
  groupedFiles: {
    category: string;
    files: FileData[];
    allFiles?: FileData[]; // All files in the category (for counts)
    isExpanded: boolean;
  }[];
  toggleCategory: (category: string) => void;
  handleFileClick: (file: FileData) => void;
  formatTimestamp: (timestamp: Timestamp | undefined) => string;
  truncateFileName: (fileName: string, maxLength?: number) => string;
  sortConfig: SortConfig | null;
  onDeleteFolder?: (category: string, total: number) => void;
}

const FolderList: React.FC<FolderListProps> = ({
  groupedFiles,
  toggleCategory,
  handleFileClick,
  formatTimestamp,
  truncateFileName,
  sortConfig,
  onDeleteFolder,
}) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [foldersPerPage, setFoldersPerPage] = useState(10); // Show 10 folders per page as shown in the screenshots

  // Sort folders alphabetically
  const sortedFolders = useMemo(() => {
    return [...groupedFiles].sort((a, b) => a.category.localeCompare(b.category));
  }, [groupedFiles]);

  // Calculate pagination
  const totalPages = Math.ceil(sortedFolders.length / foldersPerPage);

  // Get current page folders
  const currentFolders = useMemo(() => {
    const indexOfLastFolder = currentPage * foldersPerPage;
    const indexOfFirstFolder = indexOfLastFolder - foldersPerPage;
    return sortedFolders.slice(indexOfFirstFolder, indexOfLastFolder);
  }, [sortedFolders, currentPage, foldersPerPage]);

  // Handle page change
  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  return (
    <div className="divide-y divide-gray-700 mt-5">
      <div className="text-gray-400 text-left ml-2 font-semibold mb-1 shadow-md p-2 w-max rounded-xl flex items-center">
        <FoldersIcon className="h-5 w-5 mr-1" />
        My list of folders
      </div>

      {/* Scrollable container for folders */}
      <div className="max-h-[calc(100vh-380px)] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800">
        {currentFolders.map(({ category, files, allFiles, isExpanded }) => (
          <div key={category}>
            <div className="grid grid-cols-8 gap-2 p-1 text-xs text-left ml-2 mr-2 rounded-xl bg-ike-dark-purple text-white items-center mb-1 hover:bg-ike-purple_b">
              <div className="col-span-4 flex items-center">
                <button
                  onClick={() => toggleCategory(category)}
                  className="focus:outline-none mr-2"
                  aria-label={isExpanded ? `Collapse ${category}` : `Expand ${category}`}
                >
                  {isExpanded ? (
                    <ChevronDown className="h-5 w-5 text-amber-500" />
                  ) : (
                    <ChevronRight className="h-5 w-5 text-amber-500" />
                  )}
                </button>
                <Folder className="inline mr-2 text-amber-500" />
                {category}
              </div>
              <div className="flex items-center justify-center w-full">
                {/* Use allFiles for count if available, otherwise use files */}
                {allFiles?.length || files.length}
              </div>
              <div className="flex items-center justify-center w-full">
                {/* Use allFiles for chat count if available, otherwise use files */}
                {(allFiles || files).reduce((sum: number, file: FileData) => sum + (file.chatCount || 0), 0)}
              </div>
              <div className="flex items-center justify-end w-full pr-2">
                <button
                  aria-label={`Delete folder ${category}`}
                  title={`Delete folder ${category}`}
                  className="text-red-400 hover:text-red-300 focus:outline-none"
                  onClick={() => onDeleteFolder && onDeleteFolder(category, (allFiles?.length || files.length))}
                >
                  <Trash className="h-4 w-4" />
                </button>
              </div>
              <div></div>
            </div>
            {isExpanded && (
              <div className="pl-4">
                <FileList
                  files={files}
                  handleFileClick={handleFileClick}
                  truncateFileName={truncateFileName}
                  formatTimestamp={formatTimestamp}
                  sortConfig={sortConfig}
                  variant="categorized"
                />
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Show pagination controls and folders per page */}
      <div className="mt-0.5 mb-0.5 flex justify-between items-center px-2">
        <div className="flex items-center text-gray-400 text-xs">
          <span className="mr-1">Folders per page:</span>
          <select
            value={foldersPerPage}
            onChange={(e) => {
              const newValue = Number(e.target.value);
              setFoldersPerPage(newValue);
              setCurrentPage(1); // Reset to first page when changing items per page
            }}
            className="bg-gray-800 text-gray-200 border border-gray-700 rounded px-1 py-0.5 text-xs"
          >
            <option value="10">10</option>
            <option value="20">20</option>
            <option value="50">50</option>
          </select>
        </div>

        {totalPages > 1 && (
          <FileManagerPagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
          />
        )}
      </div>
    </div>
  );
};

export default FolderList;