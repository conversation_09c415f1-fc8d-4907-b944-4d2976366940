#!/usr/bin/env node

/**
 * ElevenLabs Post-Call Webhook Setup Script
 * 
 * This script helps configure the post-call webhook in your ElevenLabs workspace.
 * The webhook must be configured at the workspace level, not per-agent.
 * 
 * Usage: node scripts/setup-elevenlabs-webhook.js
 */

const https = require('https');
const fs = require('fs');
const path = require('path');

// Configuration
const WEBHOOK_ENDPOINT = '/api/elevenlabs/post-call-webhook';
const ENV_FILE = path.join(__dirname, '..', '.env.local');

async function main() {
  console.log('🔧 ElevenLabs Post-Call Webhook Setup');
  console.log('=====================================\n');

  // Check environment variables
  const webhookBaseUrl = process.env.ELEVENLABS_WEBHOOK_BASE_URL || process.env.NEXTAUTH_URL;
  const webhookSecret = process.env.ELEVENLABS_WEBHOOK_SECRET;
  const apiKey = process.env.NEXT_PUBLIC_ELEVENLABS_COMPANY_API_KEY;

  if (!webhookBaseUrl) {
    console.error('❌ Missing ELEVENLABS_WEBHOOK_BASE_URL or NEXTAUTH_URL');
    console.log('   Please set one of these environment variables to your public URL');
    process.exit(1);
  }

  if (!webhookSecret) {
    console.error('❌ Missing ELEVENLABS_WEBHOOK_SECRET');
    console.log('   Please set this environment variable for webhook authentication');
    process.exit(1);
  }

  if (!apiKey) {
    console.error('❌ Missing NEXT_PUBLIC_ELEVENLABS_COMPANY_API_KEY');
    console.log('   Please set this environment variable with your ElevenLabs API key');
    process.exit(1);
  }

  const fullWebhookUrl = `${webhookBaseUrl}${WEBHOOK_ENDPOINT}`;

  console.log('📋 Configuration Summary:');
  console.log(`   Webhook URL: ${fullWebhookUrl}`);
  console.log(`   Secret: ${webhookSecret.substring(0, 8)}...`);
  console.log(`   API Key: ${apiKey.substring(0, 8)}...\n`);

  // Test webhook endpoint
  console.log('🧪 Testing webhook endpoint...');
  try {
    const testResponse = await testWebhookEndpoint(fullWebhookUrl);
    if (testResponse.ok) {
      console.log('✅ Webhook endpoint is accessible');
    } else {
      console.log(`⚠️  Webhook endpoint returned status: ${testResponse.status}`);
      console.log('   This might be expected if authentication is required');
    }
  } catch (error) {
    console.error('❌ Failed to reach webhook endpoint:', error.message);
    console.log('   Make sure your server is running and accessible from the internet');
  }

  console.log('\n📝 Manual Configuration Required:');
  console.log('==================================');
  console.log('ElevenLabs post-call webhooks must be configured manually in the dashboard:');
  console.log('');
  console.log('1. Go to: https://elevenlabs.io/app/conversational-ai');
  console.log('2. Click on "Settings" in the left sidebar');
  console.log('3. Find "Post-call webhooks" section');
  console.log('4. Enable post-call webhooks');
  console.log(`5. Set webhook URL to: ${fullWebhookUrl}`);
  console.log('6. Set the webhook secret (if required)');
  console.log('7. Save the configuration');
  console.log('');
  console.log('📚 Documentation: https://elevenlabs.io/docs/conversational-ai/workflows/post-call-webhooks');
  console.log('');

  // Check if ngrok is being used
  if (webhookBaseUrl.includes('ngrok')) {
    console.log('🔗 Using ngrok detected:');
    console.log('   Make sure your ngrok tunnel is active and pointing to your local server');
    console.log('   The webhook URL must be accessible from the internet');
    console.log('');
  }

  console.log('✅ Setup script completed!');
  console.log('   After configuring the webhook in ElevenLabs dashboard,');
  console.log('   test it by having a conversation with your agent and ending the call.');
}

async function testWebhookEndpoint(url) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || (urlObj.protocol === 'https:' ? 443 : 80),
      path: urlObj.pathname,
      method: 'GET',
      timeout: 5000
    };

    const req = (urlObj.protocol === 'https:' ? https : require('http')).request(options, (res) => {
      resolve({ ok: res.statusCode < 400, status: res.statusCode });
    });

    req.on('error', reject);
    req.on('timeout', () => reject(new Error('Request timeout')));
    req.setTimeout(5000);
    req.end();
  });
}

// Run the script
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main };
