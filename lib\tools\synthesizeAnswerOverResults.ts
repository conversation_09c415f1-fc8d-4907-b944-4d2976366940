import type { CodebaseHit } from './queryCodebaseStorage';
import { processWithGoogleAI } from './google-ai';
import { processWithOpenAI } from './openai-ai';
import { processWithAnthropic } from './anthropic-ai';
import { processWithGroq } from './groq-ai';


export interface SynthesisResult {
  llmAnalysis: string;
  llmModel: string;
  llmProvider: string;
  timingMs: number;
}

/**
 * Shared LLM synthesis over retrieved search hits.
 * Uses the same prompt template and fallback chain as previously in queryCodebaseStorage.
 */
export async function synthesizeAnswerOverResults(
  query: string,
  category: string,
  hits: CodebaseHit[],
  options?: { provider?: string; model?: string }
): Promise<SynthesisResult> {
  const llmStart = Date.now();

  // Build truncated context from top hits (limit total characters)
  const maxConcat = 18000;
  const context = (hits || [])
    .slice(0, 20)
    .map((h) => `# ${h.firestoreMetadata?.filePath || h.pineconeMetadata?.filePath || h.chunkId}\n${h.content}`)
    .join('\n\n---\n\n')
    .slice(0, maxConcat);

  const namespaces = Array.from(
    new Set((hits || []).map((h) => h.namespace).filter(Boolean))
  );

  const analysisPrompt = `You are a systems analyst specialized in analyzing and responding to queries about indexed codebases.
      - Your responses should be professional, accurate, and tailored to the user's query.
      - Your responses when related to the production of technical and non-technical documentation, should be professional, well-structured, comprehensive, and include all relevant details.
      - Your responses when related to the production of sales and marketing materials, should be engaging, accessible, and suitable as a basis for marketing materials.
      - Do not invent or hallucinate information not present in the provided context.
      - Use Markdown for formatting to improve readability (e.g., headers, code blocks, lists).

User query: ${query}
Category: ${category}
Namespaces: ${namespaces.join(', ')}

Context snippets from most relevant chunks (truncated; use only this information for your response):
${context}

Instructions:
- Analyze the user's query to determine the appropriate response style:
  - For simple questions (e.g., "What does this function do?"), provide a short, concise answer.
  - For detailed explanations or documentation requests (e.g., "Generate documentation for this component"), provide comprehensive, structured documentation including purpose, parameters, usage examples, and edge cases.
  - For functional specifications or requirements gathering (e.g., "What are the technical specifications for this feature?"), provide a clear, detailed list of requirements including functional, non-functional, and quality attributes.
  - For code reviews or product evaluations (e.g., "Review this code for best practices" or "Assess this feature's implementation"), offer constructive feedback on strengths, weaknesses, adherence to best practices, potential improvements, security concerns, performance issues, and scalability.
  - If the query asks for code snippets, examples, or modifications, include relevant code blocks extracted or derived accurately from the context.
  - For sales and marketing documentation requests (e.g., "Generate marketing content for
  this feature", "Create a product overview", or "Produce source material for sales pitches"),
  - Generate non-technical, engaging content suitable as a basis for marketing materials.
  - Wherever its relevant consider the following:
    - Highlight key features, benefits, real-world use cases, customer value propositions,
  and competitive advantages. Use accessible language, avoid technical jargon or explain it simply,
  and structure for easy adaptation into brochures, website copy, emails, or presentations.
    - Include suggested headlines, bullet points for key selling points, and calls to action where appropriate.

- Always prioritize accuracy and relevance. Cite specific file paths, entity names (e.g., functions, classes), and chunk IDs where applicable to support your claims.
- List any relevant imports, exports, APIs, libraries, or dependencies that directly relate to the query.
- Structure your response logically: Start with a summary or direct answer, followed by details, citations, and any lists or recommendations.
- If the context lacks sufficient information, state that clearly and suggest what additional details might be needed.

Response:`;

  let llmResult = '';
  // Prefer provided model/provider if specified
  let llmModel = options?.model || process.env.LLM_MODEL || 'gemini-2.5-pro';
  let provider = (options?.provider || (llmModel.startsWith('gemini') ? 'google' : 'openai')).toLowerCase();
  const isBad = (s?: string) => !s || s.trim() === '' || s.startsWith('Error') || s.startsWith('FALLBACK_REQUIRED');
  try {
    if (provider === 'google') {
      llmResult = await processWithGoogleAI({ prompt: analysisPrompt, model: llmModel });
      console.log(`synthesizeAnswerOverResults: Processed with Google AI model: ${llmModel}`);
    } else if (provider === 'openai') {
      llmResult = await processWithOpenAI({ prompt: analysisPrompt, model: llmModel, modelOptions: { temperature: 0.4, maxTokens: 10000 } });
      console.log(`synthesizeAnswerOverResults: Processed with OpenAI model: ${llmModel}`);
    } else if (provider === 'anthropic' || provider === 'claude') {
      // Support Anthropic/Claude via unified Anthropic adapter (same as tools-test page)
      llmResult = await processWithAnthropic({ prompt: analysisPrompt, model: llmModel, modelOptions: { temperature: 0.4, maxTokens: 10000 } });
      console.log(`synthesizeAnswerOverResults: Processed with Anthropic model: ${llmModel}`);
    } else if (provider === 'groq' || provider === 'deepseek') {
      // Map deepseek to groq-compatible route
      llmResult = await processWithGroq({ prompt: analysisPrompt, model: llmModel, modelOptions: { temperature: 0.4, maxTokens: 8000 } });
      console.log(`synthesizeAnswerOverResults: Processed with Groq model: ${llmModel}`);
    } else {
      // Unknown provider – skip to fallbacks
      llmResult = '';
    }
  } catch {
    // proceed to fallbacks
  }
  // First fallback: OpenAI default (covers unsupported providers/models)
  if (isBad(llmResult)) {
    try {
      provider = 'openai';
      llmModel = process.env.OPENAI_MODEL || 'gpt-5-2025-08-07';
      llmResult = await processWithOpenAI({ prompt: analysisPrompt, model: llmModel, modelOptions: { temperature: 0.4, maxTokens: 10000 } });
    } catch {}
  }
  // Second fallback: Google Gemini default (useful if OpenAI key is missing locally)
  if (isBad(llmResult)) {
    try {
      provider = 'google';
      llmModel = process.env.GOOGLE_MODEL || 'gemini-2.5-pro';
      llmResult = await processWithGoogleAI({ prompt: analysisPrompt, model: llmModel });
    } catch {}
  }
  // Final safety: ensure we always return a string so UI can render the Answer section
  if (isBad(llmResult)) {
    llmResult = 'Error: LLM synthesis failed after trying available providers. Ensure valid API keys (OPENAI_API_KEY or GOOGLE_API_KEY) are configured.';
  }

  return {
    llmAnalysis: (llmResult || '').trim(),
    llmModel,
    llmProvider: provider,
    timingMs: Date.now() - llmStart,
  };
}

