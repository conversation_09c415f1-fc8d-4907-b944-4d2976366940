export const runtime = 'nodejs';
export const maxDuration = 60;

import { NextRequest, NextResponse } from 'next/server';
import { saveTranscript, TranscriptMetadata } from '../../../../lib/utils/transcriptUtils';
import {
  generateMeetingDocument,
  MeetingDocumentGenerationResult
} from '../../../../lib/tools/meetingDocumentGenerator';
import { createPMORecordFromForm, updatePMORecord } from '../../../../lib/firebase/pmoCollection';

// Define TranscriptMessage interface locally to avoid JSX import issues
interface TranscriptMessage {
  role: 'user' | 'assistant' | 'agent';
  content: string;
  timestamp: Date;
}
// import { getTeamIdFromAgentType } from '../../../../lib/utils/teamNameUtils';

// ✅ PREVENT DUPLICATE SAVES: Track processed conversation IDs at module level
const processedConversations = new Set<string>();





/**
 * Webhook endpoint for ElevenLabs agents to save meeting summaries
 * This endpoint is called by the voice agent when the conversation is ending
 * to save the transcript and optionally generate documents
 *
 * This replaces the need for workspace-level post-call webhooks by allowing
 * the agent to proactively save the meeting content before ending the call.
 */
export async function POST(request: NextRequest) {
  const startTime = Date.now();
  const EARLY_ACK_TIMEOUT = 45000; // 45 seconds - leave buffer for Vercel's 60s limit

  try {
    console.log('[SAVE_MEETING_WEBHOOK] Received save meeting summary request from voice agent');
    console.log('[SAVE_MEETING_WEBHOOK] Request headers:', {
      'content-type': request.headers.get('content-type'),
      'authorization': request.headers.get('authorization') ? '[REDACTED]' : 'none',
      'user-agent': request.headers.get('user-agent')
    });

    // Verify webhook authentication
    const authHeader = request.headers.get('authorization');
    const expectedAuth = `Bearer ${process.env.ELEVENLABS_WEBHOOK_SECRET || 'pmo-webhook-secret-2024'}`;

    if (authHeader !== expectedAuth) {
      console.error('[SAVE_MEETING_WEBHOOK] Unauthorized webhook request');
      console.error('[SAVE_MEETING_WEBHOOK] Received auth:', authHeader);
      console.error('[SAVE_MEETING_WEBHOOK] Expected auth:', expectedAuth);
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Helper function to check if we should return early
    const shouldReturnEarly = () => {
      const elapsed = Date.now() - startTime;
      return elapsed > EARLY_ACK_TIMEOUT;
    };

    // Parse request body
    const body = await request.json();
    console.log('[SAVE_MEETING_WEBHOOK] Request body:', JSON.stringify(body, null, 2));

    // ✅ CRITICAL FIX: Prevent duplicate processing of the same conversation
    const conversationId = body.conversation_id;
    if (conversationId) {
      if (processedConversations.has(conversationId)) {
        console.log('[SAVE_MEETING_WEBHOOK] ⚠️ DUPLICATE CONVERSATION DETECTED - Skipping processing:', {
          conversationId,
          alreadyProcessed: true,
          note: 'This conversation has already been processed to prevent duplicate documents'
        });

        return NextResponse.json({
          success: true,
          message: 'Conversation already processed',
          conversationId,
          skipped: true
        });
      }

      // Mark conversation as being processed
      processedConversations.add(conversationId);
      console.log('[SAVE_MEETING_WEBHOOK] ✅ Marked conversation as processing:', conversationId);
    }

    // ✅ EARLY DEBUG: Check what we receive immediately
    console.log('[SAVE_MEETING_WEBHOOK] 🔍 EARLY DEBUG - Raw payload analysis:', {
      hasAgentId: 'agent_id' in body,
      agentIdValue: body.agent_id,
      agentIdType: typeof body.agent_id,
      hasConversationId: 'conversation_id' in body,
      conversationIdValue: body.conversation_id,
      conversationIdType: typeof body.conversation_id,
      hasConversationInitiation: 'conversation_initiation_client_data' in body,
      conversationInitiationValue: body.conversation_initiation_client_data,
      hasAgentName: 'agent_name' in body,
      agentNameValue: body.agent_name,
      allKeys: Object.keys(body),
      note: 'If agent_id or conversation_id are undefined, ElevenLabs webhook configuration may be incomplete'
    });

    // Extract data from ElevenLabs webhook format
    const {
      summary,
      action_items,
      document_title,
      generate_document = true,
      // Web search results from the meeting
      web_search_results = [],
      search_queries = [],
      research_findings = '',
      // ElevenLabs provides these automatically
      agent_id,
      conversation_id,
      user_id = process.env.NEXT_PUBLIC_SYS_ADMIN || '<EMAIL>', // Use system admin instead of 'system'
      conversation_history = [],
      // ✅ Extract transcript metadata containing selected document categories
      transcript_metadata = {},
      // ✅ Extract conversation initiation metadata (passed from UI)
      conversation_initiation_client_data = {},
    // ✅ Extract dynamic variables (ElevenLabs system variables)
    dynamic_variables = {}
    } = body;

    console.log('[SAVE_MEETING_WEBHOOK] Processing meeting summary:', {
      agent_id,
      conversation_id,
      user_id,
      summary_length: summary?.length || 0,
      has_action_items: !!action_items,
      generate_document,
      conversation_history_length: conversation_history?.length || 0,
      web_search_results_count: web_search_results?.length || 0,
      search_queries_count: search_queries?.length || 0,
      has_research_findings: !!research_findings,
      transcript_metadata: transcript_metadata,
      conversation_initiation_client_data: conversation_initiation_client_data,
      dynamic_variables: dynamic_variables
    });

    // ✅ CRITICAL: Check for ElevenLabs system variable substitution issues
    console.log('[SAVE_MEETING_WEBHOOK] 🚨 ELEVENLABS SYSTEM VARIABLE ANALYSIS:', {
      agentIdIsUnknown: agent_id === 'unknown',
      conversationIdIsUnknown: conversation_id === 'unknown',
      systemVariableSubstitutionFailed: agent_id === 'unknown' || conversation_id === 'unknown',
      expectedSystemVariables: ['{{system__agent_id}}', '{{system__conversation_id}}', '{{system__agent_name}}'],
      actualValues: { agent_id, conversation_id, agent_name: body.agent_name },
      troubleshooting: agent_id === 'unknown' ? 'ElevenLabs webhook is not substituting {{system__agent_id}} properly' : 'Agent ID substitution working',
      note: 'If values are "unknown", the ElevenLabs agent webhook configuration needs to be updated'
    });

    // ✅ DEBUG: Log the entire webhook payload to see what we're actually receiving
    console.log('[SAVE_MEETING_WEBHOOK] 🔍 FULL WEBHOOK PAYLOAD DEBUG:', JSON.stringify({
      agent_id,
      conversation_id,
      user_id,
      agent_id_type: typeof agent_id,
      agent_id_value: agent_id,
      conversation_initiation_client_data,
      transcript_metadata
    }, null, 2));

    // ✅ CRITICAL DEBUG: Check conversation initiation client data structure
    console.log('[SAVE_MEETING_WEBHOOK] 🎯 CONVERSATION INITIATION CLIENT DATA ANALYSIS:', {
      hasConversationInitiationData: !!conversation_initiation_client_data,
      conversationInitiationKeys: conversation_initiation_client_data ? Object.keys(conversation_initiation_client_data) : [],
      documentCategory: conversation_initiation_client_data?.documentCategory,
      agentType: conversation_initiation_client_data?.agentType,
      selectedDocuments: conversation_initiation_client_data?.selectedDocuments,
      selectedDocumentsCount: conversation_initiation_client_data?.selectedDocuments?.length || 0,
      note: 'This should contain the documentCategory from SelectedDocumentsDisplay.tsx line 161 via clientData wrapper'
    });



    // ✅ Extract selected document categories from transcript metadata AND conversation initiation data
    console.log('[SAVE_MEETING_WEBHOOK] Transcript metadata received:', transcript_metadata);
    console.log('[SAVE_MEETING_WEBHOOK] Conversation initiation client data received:', conversation_initiation_client_data);

    // ✅ ENHANCED CATEGORY EXTRACTION: Try UI state capture FIRST (highest priority)
    let selectedDocumentCategory: string | undefined;

    // ✅ PRIORITY 1: Try to get category from UI state capture API (most reliable)
    // Check both the actual user_id and fallback user_id since ElevenLabs may not provide user_id
    const userIdsToCheck = [
      user_id, // The user_id from ElevenLabs (might be fallback)
      process.env.NEXT_PUBLIC_SYS_ADMIN || '<EMAIL>' // Fallback user
    ].filter((id, index, arr) => id && arr.indexOf(id) === index); // Remove duplicates and empty values

    console.log('[SAVE_MEETING_WEBHOOK] 🎯 Attempting to retrieve category from UI state capture for user IDs:', userIdsToCheck);

    for (const userId of userIdsToCheck) {
      try {
        const categoryStateResponse = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/document-category-state?userId=${encodeURIComponent(userId)}`, {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' }
        });

        console.log('[SAVE_MEETING_WEBHOOK] 🔍 Category state API response for userId:', userId, {
          status: categoryStateResponse.status,
          statusText: categoryStateResponse.statusText,
          ok: categoryStateResponse.ok
        });

        if (categoryStateResponse.ok) {
          const categoryStateData = await categoryStateResponse.json();

          console.log('[SAVE_MEETING_WEBHOOK] 🔍 Category state API data for userId:', userId, {
            success: categoryStateData.success,
            hasCategoryState: !!categoryStateData.categoryState,
            primaryCategory: categoryStateData.categoryState?.primaryCategory,
            agentType: categoryStateData.categoryState?.agentType,
            timestamp: categoryStateData.categoryState?.timestamp
          });

          if (categoryStateData.success && categoryStateData.categoryState?.primaryCategory) {
            selectedDocumentCategory = categoryStateData.categoryState.primaryCategory;

            console.log('[SAVE_MEETING_WEBHOOK] ✅ Retrieved category from UI state capture (PRIORITY 1):', {
              checkedUserId: userId,
              primaryCategory: selectedDocumentCategory,
              selectedDocumentsCount: categoryStateData.categoryState.selectedDocuments?.length || 0,
              agentType: categoryStateData.categoryState.agentType,
              stateTimestamp: categoryStateData.categoryState.timestamp,
              note: 'This is the exact category from SelectedDocumentsDisplay.tsx line 161 - SHOULD OVERRIDE AGENT CATEGORY'
            });
            break; // Found category, stop checking other user IDs
          } else {
            console.log('[SAVE_MEETING_WEBHOOK] ⚠️ No valid category state found for userId:', userId, {
              categoryStateExists: !!categoryStateData.categoryState,
              primaryCategoryExists: !!categoryStateData.categoryState?.primaryCategory,
              fullCategoryState: categoryStateData.categoryState
            });
          }
        } else {
          console.log('[SAVE_MEETING_WEBHOOK] ⚠️ Failed to retrieve category state for userId:', userId, {
            status: categoryStateResponse.status,
            statusText: categoryStateResponse.statusText
          });
        }
      } catch (uiStateError) {
        console.log('[SAVE_MEETING_WEBHOOK] ⚠️ Error retrieving UI state for userId:', userId, uiStateError);
      }
    }

    // ✅ PRIORITY 2: Fall back to conversation_initiation_client_data (if UI state capture failed)
    if (!selectedDocumentCategory) {
      selectedDocumentCategory = conversation_initiation_client_data?.documentCategory;
      if (selectedDocumentCategory) {
        console.log('[SAVE_MEETING_WEBHOOK] ✅ Using category from conversation_initiation_client_data (PRIORITY 2):', selectedDocumentCategory);
      }
    }

    // If conversation initiation data is missing, try to extract from transcript metadata
    if (!selectedDocumentCategory && transcript_metadata?.selectedDocuments?.length > 0) {
      console.log('[SAVE_MEETING_WEBHOOK] 🔍 Attempting document lookup from transcript metadata:', {
        selectedDocuments: transcript_metadata.selectedDocuments,
        transcriptCategory: transcript_metadata.documentCategory
      });

      // Try to find the actual document by title to get the real category
      try {
        const { getFirestore } = await import('firebase-admin/firestore');
        const db = getFirestore();

        const documentTitle = transcript_metadata.selectedDocuments[0];
        console.log('[SAVE_MEETING_WEBHOOK] 🔍 Looking up document by title:', documentTitle);

        // Search for document by title in the user's files
        // Note: title and category are DIFFERENT fields - title is the document name, category is the PMO category
        const documentsRef = db.collection('users').doc(user_id).collection('files');
        const titleQuery = await documentsRef.where('name', '==', documentTitle).limit(1).get();

        if (!titleQuery.empty) {
          const doc = titleQuery.docs[0];
          const docData = doc.data();
          selectedDocumentCategory = docData.category;
          console.log('[SAVE_MEETING_WEBHOOK] ✅ Found document by title lookup:', {
            documentTitle: documentTitle,
            documentName: docData.name,
            foundCategory: selectedDocumentCategory,
            documentId: doc.id,
            note: 'Title and category are DIFFERENT - title is document name, category is PMO category'
          });
        } else {
          console.log('[SAVE_MEETING_WEBHOOK] ⚠️ Document not found by title, trying alternative search methods');

          // Try searching by partial title match
          const allDocs = await documentsRef.get();
          let foundDocData: any = null;
          let foundDocId: string = '';

          allDocs.forEach(doc => {
            const data = doc.data();
            if (data.name && data.name.includes(documentTitle.substring(0, 20))) {
              foundDocData = data;
              foundDocId = doc.id;
            }
          });

          if (foundDocData) {
            selectedDocumentCategory = foundDocData.category;
            console.log('[SAVE_MEETING_WEBHOOK] ✅ Found document by partial title match:', {
              searchTitle: documentTitle,
              foundName: foundDocData.name,
              foundCategory: selectedDocumentCategory,
              documentId: foundDocId
            });
          } else {
            console.log('[SAVE_MEETING_WEBHOOK] ⚠️ No document found - will NOT use automatic "Market Research" fallback');
            // ✅ REMOVED: selectedDocumentCategory = transcript_metadata.documentCategory;
          }
        }
      } catch (lookupError) {
        console.log('[SAVE_MEETING_WEBHOOK] ⚠️ Document lookup failed - will NOT use automatic "Market Research" fallback:', lookupError);
        // ✅ REMOVED: selectedDocumentCategory = transcript_metadata.documentCategory;
      }
    }

    console.log('[SAVE_MEETING_WEBHOOK] 🎯 ENHANCED CATEGORY EXTRACTION RESULT:', {
      documentCategoryFromUICapture: selectedDocumentCategory && !conversation_initiation_client_data?.documentCategory ? selectedDocumentCategory : null,
      documentCategoryFromConversationInit: conversation_initiation_client_data?.documentCategory,
      documentCategoryFromTranscript: transcript_metadata?.documentCategory,
      voiceAgentAutoCategory: transcript_metadata?.documentCategory, // This is often "Market Research" for Marketing agents
      finalSelectedCategory: selectedDocumentCategory,
      source: selectedDocumentCategory && !conversation_initiation_client_data?.documentCategory ? 'ui_state_capture' :
              (conversation_initiation_client_data?.documentCategory ? 'conversation_initiation_client_data' :
              (selectedDocumentCategory !== transcript_metadata?.documentCategory ? 'document_title_lookup' : 'transcript_metadata_fallback')),
      categoryChanged: selectedDocumentCategory !== transcript_metadata?.documentCategory,
      voiceAgentOverridden: transcript_metadata?.documentCategory === 'Market Research' && selectedDocumentCategory !== 'Market Research',
      note: 'Enhanced: ui_state_capture → conversation_initiation_client_data → document_title_lookup → transcript_metadata',
      success: selectedDocumentCategory && selectedDocumentCategory.includes('PMO -') ? '✅ PMO category found' : '⚠️ Non-PMO category'
    });

    const selectedDocumentId = transcript_metadata?.documentId;
    const metadataAgentType = conversation_initiation_client_data?.agentType || transcript_metadata?.agentType;

    console.log('[SAVE_MEETING_WEBHOOK] Extracted from transcript metadata:', {
      selectedDocumentCategory,
      selectedDocumentId,
      metadataAgentType,
      hasTranscriptMetadata: !!transcript_metadata,
      transcriptMetadataKeys: transcript_metadata ? Object.keys(transcript_metadata) : []
    });

    // Validate required data
    if (!summary) {
      console.error('[SAVE_MEETING_WEBHOOK] Missing required summary');
      return NextResponse.json({
        error: 'Missing required field: summary'
      }, { status: 400 });
    }

    // Create transcript messages from UI capture, conversation history, or fallback to summary
    let transcriptMessages: TranscriptMessage[] = [];

    // ✅ ENHANCED TRANSCRIPT RETRIEVAL LOGIC
    // Based on server logs showing conversationId: undefined, we've improved the retrieval logic to:
    // 1. Try multiple parameter combinations (full params, user+agent, user only, etc.)
    // 2. Accept transcript data even if API returns success: false
    // 3. Add comprehensive debugging to identify retrieval failures
    // 4. Include final fallback with direct store inspection
    // 5. Keep additionalContext clean when full transcript is available

    // ✅ TRANSCRIPT RETRIEVAL WILL BE DONE AFTER AGENT ID RESOLUTION
    let uiTranscriptFound = false;

    // PRIORITY 2: Fall back to ElevenLabs conversation history if UI transcript not found
    if (!uiTranscriptFound && conversation_history && Array.isArray(conversation_history) && conversation_history.length > 0) {
      console.log('[SAVE_MEETING_WEBHOOK] Using ElevenLabs conversation history (PRIORITY 2):', {
        historyLength: conversation_history.length,
        firstMessage: conversation_history[0],
        lastMessage: conversation_history[conversation_history.length - 1]
      });

      // Convert ElevenLabs conversation history to our TranscriptMessage format
      transcriptMessages = conversation_history.map((message: any, index: number) => {
        const role = message.role === 'user' ? 'user' : 'assistant';
        const content = message.content || message.text || message.message || '';
        const timestamp = message.timestamp ? new Date(message.timestamp) : new Date(Date.now() - (conversation_history.length - index) * 30000);

        return {
          role: role as 'user' | 'assistant' | 'agent',
          content,
          timestamp
        };
      });

      console.log('[SAVE_MEETING_WEBHOOK] Converted ElevenLabs conversation history to transcript messages:', {
        originalLength: conversation_history.length,
        convertedLength: transcriptMessages.length,
        userMessages: transcriptMessages.filter(m => m.role === 'user').length,
        assistantMessages: transcriptMessages.filter(m => m.role === 'assistant').length
      });
    } else if (!uiTranscriptFound) {
      console.log('[SAVE_MEETING_WEBHOOK] No UI transcript or conversation history available, creating structured summary document (PRIORITY 3)');

      // Fallback: Create transcript messages from the summary and action items
      transcriptMessages = [
        {
          role: 'assistant',
          content: `Meeting Summary Generated by Agent`,
          timestamp: new Date(Date.now() - 60000) // 1 minute ago
        },
        {
          role: 'assistant',
          content: `Summary: ${summary}`,
          timestamp: new Date(Date.now() - 30000) // 30 seconds ago
        }
      ];

      // Add action items if provided
      if (action_items && typeof action_items === 'string' && action_items.trim().length > 0) {
        transcriptMessages.push({
          role: 'assistant',
          content: `Action Items: ${action_items}`,
          timestamp: new Date(Date.now() - 45000) // 45 seconds ago
        });
      }

      // Add web search results and research findings if available
      if (search_queries && Array.isArray(search_queries) && search_queries.length > 0) {
        transcriptMessages.push({
          role: 'assistant',
          content: `Web Search Queries Performed: ${search_queries.join(', ')}`,
          timestamp: new Date(Date.now() - 30000) // 30 seconds ago
        });
      }

      if (research_findings && typeof research_findings === 'string' && research_findings.trim().length > 0) {
        transcriptMessages.push({
          role: 'assistant',
          content: `Research Findings: ${research_findings}`,
          timestamp: new Date(Date.now() - 15000) // 15 seconds ago
        });
      }

      // Add final summary message
      transcriptMessages.push({
        role: 'assistant',
        content: `Meeting documentation completed. Summary, action items, and research findings have been saved.`,
        timestamp: new Date()
      });
    }

    console.log('[SAVE_MEETING_WEBHOOK] 🎯 FINAL TRANSCRIPT SOURCE ANALYSIS (BEFORE AGENT ID RESOLUTION):', {
      transcriptLength: transcriptMessages.length,
      source: uiTranscriptFound ? 'UI_CAPTURE_WITH_RESOLVED_AGENT_ID' :
              (conversation_history?.length > 0 ? 'ELEVENLABS_HISTORY' : 'SUMMARY_FALLBACK'),
      userMessages: transcriptMessages.filter(m => m.role === 'user').length,
      assistantMessages: transcriptMessages.filter(m => m.role === 'assistant').length,
      hasFullConversation: transcriptMessages.length > 2, // More than just summary messages
      uiTranscriptFound,
      webhookAgentId: agent_id,
      note: uiTranscriptFound ? 'SUCCESS: Retrieved actual conversation using resolved ElevenLabs agent ID' :
            (conversation_history?.length > 0 ? 'Using ElevenLabs conversation history' : 'Using summary fallback'),
      criticalFix: 'Transcript retrieval now happens AFTER agent ID resolution for proper key matching'
    });

    // ✅ STEP 1: Resolve agent ID from webhook data
    let finalAgentType = metadataAgentType;
    let mappedAgentId = 'unknown';
    let actualElevenLabsAgentId = agent_id; // Start with webhook agent ID

    console.log('[SAVE_MEETING_WEBHOOK] 🔍 AGENT TYPE RESOLUTION DEBUG:', {
      metadataAgentType,
      finalAgentType,
      conversationInitiationAgentType: conversation_initiation_client_data?.agentType,
      transcriptMetadataAgentType: transcript_metadata?.agentType,
      hasConversationData: !!conversation_initiation_client_data,
      hasTranscriptMetadata: !!transcript_metadata,
      webhookAgentId: agent_id,
      agentName: body.agent_name
    });

    // ✅ CRITICAL FIX: If agent_id is missing, resolve it from transcript state API
    if (!actualElevenLabsAgentId) {
      console.log('[SAVE_MEETING_WEBHOOK] 🔧 Agent ID missing from webhook, checking transcript state API...');

      try {
        // Check transcript state API for the agent ID - try multiple lookup strategies
        const transcriptStateUrl = `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/transcript-state`;

        // Try with user ID first
        let transcriptStateResponse = await fetch(`${transcriptStateUrl}?userId=${encodeURIComponent(user_id)}`, {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' }
        });

        console.log('[SAVE_MEETING_WEBHOOK] 🔍 Transcript state API response (userId lookup):', {
          status: transcriptStateResponse.status,
          statusText: transcriptStateResponse.statusText,
          ok: transcriptStateResponse.ok
        });

        if (transcriptStateResponse.ok) {
          const transcriptStateData = await transcriptStateResponse.json();
          console.log('[SAVE_MEETING_WEBHOOK] 🔍 Transcript state API data (userId lookup):', {
            success: transcriptStateData.success,
            hasTranscriptState: !!transcriptStateData.transcriptState,
            agentId: transcriptStateData.transcriptState?.agentId,
            conversationId: transcriptStateData.transcriptState?.conversationId,
            foundKey: transcriptStateData.foundKey
          });

          if (transcriptStateData.success && transcriptStateData.transcriptState?.agentId) {
            actualElevenLabsAgentId = transcriptStateData.transcriptState.agentId;
            console.log('[SAVE_MEETING_WEBHOOK] ✅ Resolved agent ID from transcript state:', {
              userId: user_id,
              resolvedAgentId: actualElevenLabsAgentId,
              source: 'Transcript_State_API_userId',
              conversationId: transcriptStateData.transcriptState.conversationId,
              foundKey: transcriptStateData.foundKey
            });
          } else {
            console.warn('[SAVE_MEETING_WEBHOOK] ⚠️ No agent ID found in transcript state for user:', user_id);
          }
        } else {
          console.warn('[SAVE_MEETING_WEBHOOK] ⚠️ Transcript state API request failed:', transcriptStateResponse.status);
        }
      } catch (resolveError) {
        console.error('[SAVE_MEETING_WEBHOOK] Error resolving agent ID from transcript state:', resolveError);
      }

      // Fallback: Try resolving from agent_name if transcript state fails
      if (!actualElevenLabsAgentId && body.agent_name) {
        console.log('[SAVE_MEETING_WEBHOOK] 🔧 Fallback: Resolving from agent_name:', body.agent_name);

        try {
          const { findAgentByExactName } = await import('../../../../lib/elevenlabs/agentValidation');
          const apiKey = process.env.NEXT_PUBLIC_ELEVENLABS_COMPANY_API_KEY;

          if (apiKey) {
            const searchResult = await findAgentByExactName(body.agent_name, apiKey);

            if (searchResult.found && searchResult.agent) {
              actualElevenLabsAgentId = searchResult.agent.agent_id;
              console.log('[SAVE_MEETING_WEBHOOK] ✅ Resolved agent ID from name (fallback):', {
                agentName: body.agent_name,
                resolvedAgentId: actualElevenLabsAgentId,
                source: 'ElevenLabs_API_Search_Fallback'
              });
            }
          }
        } catch (fallbackError) {
          console.error('[SAVE_MEETING_WEBHOOK] Fallback agent name resolution failed:', fallbackError);
        }
      }
    }

    // ✅ CRITICAL FIX: If agent_id is "unknown", try to resolve from UI state
    if (agent_id === 'unknown' && selectedDocumentCategory) {
      console.log('[SAVE_MEETING_WEBHOOK] 🔧 Agent ID is unknown, attempting to resolve from UI state...');

      // Try to get agent type from category state
      for (const userId of userIdsToCheck) {
        try {
          const categoryStateResponse = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/document-category-state?userId=${encodeURIComponent(userId)}`, {
            method: 'GET',
            headers: { 'Content-Type': 'application/json' }
          });

          if (categoryStateResponse.ok) {
            const categoryStateData = await categoryStateResponse.json();

            if (categoryStateData.success && categoryStateData.categoryState?.agentType) {
              const uiAgentType = categoryStateData.categoryState.agentType;
              console.log('[SAVE_MEETING_WEBHOOK] ✅ Found agent type from UI state:', {
                userId,
                agentType: uiAgentType,
                note: 'Will attempt to resolve ElevenLabs agent ID from this agent type'
              });

              // Try to find the ElevenLabs agent ID for this agent type
              try {
                const { verifyAgentInElevenLabsByName } = await import('../../../../lib/elevenlabs/agentValidation');
                const apiKey = process.env.NEXT_PUBLIC_ELEVENLABS_COMPANY_API_KEY;

                if (apiKey) {
                  // Construct the expected agent name pattern: userId-pmo-agentType
                  const expectedAgentName = `${userId}-pmo-${uiAgentType}`;
                  console.log('[SAVE_MEETING_WEBHOOK] 🔍 Searching for ElevenLabs agent:', expectedAgentName);

                  const elevenLabsResult = await verifyAgentInElevenLabsByName(expectedAgentName, apiKey);

                  if (elevenLabsResult.exists && elevenLabsResult.agentData) {
                    actualElevenLabsAgentId = elevenLabsResult.agentData.agent_id;
                    finalAgentType = uiAgentType;

                    console.log('[SAVE_MEETING_WEBHOOK] ✅ Successfully resolved ElevenLabs agent ID from UI state:', {
                      originalAgentId: agent_id,
                      resolvedAgentId: actualElevenLabsAgentId,
                      agentType: finalAgentType,
                      agentName: expectedAgentName,
                      note: 'Knowledge base upload will now be enabled'
                    });
                    break; // Found the agent, stop searching
                  }
                }
              } catch (resolveError) {
                console.warn('[SAVE_MEETING_WEBHOOK] Could not resolve ElevenLabs agent ID from UI state:', resolveError);
              }
            }
          }
        } catch (error) {
          console.warn('[SAVE_MEETING_WEBHOOK] Error checking category state for agent resolution:', error);
        }
      }
    }

    // ✅ CRITICAL: ElevenLabs is not transmitting conversation initiation data properly
    // We need to implement a robust fallback system using available data
    console.log('[SAVE_MEETING_WEBHOOK] 🚨 ElevenLabs data transmission issue detected:', {
      agentIdMissing: !agent_id,
      conversationDataEmpty: Object.keys(conversation_initiation_client_data || {}).length === 0,
      onlyHasTranscriptMetadata: !!transcript_metadata
    });

    // ✅ CRITICAL FIX: Use the agent ID directly from the webhook payload
    // DO NOT use database lookup as it returns stale/incorrect agent IDs
    console.log('[SAVE_MEETING_WEBHOOK] 🔧 USING WEBHOOK AGENT ID DIRECTLY:', {
      webhookAgentId: agent_id,
      reason: 'Database lookup returns stale agent IDs - using webhook payload directly'
    });

    // ✅ CRITICAL FIX: Extract agent type from ElevenLabs agent name directly
    // The agent name follows pattern: userId-pmo-agentType
    console.log('[SAVE_MEETING_WEBHOOK] 🔧 EXTRACTING AGENT TYPE FROM ELEVENLABS API:', {
      agent_id,
      metadataAgentType,
      reason: 'Avoiding database lookups that return stale data'
    });

    if (!finalAgentType && agent_id) {
      try {
        // Call ElevenLabs API to get the current agent name
        const apiKey = process.env.NEXT_PUBLIC_ELEVENLABS_COMPANY_API_KEY;
        if (apiKey) {
          const response = await fetch(`https://api.elevenlabs.io/v1/convai/agents/${agent_id}`, {
            headers: {
              'xi-api-key': apiKey,
              'Content-Type': 'application/json'
            }
          });

          if (response.ok) {
            const agentData = await response.json();
            const agentName = agentData.name;

            console.log('[SAVE_MEETING_WEBHOOK] ✅ Retrieved agent name from ElevenLabs:', {
              agentId: agent_id,
              agentName
            });

            // Extract agent type from agent name pattern: userId-pmo-agentType
            if (agentName && agentName.includes('-pmo-')) {
              const agentNameParts = agentName.split('-');
              if (agentNameParts.length >= 3 && agentNameParts[1] === 'pmo') {
                finalAgentType = agentNameParts[2];
                console.log('[SAVE_MEETING_WEBHOOK] 🎯 Extracted agent type from ElevenLabs agent name:', {
                  agentName,
                  extractedAgentType: finalAgentType
                });
              }
            }
          } else {
            console.error('[SAVE_MEETING_WEBHOOK] Failed to fetch agent from ElevenLabs:', response.status, response.statusText);
          }
        }
      } catch (error) {
        console.error('[SAVE_MEETING_WEBHOOK] Error fetching agent from ElevenLabs:', error);
      }
    }

    // If no agent type found yet, try to infer from conversation content as fallback
    if (!finalAgentType) {
      console.log('[SAVE_MEETING_WEBHOOK] No agent type found in metadata or database, inferring from content...');

      // Look for agent type hints in the summary or conversation content
      const contentToAnalyze = `${summary} ${action_items || ''} ${research_findings || ''}`.toLowerCase();

      if (contentToAnalyze.includes('marketing') || contentToAnalyze.includes('strategic')) {
        finalAgentType = 'Marketing';
      } else if (contentToAnalyze.includes('research') || contentToAnalyze.includes('analysis')) {
        finalAgentType = 'Research';
      } else if (contentToAnalyze.includes('sales') || contentToAnalyze.includes('revenue')) {
        finalAgentType = 'Sales';
      } else if (contentToAnalyze.includes('software') || contentToAnalyze.includes('development')) {
        finalAgentType = 'SoftwareDesign';
      } else if (contentToAnalyze.includes('business') || contentToAnalyze.includes('process')) {
        finalAgentType = 'BusinessAnalysis';
      }

      if (finalAgentType) {
        console.log('[SAVE_MEETING_WEBHOOK] Inferred agent type from content:', finalAgentType);
      }
    }

    // STEP 2: Map agent type to PMO Agent ID (same as manual save path)
    if (finalAgentType) {
      try {
        const { getTeamIdFromAgentType } = await import('../../../../lib/utils/teamNameUtils');
        const teamId = getTeamIdFromAgentType(finalAgentType);
        mappedAgentId = teamId || 'unknown';

        console.log('[SAVE_MEETING_WEBHOOK] ✅ Successfully mapped agent type to PMO Agent ID:', {
          originalElevenLabsAgentId: agent_id,
          actualElevenLabsAgentId,
          finalAgentType,
          pmoAgentId: teamId,
          mappedPMOAgentId: mappedAgentId,
          source: metadataAgentType === finalAgentType ? 'metadata' : 'database_lookup',
          note: 'PMO Agent ID (Ag001, Ag002, etc.) used for Firebase storage - NOT ElevenLabs agent ID'
        });
      } catch (error) {
        console.error('[SAVE_MEETING_WEBHOOK] Error mapping agent type to PMO ID:', error);
        mappedAgentId = 'unknown';
      }
    } else {
      console.warn('[SAVE_MEETING_WEBHOOK] ❌ No agent type found, using unknown. Check agent creation and database storage.');
      mappedAgentId = 'unknown';
    }

    // ✅ CRITICAL FIX: NOW RETRIEVE TRANSCRIPT WITH RESOLVED AGENT ID
    console.log('[SAVE_MEETING_WEBHOOK] 🎯 Starting transcript retrieval with resolved agent ID...');
    console.log('[SAVE_MEETING_WEBHOOK] 🔍 AGENT ID RESOLUTION COMPLETE:', {
      webhookAgentId: agent_id,
      actualElevenLabsAgentId,
      willUseForRetrieval: actualElevenLabsAgentId || agent_id,
      note: 'Using resolved ElevenLabs agent ID for transcript retrieval'
    });

    // ✅ Use the resolved ElevenLabs agent ID for transcript retrieval
    const agentIdForRetrieval = actualElevenLabsAgentId || agent_id;

    // ✅ ENHANCED USER-SCOPED RETRIEVAL: Handle "unknown" agent/conversation IDs
    const retrievalStrategies = [
      // Strategy 1: User-scoped with resolved agent ID (primary)
      { userId: user_id, agentId: agentIdForRetrieval, conversationId: conversation_id !== 'unknown' ? conversation_id : '', name: 'user_scoped_resolved_agent' },
      // Strategy 2: Fallback user-scoped with resolved agent ID
      { userId: '<EMAIL>', agentId: agentIdForRetrieval, conversationId: conversation_id !== 'unknown' ? conversation_id : '', name: 'fallback_user_scoped_resolved_agent' },
      // Strategy 3: User-scoped with original agent ID (if different and not "unknown")
      { userId: user_id, agentId: agent_id !== 'unknown' ? agent_id : '', conversationId: conversation_id !== 'unknown' ? conversation_id : '', name: 'user_scoped_original_agent' },
      // Strategy 4: Fallback user-scoped with original agent ID (if not "unknown")
      { userId: '<EMAIL>', agentId: agent_id !== 'unknown' ? agent_id : '', conversationId: conversation_id !== 'unknown' ? conversation_id : '', name: 'fallback_user_scoped_original_agent' },
      // Strategy 5: User-scoped latest transcript (when IDs are unknown)
      { userId: user_id, agentId: '', conversationId: '', name: 'user_scoped_latest_transcript' },
      // Strategy 6: Fallback user-scoped latest transcript
      { userId: '<EMAIL>', agentId: '', conversationId: '', name: 'fallback_user_scoped_latest_transcript' },
      // Strategy 7: Legacy direct lookup (backward compatibility)
      { userId: '', agentId: agentIdForRetrieval, conversationId: '', name: 'legacy_direct_agent_lookup' },
      // Strategy 8: Legacy fallback user
      { userId: '<EMAIL>', agentId: '', conversationId: '', name: 'legacy_fallback_user' }
    ];

    for (const strategy of retrievalStrategies) {
      if (uiTranscriptFound) break; // Stop if we found a transcript

      try {
        const transcriptStateUrl = `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/transcript-state?userId=${encodeURIComponent(strategy.userId || '')}&agentId=${encodeURIComponent(strategy.agentId || '')}&conversationId=${encodeURIComponent(strategy.conversationId || '')}`;

        console.log('[SAVE_MEETING_WEBHOOK] 🔍 Attempting USER-SCOPED retrieval strategy:', strategy.name, {
          url: transcriptStateUrl,
          userId: strategy.userId,
          agentId: strategy.agentId,
          conversationId: strategy.conversationId,
          resolvedAgentId: agentIdForRetrieval,
          expectedUserScopedKeys: strategy.userId ? [
            `users/${strategy.userId}/transcripts/conversation:${strategy.conversationId}`,
            `users/${strategy.userId}/transcripts/agent:${strategy.agentId}`,
            `users/${strategy.userId}/transcripts/latest`
          ] : ['legacy_keys'],
          note: 'Using user-scoped collection structure: users/{userId}/transcripts/{identifier}'
        });

        const transcriptStateResponse = await fetch(transcriptStateUrl, {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' }
        });

        console.log('[SAVE_MEETING_WEBHOOK] 🔍 Transcript state response for strategy:', strategy.name, {
          status: transcriptStateResponse.status,
          statusText: transcriptStateResponse.statusText,
          ok: transcriptStateResponse.ok
        });

        if (transcriptStateResponse.ok) {
          const transcriptStateData = await transcriptStateResponse.json();

          console.log('[SAVE_MEETING_WEBHOOK] 🔍 Transcript state data for strategy:', strategy.name, {
            success: transcriptStateData.success,
            hasTranscriptState: !!transcriptStateData.transcriptState,
            dialogueLength: transcriptStateData.transcriptState?.dialogue?.length || 0,
            foundKey: transcriptStateData.foundKey,
            agentId: transcriptStateData.transcriptState?.agentId,
            conversationId: transcriptStateData.transcriptState?.conversationId
          });

          // ✅ Accept transcript even if success is false, as long as we have dialogue data
          if (transcriptStateData.transcriptState?.dialogue?.length > 0) {
            // Convert timestamp strings back to Date objects
            transcriptMessages = transcriptStateData.transcriptState.dialogue.map((msg: any) => ({
              ...msg,
              timestamp: new Date(msg.timestamp)
            }));

            console.log('[SAVE_MEETING_WEBHOOK] ✅ Retrieved transcript from UI capture with resolved agent ID:', {
              strategy: strategy.name,
              foundKey: transcriptStateData.foundKey,
              dialogueLength: transcriptMessages.length,
              userMessages: transcriptMessages.filter(m => m.role === 'user').length,
              assistantMessages: transcriptMessages.filter(m => m.role === 'assistant').length,
              agentName: transcriptStateData.transcriptState.agentName,
              agentType: transcriptStateData.transcriptState.agentType,
              resolvedAgentId: agentIdForRetrieval,
              storedAgentId: transcriptStateData.transcriptState.agentId,
              note: 'SUCCESS: Retrieved actual conversation using resolved ElevenLabs agent ID'
            });
            uiTranscriptFound = true;
            break;
          } else {
            console.log('[SAVE_MEETING_WEBHOOK] ⚠️ No valid transcript found for strategy:', strategy.name, {
              success: transcriptStateData.success,
              hasTranscriptState: !!transcriptStateData.transcriptState,
              hasDialogue: !!transcriptStateData.transcriptState?.dialogue,
              dialogueLength: transcriptStateData.transcriptState?.dialogue?.length || 0,
              reason: !transcriptStateData.transcriptState ? 'No transcriptState in response' :
                      !transcriptStateData.transcriptState.dialogue ? 'No dialogue in transcriptState' :
                      'Dialogue array is empty'
            });
          }
        } else {
          console.log('[SAVE_MEETING_WEBHOOK] ⚠️ Failed to retrieve transcript state for strategy:', strategy.name, {
            status: transcriptStateResponse.status,
            statusText: transcriptStateResponse.statusText,
            url: transcriptStateUrl
          });
        }
      } catch (uiTranscriptError) {
        console.log('[SAVE_MEETING_WEBHOOK] ⚠️ Error retrieving UI transcript for strategy:', strategy.name, {
          error: uiTranscriptError instanceof Error ? uiTranscriptError.message : String(uiTranscriptError)
        });
      }
    }

    console.log('[SAVE_MEETING_WEBHOOK] 🔍 TRANSCRIPT RETRIEVAL SUMMARY WITH RESOLVED AGENT ID:', {
      uiTranscriptFound,
      transcriptMessagesLength: transcriptMessages.length,
      strategiesAttempted: retrievalStrategies.length,
      resolvedAgentId: agentIdForRetrieval,
      webhookAgentId: agent_id,
      conversationId: conversation_id,
      finalTranscriptSource: uiTranscriptFound ? 'UI_CAPTURE_WITH_RESOLVED_AGENT_ID' : 'WILL_FALLBACK_TO_ELEVENLABS_OR_SUMMARY'
    });

    // ✅ STEP 2: Extract metadata for transcript saving - use UI values (same as manual save)
    const agentName = finalAgentType ? `${finalAgentType} Agent` : 'PMO Agent';
    const agentType = finalAgentType || 'PMO';

    // ✅ CRITICAL FIX: Use stored category state FIRST, then fallback to agent-provided category
    // The agent often makes up categories like "Market Research" instead of using the real UI category
    const agentProvidedCategory = transcript_metadata?.documentCategory;
    let category = selectedDocumentCategory || agentProvidedCategory || 'Meeting Transcript';

    console.log('[SAVE_MEETING_WEBHOOK] 🚨 CRITICAL: AGENT vs UI CATEGORY COMPARISON:', {
      uiCapturedCategory: selectedDocumentCategory,
      agentProvidedCategory: agentProvidedCategory,
      finalCategoryUsed: category,
      categorySource: selectedDocumentCategory ? 'UI_State_Capture_API' :
                     agentProvidedCategory ? 'Agent_Provided_Metadata' : 'default_fallback',
      agentOverrodeUI: !selectedDocumentCategory && !!agentProvidedCategory,
      uiCategoryWins: !!selectedDocumentCategory,
      note: 'If agentOverrodeUI=true, the ElevenLabs agent changed the category!'
    });

    console.log('[SAVE_MEETING_WEBHOOK] ✅ CATEGORY RESOLUTION WITH UI STATE PRIORITY:', {
      finalCategory: category,
      source: selectedDocumentCategory ? 'UI_State_Capture_API' :
              agentProvidedCategory ? 'Agent_Provided_Metadata' : 'default_fallback',
      selectedDocumentCategory,
      agentProvidedCategory,
      note: 'Priority: UI State Capture → Agent Metadata → Default Fallback'
    });

    const finalDocumentTitle = document_title || `Meeting Summary - ${agentName} - ${new Date().toLocaleDateString()}`;

    console.log('[SAVE_MEETING_WEBHOOK] ✅ FINAL DOCUMENT METADATA:', {
      title: finalDocumentTitle,
      category: category,
      agentName: agentName,
      pmoAgentId: mappedAgentId,
      elevenLabsAgentId: agent_id,
      note: 'Category is direct from SelectedDocumentsDisplay.tsx line 161 {document.category}'
    });

    // Create transcript metadata
    const transcriptMetadata: TranscriptMetadata = {
      agentName,
      agentType,
      documentTitle: finalDocumentTitle,
      category,
      startTime: new Date(Date.now() - (transcriptMessages.length * 30000)), // Estimate start time
      endTime: new Date(),
      totalMessages: transcriptMessages.length,
      userMessages: transcriptMessages.filter(m => m.role === 'user').length,
      agentMessages: transcriptMessages.filter(m => m.role === 'assistant').length,
      pmoAgentId: mappedAgentId, // ✅ Include PMO agent ID for proper file categorization
      elevenLabsAgentId: actualElevenLabsAgentId // ✅ Include ElevenLabs agent ID for KB operations
    };

    console.log('[SAVE_MEETING_WEBHOOK] Saving transcript with metadata:', transcriptMetadata);

    // Save transcript using existing utility with timeout tracking
    console.log('[SAVE_MEETING_WEBHOOK] Starting transcript save process...');
    const saveStartTime = Date.now();

    // Use early-ack pattern: save PDF first, then handle KB upload separately
    // ✅ Use PMO agent ID for file categorization (same as manual save)
    console.log('[SAVE_MEETING_WEBHOOK] Saving transcript with agent IDs:', {
      elevenLabsAgentId: actualElevenLabsAgentId,
      mappedPMOAgentId: mappedAgentId,
      usingForSave: mappedAgentId !== 'unknown' ? mappedAgentId : (actualElevenLabsAgentId || 'unknown')
    });

    // ✅ Enable knowledge base upload if we have a valid ElevenLabs agent ID
    const shouldUploadToKB = !!(actualElevenLabsAgentId && actualElevenLabsAgentId !== 'unknown');

    console.log('[SAVE_MEETING_WEBHOOK] Knowledge base upload decision:', {
      actualElevenLabsAgentId,
      shouldUploadToKB,
      reason: shouldUploadToKB ? 'Valid ElevenLabs agent ID found' : 'No valid ElevenLabs agent ID'
    });

    // ✅ CRITICAL DEBUG: Log exactly what agent ID is being passed to saveTranscript
    const agentIdForTranscriptSave = shouldUploadToKB ? actualElevenLabsAgentId : mappedAgentId;
    console.log('[SAVE_MEETING_WEBHOOK] 🔍 CRITICAL DEBUG - Agent ID being passed to saveTranscript:', {
      mappedAgentId,
      actualElevenLabsAgentId,
      agentIdForTranscriptSave,
      shouldUploadToKB,
      finalAgentType,
      agentIdType: typeof agentIdForTranscriptSave,
      agentIdLength: agentIdForTranscriptSave?.length,
      note: shouldUploadToKB ? 'Using ElevenLabs agent ID for KB upload' : 'Using PMO agent ID for file categorization only'
    });

    const saveResult = await saveTranscript(transcriptMessages, {
      userId: user_id,
      agentId: agentIdForTranscriptSave, // ✅ Use ElevenLabs agent ID for KB upload, PMO agent ID for file categorization
      metadata: transcriptMetadata,
      uploadToKnowledgeBase: shouldUploadToKB, // ✅ Enable KB upload if we have valid agent ID
      forceUpload: false,
      onProgress: (step: string, message: string, progress?: number) => {
        const elapsed = Date.now() - saveStartTime;
        console.log(`[SAVE_MEETING_WEBHOOK] Progress (${elapsed}ms) - ${step}: ${message} (${progress || 0}%)`);
      }
    });

    const saveElapsed = Date.now() - saveStartTime;
    console.log(`[SAVE_MEETING_WEBHOOK] PDF generation completed in ${saveElapsed}ms`);

    if (!saveResult.success) {
      console.error('[SAVE_MEETING_WEBHOOK] Failed to save transcript:', saveResult.error);
      return NextResponse.json({
        error: 'Failed to save transcript',
        details: saveResult.error
      }, { status: 500 });
    }

    // Check if we should return early to avoid timeout
    if (shouldReturnEarly()) {
      console.log('[SAVE_MEETING_WEBHOOK] Returning early to avoid timeout, KB upload will continue in background');

      // Start background KB upload (fire and forget)
      if (saveResult.pdfUrl && (agent_id || 'unknown') !== 'unknown') {
        setImmediate(async () => {
          try {
            console.log('[SAVE_MEETING_WEBHOOK] Starting background KB upload...');
            // TODO: Implement background KB upload here
            console.log('[SAVE_MEETING_WEBHOOK] Background KB upload completed');
          } catch (error) {
            console.error('[SAVE_MEETING_WEBHOOK] Background KB upload failed:', error);
          }
        });
      }

      return NextResponse.json({
        success: true,
        message: 'Meeting summary saved successfully',
        pdfUrl: saveResult.pdfUrl,
        fileName: saveResult.fileName,
        note: 'Knowledge base upload continuing in background'
      });
    }

    console.log('[SAVE_MEETING_WEBHOOK] Transcript saved successfully:', {
      pdfUrl: saveResult.pdfUrl,
      fileName: saveResult.fileName,
      knowledgeBaseId: saveResult.knowledgeBaseId
    });

    // Handle document generation if requested
    let documentGenerationResult: MeetingDocumentGenerationResult | null = null;

    if (generate_document) {
      try {
        console.log('[SAVE_MEETING_WEBHOOK] Document generation requested:', {
          title: finalDocumentTitle,
          summary_length: summary.length
        });

        // ✅ CRITICAL DEBUG: Check what agent ID we're about to use
        const finalAgentIdForGeneration = actualElevenLabsAgentId && actualElevenLabsAgentId !== 'unknown' ? actualElevenLabsAgentId : undefined;

        console.log('[SAVE_MEETING_WEBHOOK] 🔍 CRITICAL - Agent ID resolution before document generation:', {
          originalAgentId: agent_id,
          actualElevenLabsAgentId,
          finalAgentIdForGeneration,
          willSkipKnowledgeBase: !finalAgentIdForGeneration,
          documentCategory: category,
          agentIdType: typeof finalAgentIdForGeneration,
          agentIdLength: finalAgentIdForGeneration?.length,
          agentIdStartsWith: finalAgentIdForGeneration?.substring(0, 10),
          isMarketingDirector: finalAgentIdForGeneration === 'Marketing Director'
        });

        // ✅ CRITICAL: Show the distinction between PMO and ElevenLabs agent IDs
        console.log('[SAVE_MEETING_WEBHOOK] 🔍 Agent ID Usage Summary:', {
          pmoAgentId: mappedAgentId, // Used for Firebase files collection storage (Ag001, Ag002, etc.)
          elevenLabsAgentId: finalAgentIdForGeneration, // Used for knowledge base operations (agent_xxx format)
          willPassToGenerateDocument: finalAgentIdForGeneration,
          willStoreInFirebaseFiles: mappedAgentId, // PMO Agent ID stored in Firebase
          knowledgeBaseOperationsEnabled: !!finalAgentIdForGeneration,
          note: 'PMO Agent ID and ElevenLabs Agent ID serve different purposes'
        });

        // Create PMO record first
        let pmoId: string | null = null;
        try {
          console.log('[SAVE_MEETING_WEBHOOK] Creating PMO record...');
          pmoId = await createPMORecordFromForm(user_id, {
            title: finalDocumentTitle,
            description: summary,
            priority: 'Medium',
            category: 'Meeting Documents'
          });

          console.log('[SAVE_MEETING_WEBHOOK] PMO record created:', pmoId);
        } catch (pmoError) {
          console.error('[SAVE_MEETING_WEBHOOK] Error creating PMO record:', pmoError);
        }

        // ✅ Use the category we already resolved from conversation initiation data
        const documentCategory = category || 'Meeting Transcript';
        console.log('[SAVE_MEETING_WEBHOOK] 🔍 CRITICAL CATEGORY TRACE - Final category being passed to generateMeetingDocument:', {
          documentCategory,
          originalCategory: category,
          selectedDocumentCategory,
          primaryCategoryFromUI: selectedDocumentCategory,
          agentProvidedCategory: transcript_metadata?.documentCategory,
          source: 'conversation_initiation_or_fallback',
          categoryFlow: 'primaryCategory → selectedDocumentCategory → category → documentCategory → processPMODocument'
        });

        // Generate the meeting document with PMO Agent ID (same as transcript save)
        const titleWithPmoId = pmoId ? `[${pmoId}] ${finalDocumentTitle}` : finalDocumentTitle;

        console.log('[SAVE_MEETING_WEBHOOK] Generating document with dual agent ID support:', {
          finalAgentType,
          pmoAgentId: mappedAgentId, // For Firebase storage (Ag001, Ag002, etc.)
          elevenLabsAgentId: finalAgentIdForGeneration, // For knowledge base operations
          documentCategory, // From SelectedDocumentsDisplay.tsx line 161
          titleWithPmoId,
          note: 'Using separate agent IDs for Firebase storage and Knowledge Base operations'
        });

        // ✅ CRITICAL FIX: Pass both agent IDs to support both file storage and Knowledge Base operations
        // PMO agent ID (Ag001, Ag002, etc.) for Firebase file storage
        // ElevenLabs agent ID for Knowledge Base operations
        const pmoAgentIdForStorage = mappedAgentId !== 'unknown' ? mappedAgentId : undefined;
        const elevenLabsAgentIdForKB = finalAgentIdForGeneration || undefined;

        console.log('[SAVE_MEETING_WEBHOOK] 🔧 CRITICAL FIX - Using both agent IDs for document generation:', {
          elevenLabsAgentId: elevenLabsAgentIdForKB,
          pmoAgentId: pmoAgentIdForStorage,
          willUploadToKnowledgeBase: !!elevenLabsAgentIdForKB,
          willStoreInFirebase: !!pmoAgentIdForStorage,
          reason: 'Separate agent IDs for different operations'
        });

        documentGenerationResult = await generateMeetingDocument({
          title: titleWithPmoId,
          category: documentCategory, // ✅ CRITICAL: Category from SelectedDocumentsDisplay.tsx line 161 {document.category}
          meetingTranscript: transcriptMessages,
          agentId: pmoAgentIdForStorage, // ✅ PMO agent ID for Firebase storage (Ag001, Ag002, etc.)
          elevenLabsAgentId: elevenLabsAgentIdForKB, // ✅ ElevenLabs agent ID for Knowledge Base operations
          userId: user_id,
          webSearchResults: web_search_results, // Include web search results from the meeting
          additionalContext: uiTranscriptFound ?
            // If we have the full transcript, only include truly supplementary information
            `${research_findings ? `Research Findings: ${research_findings}` : ''}${(search_queries && Array.isArray(search_queries) && search_queries.length > 0) ? `${research_findings ? '\n\n' : ''}Search Queries: ${search_queries.join(', ')}` : ''}`.trim() || undefined :
            // If we don't have the full transcript, include summary and action items as context
            `Meeting Summary: ${summary || 'No summary provided'}${action_items ? `\n\nAction Items: ${action_items}` : ''}${research_findings ? `\n\nResearch Findings: ${research_findings}` : ''}${(search_queries && Array.isArray(search_queries) && search_queries.length > 0) ? `\n\nSearch Queries: ${search_queries.join(', ')}` : ''}`,
          pmoId: pmoId || undefined,
          documentId: selectedDocumentId || undefined, // ✅ Use selected document ID if provided
          onProgress: (step: string, message: string, progress?: number) => {
            console.log(`[SAVE_MEETING_WEBHOOK] Document Generation - ${step}: ${message} (${progress || 0}%)`);
          }
        });

        if (documentGenerationResult.success) {
          console.log('[SAVE_MEETING_WEBHOOK] Document generated successfully:', {
            documentId: documentGenerationResult.documentId,
            downloadUrl: documentGenerationResult.downloadUrl,
            knowledgeBaseId: documentGenerationResult.knowledgeBaseId
          });

          // Update PMO record with document details and team assignment
          if (pmoId) {
            try {
              const updateData: any = {
                status: 'Completed',
                summary: `Document generated successfully. Knowledge Base ID: ${documentGenerationResult.knowledgeBaseId || 'N/A'}`
              };

              // Add PMO Agent ID if we have a valid one
              if (mappedAgentId && mappedAgentId !== 'unknown') {
                updateData.agentIds = [mappedAgentId]; // Store PMO Agent ID
              }

              await updatePMORecord(user_id, pmoId, updateData);
              console.log('[SAVE_MEETING_WEBHOOK] PMO record updated with document details');
            } catch (updateError) {
              console.error('[SAVE_MEETING_WEBHOOK] Error updating PMO record:', updateError);
            }
          }
        } else {
          console.error('[SAVE_MEETING_WEBHOOK] Document generation failed:', documentGenerationResult.error);

          // Update PMO record to reflect failure
          if (pmoId) {
            try {
              await updatePMORecord(user_id, pmoId, {
                status: 'Cancelled',
                summary: `Document generation failed: ${documentGenerationResult.error || 'Unknown error'}`
              });
            } catch (updateError) {
              console.error('[SAVE_MEETING_WEBHOOK] Error updating PMO record with failure:', updateError);
            }
          }
        }

      } catch (docGenError) {
        console.error('[SAVE_MEETING_WEBHOOK] Error during document generation:', docGenError);
        documentGenerationResult = {
          success: false,
          error: docGenError instanceof Error ? docGenError.message : 'Unknown error during document generation'
        };
      }
    }

    // Return success response to the agent
    const response = {
      success: true,
      message: 'Meeting summary saved successfully',
      results: {
        transcript: {
          saved: true,
          pdfUrl: saveResult.pdfUrl,
          fileName: saveResult.fileName,
          knowledgeBaseId: saveResult.knowledgeBaseId,
          messageCount: transcriptMessages.length
        },
        documentGeneration: documentGenerationResult
      }
    };

    console.log('[SAVE_MEETING_WEBHOOK] Sending success response:', response);
    return NextResponse.json(response);

  } catch (error) {
    console.error('[SAVE_MEETING_WEBHOOK] Error processing save meeting summary webhook:', error);
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
