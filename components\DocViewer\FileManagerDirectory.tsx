import React, { useState } from 'react';
import { Folder, ChevronDown, ChevronRight, FilesIcon, SpeechIcon, MessageSquare, FileIcon } from 'lucide-react';
import { Timestamp } from 'firebase/firestore';
import { FileData } from './FileManagerstructure';
import FolderList from './FolderList';
import FileList from './FileList';
import { SortButton } from './SortButton';

interface FileManagerDirectoryProps {
  isLoading: boolean;
  showSearchResults: boolean;
  groupedFiles: {
    category: string;
    files: FileData[];
    allFiles?: FileData[]; // All files in the category (for counts)
    isExpanded: boolean;
  }[];
  toggleCategory: (category: string) => void;
  handleFileClick: (file: FileData) => void;
  formatTimestamp: (timestamp: Timestamp | undefined) => string;
  truncateFileName: (fileName: string, maxLength?: number) => string;
  SkeletonLoader: React.ComponentType;
}

export interface SortConfig {
  key: 'files' | 'date' | 'type';
  direction: 'asc' | 'desc';
}

const FileManagerDirectory: React.FC<FileManagerDirectoryProps> = ({
  isLoading,
  showSearchResults,
  groupedFiles,
  toggleCategory,
  handleFileClick,
  formatTimestamp,
  truncateFileName,
  SkeletonLoader,
}) => {
  const [sortConfig, setSortConfig] = useState<SortConfig | null>(null);

  const handleSort = (key: SortConfig['key']) => {
    setSortConfig(current => ({
      key,
      direction: current?.key === key && current?.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  // Sort function for categories/folders
  const sortedGroupedFiles = [...groupedFiles].sort((a, b) => {
    if (a.category === 'Unknown') return -1;
    if (b.category === 'Unknown') return 1;
    return a.category.localeCompare(b.category);
  });

  return (
    <div
      className={`transition-all duration-300 ease-in-out ${
        showSearchResults ? 'w-[68%]' : 'w-full'
      }`}
    >
      <div className="bg-slate-800 bg-opacity-65 rounded-lg overflow-hidden w-full max-h-[calc(100vh-180px)] overflow-y-auto">
        {isLoading ? (
          <SkeletonLoader />
        ) : (
          <>
            <div className="grid grid-cols-6 gap-2 p-2 text-gray-400">
              <div className="col-span-2 ml-1 flex items-center border border-gray-500 w-max p-2 rounded-xl text-left font-bold whitespace-nowrap overflow-hidden text-ellipsis">
              <FilesIcon className="h-5 w-5 mr-2" />
                My list of files
              </div>
              <div className="flex justify-center w-full">
                <div className="border border-gray-500 shadow-md rounded-xl hover:bg-ike-purple hover:text-white inline-flex items-center justify-between px-3 py-1 w-[90%]">
                <FilesIcon className="h-5 w-5 mr-2" />
                  <span>Files</span>
                  <SortButton column="files" sortConfig={sortConfig} onSort={handleSort} />
                </div>
              </div>
              <div className="flex justify-center w-full">
              <div className="border border-gray-500 shadow-md rounded-md px-3 py-1 w-[90%]">
                <div className="flex items-center justify-center">
                  <MessageSquare className="h-5 w-5 mr-1" />
                  <span>Chats</span>
                </div>
              </div>
              </div>
              <div className="flex justify-center w-full">
                <div className="border border-gray-500 shadow-md rounded-md inline-flex items-center  hover:bg-ike-purple hover:text-white justify-between px-3 py-1 w-[90%]">
                  <span>Created Date</span>
                  <SortButton column="date" sortConfig={sortConfig} onSort={handleSort} />
                </div>
              </div>
              <div className="flex justify-center w-full">
                <div className="border border-gray-500 shadow-md rounded-md  hover:bg-ike-purple hover:text-white inline-flex items-center justify-between px-3 py-1 w-[90%]">
                  <span>Type</span>
                  <SortButton column="type" sortConfig={sortConfig} onSort={handleSort} />
                </div>
              </div>
            </div>

            {/* Unknown/Single Files Section */}
            <div className="divide-y divide-gray-700">
              {sortedGroupedFiles
                .filter(({ category }) => category === 'Unknown')
                .map(({ category, files, allFiles, isExpanded }) => (
                  <div key={category}>
                    <div className="grid grid-cols-6 gap-2 p-1 text-left ml-2 mr-2 bg-opacity-65 font-bold text-ike-purple_b items-center mb-1">
                      <div className="col-span-2 flex items-center">
                        <button
                          onClick={() => toggleCategory(category)}
                          className="focus:outline-none mr-2"
                          aria-label={isExpanded ? `Collapse ${category}` : `Expand ${category}`}
                        >
                          {isExpanded ? (
                            <ChevronDown className="h-5 w-5 text-amber-500" />
                          ) : (
                            <ChevronRight className="h-5 w-5 text-amber-500" />
                          )}
                        </button>
                        <FileIcon className="inline mr-2 text-amber-500" />
                        <span className="text-gray-300">Single Files</span>
                      </div>
                      <div className="flex items-center justify-center w-full text-gray-300">
                        {/* Use allFiles for count if available, otherwise use files */}
                        {allFiles?.length || files.length}
                      </div>
                      <div className="flex items-center justify-center w-full text-gray-300">
                        {/* Use allFiles for chat count if available, otherwise use files */}
                        {(allFiles || files).reduce((sum: number, file: FileData) => sum + (file.chatCount || 0), 0)}
                      </div>
                      <div></div>
                      <div></div>
                    </div>
                    {isExpanded && (
                      <div className="max-h-[200px] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800">
                        <FileList
                          files={files}
                          handleFileClick={handleFileClick}
                          truncateFileName={truncateFileName}
                          formatTimestamp={formatTimestamp}
                          sortConfig={sortConfig}
                          variant="unknown"
                        />
                      </div>
                    )}
                  </div>
                ))}
            </div>

            {/* Categorized Files Section */}
            <FolderList
              groupedFiles={groupedFiles.filter(g => g.category !== 'Unknown')}
              toggleCategory={toggleCategory}
              handleFileClick={handleFileClick}
              formatTimestamp={formatTimestamp}
              truncateFileName={truncateFileName}
              sortConfig={sortConfig}
              onDeleteFolder={(category, total) => window.dispatchEvent(new CustomEvent('delete-folder-request', { detail: { category, total } }))}
            />
          </>
        )}
      </div>
    </div>
  );
};

export default FileManagerDirectory;