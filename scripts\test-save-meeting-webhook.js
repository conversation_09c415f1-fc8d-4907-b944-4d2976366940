#!/usr/bin/env node

/**
 * Test script for the save_meeting_summary webhook endpoint
 * 
 * This script simulates an ElevenLabs agent calling the save_meeting_summary tool
 * to verify that the webhook endpoint works correctly.
 * 
 * Usage: node scripts/test-save-meeting-webhook.js
 */

const https = require('https');
const http = require('http');

// Configuration
const WEBHOOK_ENDPOINT = '/api/elevenlabs/save-meeting-summary-webhook';
const BASE_URL = process.env.ELEVENLABS_WEBHOOK_BASE_URL || process.env.NEXTAUTH_URL || 'https://www.ike-ai.com';
const WEBHOOK_SECRET = process.env.ELEVENLABS_WEBHOOK_SECRET || 'pmo-webhook-secret-2024';

// Test payload simulating an agent call
const testPayload = {
  summary: "We discussed the strategic director assessment for the editorial article project. The user asked about the urgency level being marked as high, and I explained that it indicates critical information gaps that need to be addressed for project success. We then conducted web searches on the impact of AI on single, independent women in Australia and the UK, focusing on female-dominated industries like healthcare, education, and social services. Key findings included potential for AI bias and the need for diverse perspectives in AI development.",
  action_items: "1. Review the editorial article requirements to address identified information gaps\n2. Focus on geographic details for Australia and UK\n3. Consider the impact on female-dominated industries\n4. Address potential AI bias concerns in the article",
  document_title: "Meeting Summary - AI Impact Discussion - Marketing Director",
  generate_document: true,
  agent_id: "test_agent_123",
  conversation_id: "test_conv_456",
  user_id: "test_user_789"
};

async function testWebhook() {
  console.log('🧪 Testing save_meeting_summary webhook endpoint');
  console.log('================================================\n');

  console.log('📋 Configuration:');
  console.log(`   Base URL: ${BASE_URL}`);
  console.log(`   Endpoint: ${WEBHOOK_ENDPOINT}`);
  console.log(`   Secret: ${WEBHOOK_SECRET.substring(0, 8)}...`);
  console.log(`   Full URL: ${BASE_URL}${WEBHOOK_ENDPOINT}\n`);

  console.log('📤 Test Payload:');
  console.log(`   Summary length: ${testPayload.summary.length} characters`);
  console.log(`   Action items: ${testPayload.action_items ? 'Yes' : 'No'}`);
  console.log(`   Generate document: ${testPayload.generate_document}`);
  console.log(`   Agent ID: ${testPayload.agent_id}`);
  console.log(`   Conversation ID: ${testPayload.conversation_id}\n`);

  try {
    const response = await makeRequest(BASE_URL + WEBHOOK_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${WEBHOOK_SECRET}`
      },
      body: JSON.stringify(testPayload)
    });

    console.log('✅ Webhook Response:');
    console.log(`   Status: ${response.status}`);
    console.log(`   Response:`, JSON.stringify(response.data, null, 2));

    if (response.status === 200 && response.data.success) {
      console.log('\n🎉 Test PASSED! The webhook endpoint is working correctly.');
      
      if (response.data.results) {
        const { transcript, documentGeneration } = response.data.results;
        
        if (transcript && transcript.saved) {
          console.log(`   ✅ Transcript saved: ${transcript.fileName}`);
          console.log(`   ✅ PDF URL: ${transcript.pdfUrl}`);
          console.log(`   ✅ Knowledge base ID: ${transcript.knowledgeBaseId}`);
          console.log(`   ✅ Message count: ${transcript.messageCount}`);
        }
        
        if (documentGeneration && documentGeneration.success) {
          console.log(`   ✅ Document generated: ${documentGeneration.documentId}`);
          console.log(`   ✅ Download URL: ${documentGeneration.downloadUrl}`);
        }
      }
    } else {
      console.log('\n❌ Test FAILED! Check the response above for details.');
    }

  } catch (error) {
    console.error('\n❌ Test FAILED with error:');
    console.error(`   Error: ${error.message}`);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Troubleshooting:');
      console.log('   - Make sure your development server is running');
      console.log('   - Check that the BASE_URL is correct');
      console.log('   - Verify the webhook endpoint exists');
    }
  }
}

function makeRequest(url, options) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const isHttps = urlObj.protocol === 'https:';
    const client = isHttps ? https : http;
    
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: options.headers || {},
      timeout: 70000 // Increased timeout for webhook testing
    };

    const req = client.request(requestOptions, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedData = JSON.parse(data);
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: parsedData
          });
        } catch (parseError) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: data
          });
        }
      });
    });

    req.on('error', reject);
    req.on('timeout', () => reject(new Error('Request timeout')));
    req.setTimeout(30000);

    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

// Run the test
if (require.main === module) {
  testWebhook().catch(console.error);
}

module.exports = { testWebhook };
