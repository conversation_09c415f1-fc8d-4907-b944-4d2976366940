import { z } from 'zod';
import { processWithGroq } from './groq-ai'; // Adjust the import path as needed
import { TaskPriority, TaskStatus } from '../../admin/planner/types';

// Define the schema for task items
export const TaskItemSchema = z.object({
  title: z.string().describe('The title of the task'),
  description: z.string().describe('Detailed description of what the task involves'),
  priority: z.enum(['Low', 'Medium', 'High', 'Critical'] as const).describe('Priority level of the task'),
  startDate: z.string().describe('Start date in YYYY-MM-DD format'),
  dueDate: z.string().describe('Due date in YYYY-MM-DD format'),
  dependencies: z.array(z.string()).optional().describe('Titles of tasks this task depends on'),
  category: z.string().describe('Category the task belongs to'),
  notes: z.string().optional().describe('Additional notes about the task'),
});

export type TaskItem = z.infer<typeof TaskItemSchema>;

// Helper function to normalize priority values
function normalizePriority(priority: string): TaskPriority {
  const normalizedPriority = priority.trim().toLowerCase();

  if (normalizedPriority === 'low') return 'Low';
  if (normalizedPriority === 'medium') return 'Medium';
  if (normalizedPriority === 'high') return 'High';
  if (normalizedPriority === 'critical') return 'Critical';

  // Default to Medium if not recognized
  return 'Medium';
}

// Define the schema for the response
export const GenerateTasksResponseSchema = z.object({
  reasoning: z.string().describe('Reasoning behind the generated tasks'),
  tasks: z.array(TaskItemSchema).describe('List of generated tasks in chronological order'),
});

export type GenerateTasksResponse = z.infer<typeof GenerateTasksResponseSchema>;

// Define the schema for the input
export const GenerateTasksInputSchema = z.object({
  projectId: z.string().describe('ID of the project to generate tasks for'),
  projectTitle: z.string().describe('Title of the project'),
  projectDescription: z.string().describe('Description of the project'),
  projectCategories: z.array(z.string()).describe('Categories available in the project'),
  startDate: z.string().describe('Project start date in YYYY-MM-DD format'),
  endDate: z.string().describe('Project end date in YYYY-MM-DD format'),
  additionalContext: z.string().optional().describe('Any additional context or requirements'),
});

export type GenerateTasksInput = z.infer<typeof GenerateTasksInputSchema>;

// Define the generateTasksListTool as a function
export const generateTasksListTool = {
  name: 'generateTasksList',
  description: 'Generates a chronological list of tasks for a project with all necessary details',
  inputSchema: GenerateTasksInputSchema,
  responseSchema: GenerateTasksResponseSchema,
  model: 'deepseek-r1-distill-llama-70b',
  streaming: false, // Set to false unless streaming is implemented
  execute: async (input: GenerateTasksInput): Promise<GenerateTasksResponse> => {
    // Validate input
    const validatedInput = GenerateTasksInputSchema.parse(input);

    const prompt = `
You are a project management expert tasked with creating a comprehensive list of tasks for a project.

PROJECT DETAILS:
- Project ID: ${validatedInput.projectId}
- Project Title: ${validatedInput.projectTitle}
- Project Description: ${validatedInput.projectDescription}
- Project Categories: ${validatedInput.projectCategories.join(', ')}
- Project Start Date: ${validatedInput.startDate}
- Project End Date: ${validatedInput.endDate}
${validatedInput.additionalContext ? `- Additional Context: ${validatedInput.additionalContext}` : ''}

REQUIREMENTS:
1. Generate a chronological list of tasks that would be required to complete this project successfully.
2. All tasks should be assigned to "Admin User (admin)".
3. Set realistic start dates and due dates within the project timeframe.
4. Establish logical dependencies between tasks where appropriate.
5. Provide detailed descriptions for each task.
6. Assign appropriate priorities (Low, Medium, High, Critical) based on task importance.
7. Categorize each task using one of the available project categories.
8. Include any relevant notes for task execution.

RESPONSE FORMAT:
You MUST respond with a valid JSON object containing:
1. A "reasoning" field with your explanation of the task generation approach
2. A "tasks" array containing objects with these fields for each task:
   - title: string
   - description: string
   - priority: one of "Low", "Medium", "High", or "Critical"
   - startDate: string in YYYY-MM-DD format
   - dueDate: string in YYYY-MM-DD format
   - dependencies: array of strings (can be empty)
   - category: string
   - notes: string (optional)

Example response format:
{
  "reasoning": "I've created tasks that follow a logical progression...",
  "tasks": [
    {
      "title": "Task 1 Title",
      "description": "Detailed description of task 1",
      "priority": "High",
      "startDate": "2025-04-28",
      "dueDate": "2025-05-05",
      "dependencies": [],
      "category": "Admin",
      "notes": "Additional notes for task 1"
    },
    {
      "title": "Task 2 Title",
      "description": "Detailed description of task 2",
      "priority": "Medium",
      "startDate": "2025-05-06",
      "dueDate": "2025-05-12",
      "dependencies": ["Task 1 Title"],
      "category": "Course Work",
      "notes": "Additional notes for task 2"
    }
  ]
}

CRITICAL INSTRUCTIONS:
1. Your entire response must be ONLY a valid JSON object as described above.
2. Do NOT include any text outside of the JSON structure.
3. Do NOT wrap the JSON in markdown code blocks.
4. Do NOT include any explanations, introductions, or conclusions.
5. Do NOT use markdown formatting within your response.
6. Just return the raw JSON object and nothing else.
7. Ensure your JSON is properly formatted and valid.

If you add any text before or after the JSON, or wrap it in code blocks, your response will be considered invalid.
`;

    // Call processWithGroq to process the prompt
    const groqResponse = await processWithGroq({
      prompt,
      model: 'llama-3.3-70b-versatile',
      modelOptions: {
        temperature: 0.4,
        max_tokens: 7000, // Using max_tokens instead of maxTokens for Groq API compatibility
      },
    });

    // Log the Groq response for debugging
    console.log('Groq response:', groqResponse);

    // Parse the Groq response
    const parseResponse = (response: string): GenerateTasksResponse => {
      try {
        // Check if the response is wrapped in markdown code blocks
        const jsonBlockMatch = response.match(/```(?:json)?\s*([\s\S]*?)```/);
        let jsonContent = response;

        if (jsonBlockMatch) {
          console.log("Found JSON content wrapped in code blocks, extracting...");
          jsonContent = jsonBlockMatch[1].trim();
        }

        // Attempt to parse the response as JSON
        const parsedResponse = JSON.parse(jsonContent);

        // Use Zod schema for validation
        const result = GenerateTasksResponseSchema.safeParse(parsedResponse);

        if (result.success) {
          console.log("Successfully validated response with Zod schema");
          return result.data;
        } else {
          console.error("Zod validation failed:", result.error);

          // Try to extract valid parts from the response
          const partialResponse: Partial<GenerateTasksResponse> = {
            reasoning: parsedResponse.reasoning || "No reasoning provided",
            tasks: []
          };

          // If there's a tasks array, try to validate each task individually
          if (Array.isArray(parsedResponse.tasks)) {
            console.log(`Found ${parsedResponse.tasks.length} tasks in response, validating individually`);

            for (const taskData of parsedResponse.tasks) {
              try {
                const validatedTask = TaskItemSchema.parse(taskData);
                partialResponse.tasks!.push(validatedTask);
              } catch (taskError) {
                console.error("Failed to validate task:", taskError);

                // Try to extract as much valid data as possible
                const partialTask: Partial<TaskItem> = {
                  title: taskData.title || "Untitled Task",
                  description: taskData.description || "No description provided",
                  priority: (taskData.priority as any) || "Medium",
                  startDate: taskData.startDate || validatedInput.startDate,
                  dueDate: taskData.dueDate || validatedInput.endDate,
                  category: taskData.category || validatedInput.projectCategories[0],
                };

                // Add any missing required fields
                const task: TaskItem = {
                  ...partialTask,
                  title: partialTask.title!,
                  description: partialTask.description!,
                  priority: normalizePriority(partialTask.priority as string),
                  startDate: partialTask.startDate!,
                  dueDate: partialTask.dueDate!,
                  category: partialTask.category!,
                  dependencies: taskData.dependencies || [],
                  notes: taskData.notes || ""
                };

                partialResponse.tasks!.push(task);
              }
            }
          }

          // If we managed to extract at least one task, return the partial response
          if (partialResponse.tasks!.length > 0) {
            console.log(`Successfully extracted ${partialResponse.tasks!.length} tasks from response`);
            return partialResponse as GenerateTasksResponse;
          }

          // If we couldn't extract any tasks, throw an error to fall back to text parsing
          throw new Error("Failed to extract any valid tasks from JSON response");
        }
      } catch (e) {
        // If JSON parsing fails, attempt to extract structured data from the text
        console.error('Failed to parse JSON response, attempting to extract structured data', e);

        // First, try to extract reasoning
        let reasoning = 'No explicit reasoning provided.';

        // Look for reasoning section in various formats
        const reasoningPatterns = [
          /Reasoning:(.*?)(?:Tasks:|Task 1:|#### Task 1:|### Task 1:)/i,
          /(?:Approach|Methodology|Strategy):(.*?)(?:Tasks:|Task 1:|#### Task 1:|### Task 1:)/i,
          /(?:Project|Task) (?:Analysis|Breakdown|Planning):(.*?)(?:Tasks:|Task 1:|#### Task 1:|### Task 1:)/i
        ];

        for (const pattern of reasoningPatterns) {
          const match = response.match(pattern);
          if (match) {
            reasoning = match[1].trim();
            break;
          }
        }

        // Parse tasks from text - handle multiple formats
        const taskItems: TaskItem[] = [];

        // Try different task block patterns
        const taskPatterns = [
          // Pattern 1: Task 1: or Task 1 -
          /(?:Task|#### Task|### Task) \d+(?::|-)?(.*?)(?:(?:Task|#### Task|### Task) \d+(?::|-)|\Z)/g,

          // Pattern 2: Markdown headers with task titles
          /(?:####|\*\*) (?:Task \d+:|Task \d+)(.*?)(?=(?:####|\*\*) (?:Task \d+:|Task \d+)|\Z)/g,

          // Pattern 3: Numbered list items
          /\d+\.\s+(.*?)(?=\d+\.\s+|\Z)/g,

          // Pattern 4: JSON tasks array items
          /"title":\s*"([^"]+)".*?"description":\s*"([^"]+)".*?"priority":\s*"([^"]+)".*?"startDate":\s*"([^"]+)".*?"dueDate":\s*"([^"]+)".*?"category":\s*"([^"]+)"/g
        ];

        let taskBlocks: string[] = [];

        // Try each pattern until we find task blocks
        for (const pattern of taskPatterns) {
          const matches = [...response.matchAll(pattern)];
          if (matches.length > 0) {
            taskBlocks = matches.map(match => match[1].trim()).filter(block => block.length > 0);
            break;
          }
        }

        // If no blocks found with patterns, try splitting by common task delimiters
        if (taskBlocks.length === 0) {
          // Try to extract tasks from markdown format with headers and bullet points
          const markdownTaskMatch = response.match(/\*\*(?:Title|Task):\*\*(.*?)(?=\*\*(?:Title|Task):\*\*|\Z)/g);

          if (markdownTaskMatch && markdownTaskMatch.length > 0) {
            console.log(`Found ${markdownTaskMatch.length} tasks in markdown format`);
            taskBlocks = markdownTaskMatch;
          } else {
            // Fall back to splitting by common delimiters
            taskBlocks = response.split(/(?:Task \d+:|#### Task \d+:|### Task \d+:|\n\d+\.\s+)/g)
              .slice(1) // Skip the first split which is usually before the first task
              .filter(block => block.trim().length > 0);
          }
        }

        // If we still don't have any task blocks, try to extract from the JSON-like structure
        if (taskBlocks.length === 0) {
          const jsonTasksMatch = response.match(/"title":\s*"([^"]+)".*?"description":\s*"([^"]+)"/g);

          if (jsonTasksMatch && jsonTasksMatch.length > 0) {
            console.log(`Found ${jsonTasksMatch.length} tasks in JSON-like format`);
            taskBlocks = jsonTasksMatch;
          }
        }

        console.log(`Found ${taskBlocks.length} task blocks to parse`);

        // Enhanced regex patterns to match various formats
        for (const block of taskBlocks) {
          try {
            // Check if this block looks like a JSON object fragment
            if (block.includes('"title":') && block.includes('"description":')) {
              console.log("Processing JSON-like task block");

              // Extract data using JSON-like patterns
              const titleMatch = block.match(/"title":\s*"([^"]+)"/);
              const descriptionMatch = block.match(/"description":\s*"([^"]+)"/);
              const priorityMatch = block.match(/"priority":\s*"([^"]+)"/);
              const startDateMatch = block.match(/"startDate":\s*"([^"]+)"/);
              const dueDateMatch = block.match(/"dueDate":\s*"([^"]+)"/);
              const dependenciesMatch = block.match(/"dependencies":\s*(\[[^\]]*\])/);
              const categoryMatch = block.match(/"category":\s*"([^"]+)"/);
              const notesMatch = block.match(/"notes":\s*"([^"]+)"/);

              if (titleMatch) {
                // Parse dependencies array if it exists
                let dependencies: string[] = [];
                if (dependenciesMatch) {
                  try {
                    dependencies = JSON.parse(dependenciesMatch[1]);
                  } catch (e) {
                    console.error("Failed to parse dependencies JSON:", e);
                  }
                }

                // Normalize priority to ensure it's a valid TaskPriority
                const rawPriority = priorityMatch ? priorityMatch[1] : 'Medium';
                const normalizedPriority = normalizePriority(rawPriority);

                const task: TaskItem = {
                  title: titleMatch[1],
                  description: descriptionMatch ? descriptionMatch[1] : 'No description provided',
                  priority: normalizedPriority,
                  startDate: startDateMatch ? startDateMatch[1] : validatedInput.startDate,
                  dueDate: dueDateMatch ? dueDateMatch[1] : validatedInput.endDate,
                  dependencies: dependencies,
                  category: categoryMatch ? categoryMatch[1] : validatedInput.projectCategories[0],
                  notes: notesMatch ? notesMatch[1] : '',
                };

                taskItems.push(task);
                console.log(`Successfully parsed JSON-like task: ${task.title}`);
                continue; // Skip the regular parsing for this block
              }
            }

            // More flexible patterns to match various formats
            const titlePatterns = [
              /(?:Title|Task|Name):\s*(.*?)(?:\n|$)/i,
              /\*\*(?:Title|Task|Name):\*\*\s*(.*?)(?:\n|$)/i,
              /^(.*?)(?:\n|$)/i, // First line as title if nothing else matches
            ];

            const descriptionPatterns = [
              /(?:Description|Details|Overview):\s*(.*?)(?:\n(?:[A-Za-z]+:|Priority:|Due Date:|Start Date:))/i,
              /\*\*(?:Description|Details|Overview):\*\*\s*(.*?)(?:\n(?:[A-Za-z]+:|Priority:|Due Date:|Start Date:))/i,
            ];

            const priorityPatterns = [
              /(?:Priority):\s*(Low|Medium|High|Critical)/i,
              /\*\*(?:Priority):\*\*\s*(Low|Medium|High|Critical)/i,
            ];

            const startDatePatterns = [
              /(?:Start Date|Start):\s*(\d{4}-\d{2}-\d{2}|\d{1,2}\/\d{1,2}\/\d{4}|\d{1,2}-\d{1,2}-\d{4})/i,
              /\*\*(?:Start Date|Start):\*\*\s*(\d{4}-\d{2}-\d{2}|\d{1,2}\/\d{1,2}\/\d{4}|\d{1,2}-\d{1,2}-\d{4})/i,
            ];

            const dueDatePatterns = [
              /(?:Due Date|End Date|Deadline|Due):\s*(\d{4}-\d{2}-\d{2}|\d{1,2}\/\d{1,2}\/\d{4}|\d{1,2}-\d{1,2}-\d{4})/i,
              /\*\*(?:Due Date|End Date|Deadline|Due):\*\*\s*(\d{4}-\d{2}-\d{2}|\d{1,2}\/\d{1,2}\/\d{4}|\d{1,2}-\d{1,2}-\d{4})/i,
            ];

            const dependenciesPatterns = [
              /(?:Dependencies|Depends On|Prerequisite):\s*(.*?)(?:\n|$)/i,
              /\*\*(?:Dependencies|Depends On|Prerequisite):\*\*\s*(.*?)(?:\n|$)/i,
            ];

            const categoryPatterns = [
              /(?:Category|Type|Group):\s*(.*?)(?:\n|$)/i,
              /\*\*(?:Category|Type|Group):\*\*\s*(.*?)(?:\n|$)/i,
            ];

            const notesPatterns = [
              /(?:Notes|Additional Info|Comments):\s*(.*?)(?:\n|$)/i,
              /\*\*(?:Notes|Additional Info|Comments):\*\*\s*(.*?)(?:\n|$)/i,
            ];

            // Try each pattern until we find a match
            let title = '';
            let description = 'No description provided';
            let priority = 'Medium';
            let startDate = validatedInput.startDate;
            let dueDate = validatedInput.endDate;
            let dependencies: string[] = [];
            let category = validatedInput.projectCategories[0];
            let notes = '';

            // Find title
            for (const pattern of titlePatterns) {
              const match = block.match(pattern);
              if (match) {
                title = match[1].trim();
                break;
              }
            }

            // Skip if no title found
            if (!title) {
              console.warn('No title found in block, skipping');
              continue;
            }

            // Find description
            for (const pattern of descriptionPatterns) {
              const match = block.match(pattern);
              if (match) {
                description = match[1].trim();
                break;
              }
            }

            // Find priority
            for (const pattern of priorityPatterns) {
              const match = block.match(pattern);
              if (match) {
                priority = match[1].trim() as 'Low' | 'Medium' | 'High' | 'Critical';
                break;
              }
            }

            // Find start date
            for (const pattern of startDatePatterns) {
              const match = block.match(pattern);
              if (match) {
                // Convert to YYYY-MM-DD format if needed
                const dateStr = match[1].trim();
                startDate = formatDateString(dateStr, validatedInput.startDate);
                break;
              }
            }

            // Find due date
            for (const pattern of dueDatePatterns) {
              const match = block.match(pattern);
              if (match) {
                // Convert to YYYY-MM-DD format if needed
                const dateStr = match[1].trim();
                dueDate = formatDateString(dateStr, validatedInput.endDate);
                break;
              }
            }

            // Find dependencies
            for (const pattern of dependenciesPatterns) {
              const match = block.match(pattern);
              if (match) {
                const depsStr = match[1].trim();
                if (depsStr.toLowerCase() !== 'none' && depsStr.toLowerCase() !== 'n/a') {
                  dependencies = depsStr.split(/,|;/).map(d => d.trim());
                }
                break;
              }
            }

            // Find category
            for (const pattern of categoryPatterns) {
              const match = block.match(pattern);
              if (match) {
                category = match[1].trim();
                break;
              }
            }

            // Find notes
            for (const pattern of notesPatterns) {
              const match = block.match(pattern);
              if (match) {
                notes = match[1].trim();
                break;
              }
            }

            // Create task item
            const task: TaskItem = {
              title,
              description,
              priority: normalizePriority(priority),
              startDate,
              dueDate,
              dependencies,
              category,
              notes,
            };

            taskItems.push(task);
            console.log(`Successfully parsed task: ${title}`);
          } catch (taskError) {
            console.error('Error parsing individual task', taskError);
          }
        }

        // Helper function to format date strings to YYYY-MM-DD
        function formatDateString(dateStr: string, defaultDate: string): string {
          try {
            // Check if already in YYYY-MM-DD format
            if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
              return dateStr;
            }

            // Handle DD/MM/YYYY format
            if (/^\d{1,2}\/\d{1,2}\/\d{4}$/.test(dateStr)) {
              const parts = dateStr.split('/');
              return `${parts[2]}-${parts[1].padStart(2, '0')}-${parts[0].padStart(2, '0')}`;
            }

            // Handle DD-MM-YYYY format
            if (/^\d{1,2}-\d{1,2}-\d{4}$/.test(dateStr)) {
              const parts = dateStr.split('-');
              return `${parts[2]}-${parts[1].padStart(2, '0')}-${parts[0].padStart(2, '0')}`;
            }

            // Try to parse with Date
            const date = new Date(dateStr);
            if (!isNaN(date.getTime())) {
              return date.toISOString().split('T')[0];
            }

            // Return default if all else fails
            return defaultDate;
          } catch (e) {
            console.error('Error formatting date string', e);
            return defaultDate;
          }
        }

        return {
          reasoning,
          tasks: taskItems.length > 0 ? taskItems : [
            {
              title: 'Default Task',
              description: 'This is a default task created because parsing failed',
              priority: 'Medium' as TaskPriority,
              startDate: validatedInput.startDate,
              dueDate: validatedInput.endDate,
              category: validatedInput.projectCategories[0],
              dependencies: [],
              notes: ''
            },
          ],
        };
      }
    };

    // Parse and return the response
    return parseResponse(groqResponse);
  },
};