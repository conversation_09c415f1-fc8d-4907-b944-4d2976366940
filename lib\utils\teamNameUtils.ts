/**
 * Centralized utility functions for team name mappings
 * Ensures consistency across the application for AgenticTeamId handling
 */

import { AgenticTeamId } from '../agents/pmo/PMOInterfaces';

/**
 * Get the canonical team name (enum key) from AgenticTeamId
 * This should be used for internal consistency and data storage
 */
export function getCanonicalTeamName(teamId: AgenticTeamId): string {
  switch (teamId) {
    case AgenticTeamId.Marketing:
      return 'Marketing';
    case AgenticTeamId.Research:
      return 'Research';
    case AgenticTeamId.SoftwareDesign:
      return 'SoftwareDesign';
    case AgenticTeamId.Sales:
      return 'Sales';
    case AgenticTeamId.BusinessAnalysis:
      return 'BusinessAnalysis';
    case AgenticTeamId.InvestigativeResearch:
      return 'InvestigativeResearch';
    case AgenticTeamId.CodebaseDocumentation:
      return 'CodebaseDocumentation';
    case AgenticTeamId.DocumentationGeneration:
      return 'DocumentationGeneration';
    default:
      return 'Unknown Team';
  }
}

/**
 * Get the user-friendly display name from AgenticTeamId
 * This should be used for UI display purposes
 */
export function getDisplayTeamName(teamId: AgenticTeamId): string {
  switch (teamId) {
    case AgenticTeamId.Marketing:
      return 'Marketing';
    case AgenticTeamId.Research:
      return 'Research';
    case AgenticTeamId.SoftwareDesign:
      return 'Software Design';
    case AgenticTeamId.Sales:
      return 'Sales';
    case AgenticTeamId.BusinessAnalysis:
      return 'Business Analysis';
    case AgenticTeamId.InvestigativeResearch:
      return 'Investigative Research';
    case AgenticTeamId.CodebaseDocumentation:
      return 'Codebase Documentation';
    case AgenticTeamId.DocumentationGeneration:
      return 'Documentation Generation';
    default:
      return 'Unknown Team';
  }
}

/**
 * Get the API team name from AgenticTeamId
 * This matches the format used in TEAM_DESCRIPTIONS in the PMO assessment API
 */
export function getApiTeamName(teamId: AgenticTeamId): string {
  switch (teamId) {
    case AgenticTeamId.Marketing:
      return 'Marketing Team';
    case AgenticTeamId.Research:
      return 'Research Team';
    case AgenticTeamId.SoftwareDesign:
      return 'Software Design Team';
    case AgenticTeamId.Sales:
      return 'Sales Team';
    case AgenticTeamId.BusinessAnalysis:
      return 'Business Analysis Team';
    case AgenticTeamId.InvestigativeResearch:
      return 'Investigative Research Team';
    case AgenticTeamId.CodebaseDocumentation:
      return 'Codebase Documentation Team';
    case AgenticTeamId.DocumentationGeneration:
      return 'Documentation Generation Team';
    default:
      return 'Unknown Team';
  }
}

/**
 * Convert a team name string to AgenticTeamId
 * Supports multiple name formats for backward compatibility
 */
export function getTeamIdFromName(name: string): AgenticTeamId | null {
  const normalizedName = name.trim().toLowerCase();

  // Comprehensive mapping supporting enum keys, display names, and API names
  const teamMappings: { [key: string]: AgenticTeamId } = {
    // Enum key names (primary)
    'marketing': AgenticTeamId.Marketing,
    'research': AgenticTeamId.Research,
    'softwaredesign': AgenticTeamId.SoftwareDesign,
    'sales': AgenticTeamId.Sales,
    'businessanalysis': AgenticTeamId.BusinessAnalysis,
    'investigativeresearch': AgenticTeamId.InvestigativeResearch,
    'codebasedocumentation': AgenticTeamId.CodebaseDocumentation,
    'documentationgeneration': AgenticTeamId.DocumentationGeneration,

    // Legacy display names (backward compatibility)
    'software design': AgenticTeamId.SoftwareDesign,
    'business analysis': AgenticTeamId.BusinessAnalysis,
    'investigative research': AgenticTeamId.InvestigativeResearch,
    'codebase documentation': AgenticTeamId.CodebaseDocumentation,
    'documentation generation': AgenticTeamId.DocumentationGeneration,

    // API team names (backward compatibility)
    'marketing team': AgenticTeamId.Marketing,
    'research team': AgenticTeamId.Research,
    'software design team': AgenticTeamId.SoftwareDesign,
    'sales team': AgenticTeamId.Sales,
    'business analysis team': AgenticTeamId.BusinessAnalysis,
    'investigative research team': AgenticTeamId.InvestigativeResearch,
    'codebase documentation team': AgenticTeamId.CodebaseDocumentation,
    'documentation generation team': AgenticTeamId.DocumentationGeneration,
  };

  return teamMappings[normalizedName] || null;
}

/**
 * Get all available team IDs
 */
export function getAllTeamIds(): AgenticTeamId[] {
  return Object.values(AgenticTeamId);
}

/**
 * Convert agentType to AgenticTeamId
 * Maps agent types from transcript metadata to the corresponding team ID
 */
export function getTeamIdFromAgentType(agentType: string): AgenticTeamId | null {
  if (!agentType) return null;

  const normalizedAgentType = agentType.trim().toLowerCase();

  // Map common agent types to team IDs
  const agentTypeMapping: { [key: string]: AgenticTeamId } = {
    // Marketing team variations
    'marketing': AgenticTeamId.Marketing,
    'marketing director': AgenticTeamId.Marketing,
    'strategic-director': AgenticTeamId.Marketing,
    'content-creator': AgenticTeamId.Marketing,
    'social-media-orchestrator': AgenticTeamId.Marketing,
    'analytics-reporting': AgenticTeamId.Marketing,
    'research-insights': AgenticTeamId.Marketing,

    // Research team variations
    'research': AgenticTeamId.Research,
    'research team': AgenticTeamId.Research,
    'research-team': AgenticTeamId.Research,
    'research-strategic-director': AgenticTeamId.Research,
    'investigative research': AgenticTeamId.InvestigativeResearch,
    'investigativeresearch': AgenticTeamId.InvestigativeResearch,

    // Sales team variations
    'sales': AgenticTeamId.Sales,
    'sales team': AgenticTeamId.Sales,
    'sales director': AgenticTeamId.Sales,

    // Software Design team variations
    'software': AgenticTeamId.SoftwareDesign,
    'software design': AgenticTeamId.SoftwareDesign,
    'softwaredesign': AgenticTeamId.SoftwareDesign,
    'software design team': AgenticTeamId.SoftwareDesign,
    'developer': AgenticTeamId.SoftwareDesign,
    'engineer': AgenticTeamId.SoftwareDesign,

    // Business Analysis team variations
    'business': AgenticTeamId.BusinessAnalysis,
    'business analysis': AgenticTeamId.BusinessAnalysis,
    'businessanalysis': AgenticTeamId.BusinessAnalysis,
    'business analysis team': AgenticTeamId.BusinessAnalysis,
    'business analyst': AgenticTeamId.BusinessAnalysis,

    // Documentation team variations
    'documentation': AgenticTeamId.DocumentationGeneration,
    'codebase documentation': AgenticTeamId.CodebaseDocumentation,
    'documentation generation': AgenticTeamId.DocumentationGeneration,
  };

  return agentTypeMapping[normalizedAgentType] || null;
}

/**
 * Get all team mappings for debugging/reference
 */
export function getAllTeamMappings(): Array<{
  id: AgenticTeamId;
  canonical: string;
  display: string;
  api: string;
}> {
  return getAllTeamIds().map(id => ({
    id,
    canonical: getCanonicalTeamName(id),
    display: getDisplayTeamName(id),
    api: getApiTeamName(id)
  }));
}
