import React from 'react';

interface DeleteFolderModalProps {
  category: string;
  totalFiles: number;
  onConfirm: () => void;
  onCancel: () => void;
}

const DeleteFolderModal: React.FC<DeleteFolderModalProps> = ({ category, totalFiles, onConfirm, onCancel }) => {
  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
      <div className="bg-white text-gray-700 p-6 rounded-lg shadow-lg max-w-md w-full mx-4">
        <h2 className="text-xl font-bold mb-4 text-red-600">Delete Folder</h2>
        <p className="mb-2">Are you sure you want to delete the following folder and all associated files and chats?</p>
        <div className="space-y-2 mb-6 bg-gray-50 p-4 rounded">
          <p><strong className="text-gray-600">Folder:</strong> <span className="text-gray-800">{category}</span></p>
          <p><strong className="text-gray-600">Files to delete:</strong> <span className="text-gray-800">{totalFiles}</span></p>
          <p className="text-red-600 text-sm">Warning: This action is permanent.</p>
        </div>
        <div className="flex justify-end space-x-3">
          <button onClick={onCancel} className="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors duration-200">Cancel</button>
          <button onClick={onConfirm} className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors duration-200">Delete</button>
        </div>
      </div>
    </div>
  );
};

export default DeleteFolderModal;

