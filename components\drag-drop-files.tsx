'use client'

import React, { useState, useEffect } from 'react'
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd'
import { FolderOpenIcon, ChevronDownIcon, ChevronRightIcon } from '@heroicons/react/24/solid'
import { collection, getDocs, updateDoc, doc } from 'firebase/firestore'
import { db } from 'components/firebase'
import { useSession } from 'next-auth/react'
import { ChatBubbleLeftIcon } from '@heroicons/react/24/outline'
import CreateFoldersModal from './CreateFoldersModal';
import { FolderPlus } from 'lucide-react'

interface FileItem {
  id: string
  name: string
  category: string
}

interface Category {
  id: string
  name: string
  isExpanded: boolean
  files: FileItem[]
}

export default function DragDropFiles({ expanded, toggleMenu }: { 
  expanded: boolean
  toggleMenu: (menuName: string) => void 
}) {
  const { data: session } = useSession()
  const [categories, setCategories] = useState<Category[]>([])

  useEffect(() => {
    if (session?.user?.email) {
      fetchFiles()
    }
  }, [session?.user?.email])

  const fetchFiles = async () => {
    if (!session?.user?.email) return

    try {
      const filesCollection = collection(db, 'users', session.user.email, 'files')
      const filesSnapshot = await getDocs(filesCollection)
      const fetchedFiles = filesSnapshot.docs.map(doc => ({
        id: doc.id,
        name: doc.data().name || 'Untitled',
        category: doc.data().category || 'Unknown'
      })).sort((a, b) => a.name.localeCompare(b.name))

      // Group files by category
      const categoryMap = fetchedFiles.reduce((acc, file) => {
        if (!acc[file.category]) {
          acc[file.category] = []
        }
        acc[file.category].push(file)
        return acc
      }, {} as Record<string, FileItem[]>)

      // Convert to array and sort categories, ensuring Unknown is first
      const sortedCategories = Object.entries(categoryMap)
        .map(([name, files]) => ({
          id: name,
          name,
          isExpanded: false,
          files: files.sort((a, b) => a.name.localeCompare(b.name))
        }))
        .sort((a, b) => {
          if (a.id === 'Unknown') return -1
          if (b.id === 'Unknown') return 1
          return a.name.localeCompare(b.name)
        })

      setCategories(sortedCategories)
    } catch (error) {
      console.error('Error fetching files:', error)
    }
  }

  const toggleCategory = (categoryId: string) => {
    setCategories(prev => 
      prev.map(cat => 
        cat.id === categoryId 
          ? { ...cat, isExpanded: !cat.isExpanded }
          : cat
      )
    )
  }

  const onDragEnd = async (result: any) => {
    if (!result.destination || !session?.user?.email) return

    const { source, destination, draggableId } = result
    const sourceCategory = categories.find(c => c.id === source.droppableId)
    const file = sourceCategory?.files.find(f => f.id === draggableId)
    
    if (!file) return

    try {
      const newCategory = destination.droppableId

      // Update in Firestore
      const fileRef = doc(db, 'users', session.user.email, 'files', draggableId)
      await updateDoc(fileRef, { category: newCategory })

      // Moving between categories
      setCategories(prev => {
        const updatedCategories = prev.map(cat => {
          if (cat.id === source.droppableId) {
            return { ...cat, files: cat.files.filter(f => f.id !== draggableId) }
          }
          if (cat.id === destination.droppableId) {
            return { 
              ...cat, 
              files: [...cat.files, { ...file, category: newCategory }].sort((a, b) => a.name.localeCompare(b.name)),
              isExpanded: true
            }
          }
          return cat
        })
        // Ensure Unknown category stays first when filtering
        return updatedCategories
          .filter(cat => cat.files.length > 0)
          .sort((a, b) => {
            if (a.id === 'Unknown') return -1
            if (b.id === 'Unknown') return 1
            return a.name.localeCompare(b.name)
          })
      })
    } catch (error) {
      console.error('Error updating file:', error)
    }
  }

  const getCategoryDisplayName = (category: Category) => {
    return category.id === 'Unknown' ? 'Single Files' : category.name
  }

  return (
    <div className="rounded-lg bg-opacity-65 pt-2 -pb-2">
      <div
        onClick={() => toggleMenu('allFiles')}
        className="flex items-center justify-between cursor-pointer text-blue-700 hover:text-blue-200"
      >
        <p className="mb-3">Tools</p>
        <span className="mb-3">{expanded ? '▼' : '▶'}</span>
      </div>

      {expanded && (
        <DragDropContext onDragEnd={onDragEnd}>
          <div className="-mt-1">
            <div className="text-white text-xs mb-2">Move files between folders</div>
            {categories.map((category) => (
              <div key={category.id} className="mb-2">
                <div
                  onClick={() => toggleCategory(category.id)}
                  className="flex items-center cursor-pointer text-ike-purple_b hover:text-blue-200"
                >
                  {category.isExpanded ? (
                    <ChevronDownIcon className="h-4 w-4" />
                  ) : (
                    <ChevronRightIcon className="h-4 w-4" />
                  )}
                  <FolderOpenIcon className="h-4 w-4 ml-1" />
                  <span className="ml-1">{getCategoryDisplayName(category)}</span>
                </div>

                {category.isExpanded && (
                  <Droppable droppableId={category.id} isDropDisabled={false}>
                    {(provided) => (
                      <div
                        ref={provided.innerRef}
                        {...provided.droppableProps}
                        className="ml-6"
                      >
                        {category.files.map((file, index) => (
                          <Draggable key={file.id} draggableId={file.id} index={index} isDragDisabled={false}>
                            {(provided) => (
                              <div
                                ref={provided.innerRef}
                                {...provided.draggableProps}
                                {...provided.dragHandleProps}
                                className="flex flex-row px-2 text-xs text-gray-600 hover:text-white hover:rounded-lg mt-1 mb-1 -ml-3"
                              >
                                <ChatBubbleLeftIcon className="h-4 w-4 text-green-600 -ml-2 mr-1" />
                                {file.name.length > 25 ? `${file.name.substring(0, 25)}...` : file.name}
                              </div>
                            )}
                          </Draggable>
                        ))}
                        {provided.placeholder}
                      </div>
                    )}
                  </Droppable>
                )}
              </div>
            ))}
          </div>
          <div className="-ml-1 text-xs border-t-2 border-gray-400">
     
      
            <CreateFoldersModal onUpdate={function (): void {
              throw new Error('Function not implemented.')
            } } />  
        </div>                  
         
        </DragDropContext>
        
      )}
 
    </div>
    
  )
}