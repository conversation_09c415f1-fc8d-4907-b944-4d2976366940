import type { QueryCodebaseRequest, StrategyRun } from '../types';
import { executeTwoStage } from './twoStage';

export const HybridInvertedStrategyId = 'hybrid-inverted' as const;

export async function executeHybridInverted(req: QueryCodebaseRequest): Promise<StrategyRun> {
  const run = await executeTwoStage(req); // inherits LLM analysis from two-stage
  return { ...run, strategyId: HybridInvertedStrategyId };
}

