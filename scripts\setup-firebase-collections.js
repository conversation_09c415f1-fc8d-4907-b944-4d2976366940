/**
 * <PERSON><PERSON><PERSON> to set up Firebase collections for transcript and category state
 * This creates the necessary collections and documents with proper structure
 */

// Import Firebase Admin SDK
const admin = require('firebase-admin');

// Initialize Firebase Admin (assumes service account is already configured)
if (!admin.apps.length) {
  try {
    // Try to use existing initialization from your project
    const serviceAccount = require('../service_key.json');
    admin.initializeApp({
      credential: admin.credential.cert(serviceAccount),
      storageBucket: process.env.FIREBASE_STORAGE_BUCKET || 'your-project-id.appspot.com'
    });
  } catch (error) {
    console.error('❌ Failed to initialize Firebase Admin:', error.message);
    console.log('Please ensure service_key.json exists and FIREBASE_STORAGE_BUCKET is set');
    process.exit(1);
  }
}

const db = admin.firestore();

async function setupFirebaseCollections() {
  console.log('🔥 Setting up Firebase Collections for Transcript and Category State...\n');

  try {
    // 1. Create transcript_sessions collection with sample document
    console.log('📝 Setting up transcript_sessions collection...');
    
    const transcriptSample = {
      userId: 'setup-test-user',
      agentId: 'setup-test-agent',
      conversationId: 'setup-test-conversation',
      dialogue: [
        {
          role: 'user',
          content: 'This is a test transcript entry to initialize the collection.',
          timestamp: new Date()
        },
        {
          role: 'assistant', 
          content: 'This response confirms the transcript collection is working.',
          timestamp: new Date()
        }
      ],
      agentName: 'Test Agent',
      agentType: 'Setup',
      timestamp: Date.now(),
      createdAt: admin.firestore.Timestamp.now()
    };

    // Use the new hierarchical structure: users/{userId}/agent/{conversationId}/transcript_sessions
    const hierarchicalPath = 'users/setup-test-user/agent/setup-test-conversation/transcript_sessions';
    await db.collection(hierarchicalPath).doc('setup-test').set(transcriptSample);
    console.log('✅ transcript_sessions collection created with sample document in hierarchical structure');

    // 2. Create document_category_sessions collection with sample document
    console.log('\n📁 Setting up document_category_sessions collection...');
    
    const categorySample = {
      userId: 'setup-test-user',
      selectedDocuments: [
        {
          id: 'setup-doc-1',
          title: 'Test Document',
          category: 'PMO - Setup Test'
        }
      ],
      primaryCategory: 'PMO - Setup Test',
      conversationId: 'setup-test-conversation',
      agentType: 'Setup',
      timestamp: Date.now(),
      createdAt: admin.firestore.Timestamp.now(),
      storedAt: new Date().toISOString()
    };

    await db.collection('document_category_sessions').doc('setup-test').set(categorySample);
    console.log('✅ document_category_sessions collection created with sample document');

    // 3. Test read operations
    console.log('\n🔍 Testing read operations...');
    
    const transcriptDoc = await db.collection('users/setup-test-user/agent/setup-test-conversation/transcript_sessions').doc('setup-test').get();
    if (transcriptDoc.exists) {
      console.log('✅ transcript_sessions read test successful (hierarchical structure)');
      console.log('   Sample dialogue length:', transcriptDoc.data().dialogue.length);
    } else {
      console.log('❌ transcript_sessions read test failed');
    }

    const categoryDoc = await db.collection('document_category_sessions').doc('setup-test').get();
    if (categoryDoc.exists) {
      console.log('✅ document_category_sessions read test successful');
      console.log('   Sample primary category:', categoryDoc.data().primaryCategory);
    } else {
      console.log('❌ document_category_sessions read test failed');
    }

    // 4. Test your actual user
    console.log('\n👤 Setting up collections for your user (<EMAIL>)...');
    
    const userTranscriptSample = {
      userId: '<EMAIL>',
      agentId: 'user-setup-agent',
      conversationId: 'user-setup-conversation',
      dialogue: [
        {
          role: 'user',
          content: 'Hello, this is a setup test for my transcript collection.',
          timestamp: new Date()
        },
        {
          role: 'assistant',
          content: 'Great! Your transcript collection is now properly initialized.',
          timestamp: new Date()
        }
      ],
      agentName: 'Setup Assistant',
      agentType: 'Marketing',
      timestamp: Date.now(),
      createdAt: admin.firestore.Timestamp.now()
    };

    // Store under multiple keys as per our API design using hierarchical structure
    const userKeys = ['<EMAIL>', 'user-setup-agent', 'user-setup-conversation'];
    const batch = db.batch();
    const userHierarchicalPath = 'users/<EMAIL>/agent/user-setup-conversation/transcript_sessions';

    for (const key of userKeys) {
      const docRef = db.collection(userHierarchicalPath).doc(key);
      batch.set(docRef, userTranscriptSample);
    }
    await batch.commit();
    console.log('✅ User transcript collection initialized with multiple keys in hierarchical structure:', userKeys);

    const userCategorySample = {
      userId: '<EMAIL>',
      selectedDocuments: [
        {
          id: 'user-doc-1',
          title: 'User Test Document',
          category: 'PMO - Marketing Strategy'
        }
      ],
      primaryCategory: 'PMO - Marketing Strategy',
      conversationId: 'user-setup-conversation',
      agentType: 'Marketing',
      timestamp: Date.now(),
      createdAt: admin.firestore.Timestamp.now(),
      storedAt: new Date().toISOString()
    };

    const categoryBatch = db.batch();
    for (const key of userKeys) {
      const docRef = db.collection('document_category_sessions').doc(key);
      categoryBatch.set(docRef, userCategorySample);
    }
    await categoryBatch.commit();
    console.log('✅ User category collection initialized with multiple keys:', userKeys);

    // 5. Clean up test documents (optional)
    console.log('\n🗑️ Cleaning up setup test documents...');
    await db.collection('transcript_sessions').doc('setup-test').delete();
    await db.collection('document_category_sessions').doc('setup-test').delete();
    console.log('✅ Setup test documents cleaned up');

    console.log('\n🎉 Firebase Collections Setup Complete!');
    console.log('\n📋 Summary:');
    console.log('✅ transcript_sessions collection created and tested');
    console.log('✅ document_category_sessions collection created and tested');
    console.log('✅ Sample data <NAME_EMAIL>');
    console.log('✅ Multiple storage keys configured for reliability');
    
    console.log('\n🔧 Next Steps:');
    console.log('1. Configure TTL policies in Firebase Console (see docs/firebase-ttl-setup.md)');
    console.log('2. Test your APIs with real data');
    console.log('3. Deploy your updated code');
    console.log('4. Run end-to-end voice conversation test');

  } catch (error) {
    console.error('❌ Error setting up Firebase collections:', error);
    process.exit(1);
  }
}

// Run the setup
setupFirebaseCollections();
