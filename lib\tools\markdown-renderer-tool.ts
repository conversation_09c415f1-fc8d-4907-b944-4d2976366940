/**
 * Markdown Renderer Tool
 *
 * This tool provides markdown processing functionality extracted from the MarkdownRenderer component.
 * It ensures consistent markdown rendering across the application, including PDFs.
 */

/**
 * Efficiently preprocesses markdown content with minimal transformations
 * Preserves indentation and structure while ensuring proper rendering
 *
 * @param {string} markdown - Raw markdown content
 * @return {string} Minimally processed markdown
 */
function preprocessMarkdown(markdown: string): string {
  // Ensure markdown is a string
  if (!markdown || typeof markdown !== 'string') return "";

  // Identify code blocks to protect them from processing
  const codeBlockRegex = /```[\s\S]*?```/g;
  const codeBlocks: string[] = [];

  // Temporarily replace code blocks with placeholders
  let processedMarkdown = markdown.replace(codeBlockRegex, match => {
    const placeholder = `CODE_BLOCK_${codeBlocks.length}`;
    codeBlocks.push(match);
    return placeholder;
  });

  // Single-pass essential formatting corrections
  processedMarkdown = processedMarkdown
    // Normalize line endings to ensure consistent processing
    .replace(/\r\n/g, '\n')
    // Replace 3+ consecutive newlines with exactly 2
    .replace(/\n{3,}/g, '\n\n')
    // Ensure headers are followed by blank lines
    .replace(/^(#{1,6}[^\n]*)\n(?!\n)/gm, '$1\n\n')
    // Ensure list items in the same list stay grouped together
    .replace(/^(\s*[-*+]\s[^\n]*)\n(?!\n|\s*[-*+])/gm, '$1\n\n');

  // Restore code blocks
  processedMarkdown = processedMarkdown.replace(/CODE_BLOCK_(\d+)/g, (_, index) =>
    codeBlocks[parseInt(index)]
  );

  return processedMarkdown.trim();
}

/**
 * Converts markdown to a format suitable for PDF rendering
 * Handles headers, lists, bold text, etc.
 *
 * @param {string} markdown - Raw markdown content
 * @return {string} Processed markdown ready for PDF rendering
 */
function markdownToPdfFormat(markdown: string): string {
  // First preprocess the markdown for consistent structure
  const processedMarkdown = preprocessMarkdown(markdown);

  // Enhanced processing for PDF rendering
  let pdfReadyMarkdown = processedMarkdown;

  // Identify code blocks to protect them from processing
  const codeBlockRegex = /```[\s\S]*?```/g;
  const codeBlocks: string[] = [];

  // Temporarily replace code blocks with placeholders
  pdfReadyMarkdown = pdfReadyMarkdown.replace(codeBlockRegex, match => {
    const placeholder = `CODE_BLOCK_${codeBlocks.length}`;
    codeBlocks.push(match);
    return placeholder;
  });

  // Convert inline code (backticks) to <code> tags (after removing fenced blocks)
  pdfReadyMarkdown = pdfReadyMarkdown.replace(/`([^`]+)`/g, '<code>$1</code>');

  // Convert markdown tables to simple HTML table structure
  // Detect blocks with a header row, separator row, and one or more data rows
  const lines = pdfReadyMarkdown.split('\n');
  let i = 0;
  const out: string[] = [];
  while (i < lines.length) {
    const header = lines[i];
    const separator = lines[i + 1];
    const isTableHeader = header && header.includes('|');
    const isSeparator = separator && /^\s*\|?\s*:?-{3,}:?(\s*\|\s*:?-{3,}:?)*\s*\|?\s*$/.test(separator);
    if (isTableHeader && isSeparator) {
      // Collect table rows
      const rows: string[] = [];
      rows.push(header);
      rows.push(separator);
      let j = i + 2;
      while (j < lines.length && lines[j].includes('|')) {
        rows.push(lines[j]);
        j++;
      }
      // Build HTML table
      const cells = (row: string) => row
        .trim()
        .replace(/^\|/, '')
        .replace(/\|$/, '')
        .split('|')
        .map(c => c.trim());
      const headers = cells(rows[0]);
      const dataRows = rows.slice(2).map(r => cells(r));
      const tableHtml = [
        '<table>',
        '<thead><tr>' + headers.map(h => `<th>${h}</th>`).join('') + '</tr></thead>',
        '<tbody>' + dataRows.map(r => '<tr>' + r.map(c => `<td>${c}</td>`).join('') + '</tr>').join('') + '</tbody>',
        '</table>'
      ].join('');
      out.push(tableHtml);
      i = j;
      continue;
    }
    out.push(lines[i]);
    i++;
  }
  pdfReadyMarkdown = out.join('\n');

  // Process headings to ensure proper formatting
  pdfReadyMarkdown = pdfReadyMarkdown.replace(/^(#{1,6})\s+(.+)$/gm, (match, hashes, title) => {
    const level = hashes.length;
    const fontSize = 24 - ((level - 1) * 2); // h1=24, h2=22, h3=20, etc.
    return `<h${level} style="font-size: ${fontSize}px; font-weight: bold; margin-top: 16px; margin-bottom: 8px;">${title}</h${level}>`;
  });

  // Process lists for better formatting
  pdfReadyMarkdown = pdfReadyMarkdown.replace(/^(\s*)[-*+]\s+(.+)$/gm, (match, indent, content) => {
    const indentSize = indent.length;
    return `${indent}• ${content}`;
  });

  // Process numbered lists
  pdfReadyMarkdown = pdfReadyMarkdown.replace(/^(\s*)(\d+)\.\s+(.+)$/gm, (match, indent, num, content) => {
    return `${indent}${num}. ${content}`;
  });

  // Process bold text
  pdfReadyMarkdown = pdfReadyMarkdown.replace(/(\*\*|__)(.*?)\1/g, '<strong>$2</strong>');

  // Process italic text
  pdfReadyMarkdown = pdfReadyMarkdown.replace(/(\*|_)(.*?)\1/g, '<em>$2</em>');

  // Process links and expand with URL in parentheses for PDF readability
  pdfReadyMarkdown = pdfReadyMarkdown.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a> ($2)');

  // Process blockquotes
  pdfReadyMarkdown = pdfReadyMarkdown.replace(/^>\s+(.+)$/gm, '<blockquote style="border-left: 4px solid #ccc; padding-left: 8px; margin-left: 0;">$1</blockquote>');

  // Restore code blocks with formatting
  pdfReadyMarkdown = pdfReadyMarkdown.replace(/CODE_BLOCK_(\d+)/g, (_, index) => {
    const codeBlock = codeBlocks[parseInt(index)];
    // Strip the backticks and language identifier, preserving the actual code content
    const code = codeBlock.replace(/```(?:\w+)?\n?([\s\S]*?)```/g, '$1').trim();
    // Escape HTML entities in code to prevent rendering issues
    const escapedCode = code
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');
    return `<pre class="code-block" style="background-color: #f5f5f5; padding: 8px; border-radius: 4px; font-family: monospace; font-size: 9px; line-height: 1.2; white-space: pre-wrap; border: 1px solid #ddd;">${escapedCode}</pre>`;
  });

  return pdfReadyMarkdown;
}

/**
 * Extracts plain text from markdown for search indexing
 * Removes markdown syntax while preserving content
 *
 * @param {string} markdown - Raw markdown content
 * @return {string} Plain text without markdown syntax
 */
function markdownToPlainText(markdown: string): string {
  // First preprocess the markdown
  let processedMarkdown = preprocessMarkdown(markdown);

  // Remove markdown syntax
  processedMarkdown = processedMarkdown
    // Remove headers
    .replace(/^#{1,6}\s+(.+)$/gm, '$1')
    // Remove bold/italic
    .replace(/(\*\*|__)(.*?)\1/g, '$2')
    .replace(/(\*|_)(.*?)\1/g, '$2')
    // Remove code blocks
    .replace(/```[\s\S]*?```/g, '')
    .replace(/`([^`]+)`/g, '$1')
    // Remove links but keep text
    .replace(/\[([^\]]+)\]\([^\)]+\)/g, '$1')
    // Remove images
    .replace(/!\[([^\]]+)\]\([^\)]+\)/g, '$1')
    // Remove blockquotes
    .replace(/^>\s+(.+)$/gm, '$1')
    // Remove list markers
    .replace(/^(\s*)[-*+]\s+/gm, '$1')
    .replace(/^(\s*)\d+\.\s+/gm, '$1');

  return processedMarkdown.trim();
}

/**
 * Identifies and extracts sections from markdown content
 * Useful for creating a table of contents or section navigation
 *
 * @param {string} markdown - Raw markdown content
 * @return {Array<{level: number, title: string, content: string}>} Array of sections
 */
function extractMarkdownSections(markdown: string): Array<{level: number, title: string, content: string}> {
  const processedMarkdown = preprocessMarkdown(markdown);
  const lines = processedMarkdown.split('\n');
  const sections: Array<{level: number, title: string, content: string}> = [];

  let currentSection: {level: number, title: string, content: string} | null = null;
  let contentBuffer: string[] = [];

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const headerMatch = line.match(/^(#{1,6})\s+(.+)$/);

    if (headerMatch) {
      // If we have a current section, save it before starting a new one
      if (currentSection) {
        currentSection.content = contentBuffer.join('\n');
        sections.push(currentSection);
        contentBuffer = [];
      }

      // Start a new section
      currentSection = {
        level: headerMatch[1].length,
        title: headerMatch[2],
        content: ''
      };
    } else if (currentSection) {
      // Add to current section's content
      contentBuffer.push(line);
    }
  }

  // Don't forget the last section
  if (currentSection) {
    currentSection.content = contentBuffer.join('\n');
    sections.push(currentSection);
  }

  return sections;
}

export class MarkdownRendererTool {
  /**
   * Process markdown for consistent rendering
   */
  preprocessMarkdown(markdown: string): string {
    return preprocessMarkdown(markdown);
  }

  /**
   * Format markdown for PDF rendering
   */
  markdownToPdfFormat(markdown: string): string {
    return markdownToPdfFormat(markdown);
  }

  /**
   * Convert markdown to plain text
   */
  markdownToPlainText(markdown: string): string {
    return markdownToPlainText(markdown);
  }

  /**
   * Extract sections from markdown
   */
  extractSections(markdown: string): Array<{level: number, title: string, content: string}> {
    return extractMarkdownSections(markdown);
  }

  /**
   * Process markdown content with the specified operation
   * @param options - Processing options
   * @returns Processed markdown
   */
  async process(options: {
    markdown: string;
    operation: 'preprocess' | 'toPdfFormat' | 'toPlainText' | 'extractSections';
  }): Promise<{
    success: boolean;
    content: string;
    sections?: Array<{level: number, title: string, content: string}>;
    error?: string;
  }> {
    try {
      const { markdown, operation } = options;

      if (!markdown) {
        return {
          success: false,
          content: '',
          error: 'Markdown content is required'
        };
      }

      switch (operation) {
        case 'preprocess':
          return {
            success: true,
            content: this.preprocessMarkdown(markdown)
          };

        case 'toPdfFormat':
          return {
            success: true,
            content: this.markdownToPdfFormat(markdown)
          };

        case 'toPlainText':
          return {
            success: true,
            content: this.markdownToPlainText(markdown)
          };

        case 'extractSections':
          const sections = this.extractSections(markdown);
          return {
            success: true,
            content: JSON.stringify(sections),
            sections
          };

        default:
          return {
            success: false,
            content: '',
            error: `Unknown operation: ${operation}`
          };
      }
    } catch (error) {
      console.error('Error in markdown renderer process:', error);
      return {
        success: false,
        content: '',
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Tool definition for function calling
   * This can be used in the tools array when calling Groq API
   */
  getToolDefinition() {
    return {
      type: "function",
      function: {
        name: "markdownRenderer",
        description: "Process and format markdown content for various display purposes",
        parameters: {
          type: "object",
          properties: {
            markdown: {
              type: "string",
              description: "The markdown content to process"
            },
            operation: {
              type: "string",
              description: "The operation to perform on the markdown",
              enum: ["preprocess", "toPdfFormat", "toPlainText", "extractSections"]
            }
          },
          required: ["markdown", "operation"]
        }
      }
    };
  }
}

// Export a singleton instance
export const markdownRendererTool = new MarkdownRendererTool();
