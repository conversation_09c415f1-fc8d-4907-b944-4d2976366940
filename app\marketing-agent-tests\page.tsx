'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { checkMarketingAgentAccess } from '../lib/firebase/accounts';
import MarkdownRenderer from '../../components/MarkdownRenderer';
import MarketingDocumentsTab from '../../components/marketing/MarketingDocumentsTab';
import AgentOutputsTab from '../../components/marketing/AgentOutputsTab';
import AgentCollaborationTab from '../../components/marketing/AgentCollaborationTab';
import { Sparkles, CheckCircle } from 'lucide-react';
// These imports were used by the renderChartForPdf function
// Keeping the comment for reference

// Define test types
type AgentType = 'strategic-director' | 'research-insights' | 'content-creator' | 'social-media-orchestrator' | 'analytics-reporting' | 'team';
type ModelProvider = 'openai' | 'anthropic' | 'groq' | 'google';

interface TestResult {
  thinking: string;
  output: string;
  documentUrl?: string;
  agentMessages?: {
    from: string;
    to: string;
    message: string;
  }[];
}

// This function is used by the API route to render charts in PDFs
// It's kept here for reference but not directly used in this component

const MarketingAgentTests: React.FC = () => {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [selectedAgent, setSelectedAgent] = useState<AgentType>('strategic-director');
  const [selectedProvider, setSelectedProvider] = useState<ModelProvider>('openai');
  const [selectedModel, setSelectedModel] = useState('gpt-4o');
  const [testInput, setTestInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isGeneratingTests, setIsGeneratingTests] = useState(false);
  const [testResult, setTestResult] = useState<TestResult | null>(null);
  const [activeTab, setActiveTab] = useState<'test' | 'documents' | 'outputs' | 'collaboration'>('test');
  const [customTestCases, setCustomTestCases] = useState<Record<AgentType, string[]>>({} as Record<AgentType, string[]>);
  const [showToast, setShowToast] = useState(false);
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);

  // Define available models for each provider
  const providerModels: Record<ModelProvider, string[]> = {
    'openai': ['gpt-5-2025-08-07', 'gpt-4o', 'gpt-4.1-2025-04-14', 'o3-2025-04-16', 'o3-mini-2025-01-31', 'o1-mini-2024-09-12'],
    'anthropic': ['claude-sonnet-4-0', 'claude-opus-4-1-20250805', 'claude-opus-4-0'],
    'groq': ['deepseek-r1-distill-llama-70b', 'llama-3.3-70b-versatile'],
    'google': ['gemini-1.5-pro', 'gemini-1.5-flash', 'gemini-2.5-flash', 'gemini-2.5-pro']
  };

  // Predefined test cases for each agent
  const predefinedTests: Record<AgentType, string[]> = {
    'strategic-director': [
      'Analyze the product "IntelligentFitness.ai" - an AI-powered fitness coaching app that provides personalized workout plans and real-time form correction using computer vision.',
      'Generate a marketing strategy for "EcoPackage" - a sustainable packaging solution for e-commerce businesses that reduces plastic waste by 80%.',
      'Create a long-form document about the future of AI in marketing for the next 5 years.',
      'Generate a strategy document for "HealthTech Solutions" that includes an executive summary, marketing objectives, and implementation timeline.',
      'Create a PDF report for the Q1 marketing performance of "CloudSecure" cybersecurity software.'
    ],
    'research-insights': [
      'Conduct market research on the competitive landscape for AI-powered fitness apps.',
      'Research the target audience demographics and psychographics for sustainable packaging solutions.',
      'Analyze current trends in content marketing for SaaS products.'
    ],
    'content-creator': [
      'Create a content strategy for promoting an AI-powered fitness coaching app.',
      'Develop key messaging for a sustainable packaging solution targeting e-commerce businesses.',
      'Write a blog post about how AI is transforming the fitness industry.'
    ],
    'social-media-orchestrator': [
      'Create a social media calendar for launching an AI fitness app.',
      'Develop a platform strategy for promoting sustainable packaging across Instagram, Twitter, and LinkedIn.',
      'Design a viral social media campaign to raise awareness about plastic waste in packaging.'
    ],
    'analytics-reporting': [
      'Set up KPI tracking for an AI fitness app marketing campaign.',
      'Create a reporting framework for measuring the success of a sustainable packaging product launch.',
      'Analyze the performance of a content marketing strategy for a SaaS product.'
    ],
    'team': [
      'Create a comprehensive marketing campaign for "IntelligentFitness.ai" - an AI-powered fitness coaching app.',
      'Develop a full marketing strategy for "EcoPackage" - a sustainable packaging solution for e-commerce.',
      'Plan and execute a product launch for "SmartHome Hub" - a central device that connects and controls all smart home devices.'
    ]
  };

  const handleAgentChange = (agent: AgentType) => {
    setSelectedAgent(agent);
    setTestInput('');
    setTestResult(null);

    // If we don't have custom test cases for this agent yet, clear the input
    // Otherwise, the custom test cases will be displayed when switching to this agent
  };

  const handleProviderChange = (provider: ModelProvider) => {
    setSelectedProvider(provider);
    // Set the first model of the selected provider as default
    setSelectedModel(providerModels[provider][0]);
  };

  const handleModelChange = (model: string) => {
    setSelectedModel(model);
  };

  const handlePredefinedTest = (test: string) => {
    setTestInput(test);
  };

  // Check authentication and authorization
  useEffect(() => {
    const checkAuth = async () => {
      setIsCheckingAuth(true);

      // If not authenticated, redirect to login
      if (status === 'unauthenticated') {
        router.push('/marketing-agent-tests/login');
        return;
      }

      // If still loading auth state, wait
      if (status === 'loading') {
        return;
      }

      // If authenticated, check authorization in Accounts collection
      if (session?.user?.email) {
        try {
          const hasAccess = await checkMarketingAgentAccess(session.user.email);

          if (hasAccess) {
            console.log('User has marketing agent access');
            setIsAuthorized(true);
          } else {
            console.log('User does not have marketing agent access');
            setIsAuthorized(false);
          }
        } catch (error) {
          console.error('Error checking authorization:', error);
          setIsAuthorized(false);
        }
      }

      setIsCheckingAuth(false);
    };

    checkAuth();
  }, [session, status, router]);

  // Effect to hide toast after 3 seconds
  useEffect(() => {
    if (showToast) {
      const timer = setTimeout(() => {
        setShowToast(false);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [showToast]);

  const handleGenerateTestCases = async () => {
    try {
      setIsGeneratingTests(true);

      const response = await fetch('/api/generate-test-cases', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          agentType: selectedAgent,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to generate test cases');
      }

      const data = await response.json();

      if (data.testCases && Array.isArray(data.testCases)) {
        // Update custom test cases for the current agent
        setCustomTestCases(prev => ({
          ...prev,
          [selectedAgent]: data.testCases
        }));

        // Show success toast
        setShowToast(true);
      }
    } catch (error) {
      console.error('Error generating test cases:', error);
    } finally {
      setIsGeneratingTests(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!testInput.trim()) return;

    setIsLoading(true);
    setTestResult(null);

    try {
      const response = await fetch('/api/marketing-agent-tests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          agentType: selectedAgent,
          prompt: testInput,
          includeThinking: true,
          modelProvider: selectedProvider,
          modelName: selectedModel,
          renderChartFunction: true // Flag to indicate chart rendering is supported
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to get response from agent');
      }

      const data = await response.json();
      console.log('Test result data:', data);
      setTestResult(data);

      // Debug: Check if the result is being set correctly
      setTimeout(() => {
        console.log('Test result state after setting:', testResult);

        // Scroll to results section
        const resultsSection = document.getElementById('results-section');
        if (resultsSection) {
          resultsSection.scrollIntoView({ behavior: 'smooth' });
        }
      }, 100);
    } catch (error) {
      console.error('Error testing agent:', error);
      setTestResult({
        thinking: 'Error occurred during processing.',
        output: 'Failed to get a response from the agent. Please try again.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getAgentDescription = (agent: AgentType): string => {
    switch (agent) {
      case 'strategic-director':
        return 'Develops comprehensive marketing strategies, analyzes product value propositions, and coordinates the marketing team workflow.';
      case 'research-insights':
        return 'Conducts market research, analyzes competitors, and identifies target audience insights.';
      case 'content-creator':
        return 'Creates compelling content, develops messaging strategies, and maintains brand voice consistency.';
      case 'social-media-orchestrator':
        return 'Plans and executes social media campaigns, manages platform-specific strategies, and optimizes engagement.';
      case 'analytics-reporting':
        return 'Tracks KPIs, analyzes marketing performance, and provides data-driven optimization recommendations.';
      case 'team':
        return 'Tests the entire marketing team working together on a comprehensive campaign.';
      default:
        return '';
    }
  };

  // Show loading state while checking authorization
  if (isCheckingAuth) {
    return (
      <div className="min-h-screen bg-gray-900 text-gray-200 flex items-center justify-center">
        <div className="p-8 bg-gray-800 rounded-lg mb-8 border border-gray-700">
          <div className="flex flex-col items-center justify-center space-y-4">
            <div className="relative">
              <div className="w-16 h-16 border-t-4 border-b-4 border-purple-500 rounded-full animate-spin"></div>
              <div className="w-16 h-16 border-l-4 border-r-4 border-transparent rounded-full absolute top-0 animate-spin-slow"></div>
            </div>
            <p className="text-center text-purple-300 font-medium">Checking authorization...</p>
          </div>
        </div>
      </div>
    );
  }

  // Show unauthorized message if user doesn't have access
  if (!isAuthorized && !isCheckingAuth) {
    return (
      <div className="min-h-screen bg-gray-900 text-gray-200 flex items-center justify-center">
        <div className="p-8 bg-gray-800 rounded-lg mb-8 border border-red-700 max-w-md">
          <div className="flex flex-col items-center justify-center space-y-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m0 0v2m0-2h2m-2 0H9m3-3V6a3 3 0 00-3-3H6a3 3 0 00-3 3v7a3 3 0 003 3h7a3 3 0 003-3z" />
            </svg>
            <h2 className="text-xl font-bold text-red-300">Access Denied</h2>
            <p className="text-center text-gray-300">You don't have permission to access the Marketing Agent Tests. Please contact an administrator if you believe this is an error.</p>
            <button
              onClick={() => router.push('/')}
              className="mt-4 px-4 py-2 bg-purple-700 hover:bg-purple-800 text-white rounded-lg font-medium transition-colors"
            >
              Return to Home
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 text-gray-200">
      {/* Success Toast Notification */}
      {showToast && (
        <div className="fixed top-4 right-4 z-50 flex items-center p-4 bg-green-800 text-white rounded-lg shadow-lg animate-fadeIn">
          <CheckCircle className="w-5 h-5 mr-2" />
          <span>Fresh test cases generated successfully!</span>
        </div>
      )}

      <div className="container mx-auto px-4 py-8 max-w-7xl">
        <div className="mb-6 flex justify-between items-center">
          <div className="flex items-center">
            <img
              src="/logo5b.png"
              alt="ike Logo"
              className="h-14 w-auto ml-3 mr-4 hover:opacity-80 transition-opacity duration-300"
            />
            <h1 className="text-3xl font-bold items-center text-purple-200">Marketing Agent Testing Suite</h1>
          </div>

          <div className="flex items-center gap-4">
            <div className="text-sm text-gray-400">Test and evaluate marketing agent performance</div>

            {/* User Profile Section */}
            {session?.user && (
              <div className="flex items-center bg-gray-800 rounded-full px-2 py-1 border border-purple-700">
                <div className="flex flex-col items-end mr-3">
                  <span className="text-sm font-medium text-white">{session.user.name}</span>
                  <span className="text-xs text-gray-400">{session.user.email}</span>
                </div>
                {session.user.image ? (
                  <img
                    src={session.user.image}
                    alt="User Profile"
                    className="h-10 w-10 rounded-full border-2 border-purple-500"
                    title={`Authenticated as: ${session.user.email}`}
                  />
                ) : (
                  <div className="h-10 w-10 rounded-full bg-purple-700 flex items-center justify-center text-white font-bold">
                    {session.user.name?.charAt(0) || session.user.email?.charAt(0) || '?'}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="flex border-b border-gray-700 mb-6">
          <button
            onClick={() => setActiveTab('test')}
            data-tab="test"
            className={`py-2 px-4 font-medium text-sm ${activeTab === 'test' ? 'text-purple-300 border-b-2 border-purple-500' : 'text-gray-400 hover:text-gray-300'}`}
          >
            Test Agents
          </button>
          <button
            onClick={() => setActiveTab('outputs')}
            data-tab="outputs"
            className={`py-2 px-4 font-medium text-sm ${activeTab === 'outputs' ? 'text-purple-300 border-b-2 border-purple-500' : 'text-gray-400 hover:text-gray-300'}`}
          >
            Agent Outputs
          </button>
          <button
            onClick={() => setActiveTab('documents')}
            data-tab="documents"
            className={`py-2 px-4 font-medium text-sm ${activeTab === 'documents' ? 'text-purple-300 border-b-2 border-purple-500' : 'text-gray-400 hover:text-gray-300'}`}
          >
            Agent Documents
          </button>
          <button
            onClick={() => setActiveTab('collaboration')}
            data-tab="collaboration"
            className={`py-2 px-4 font-medium text-sm ${activeTab === 'collaboration' ? 'text-purple-300 border-b-2 border-purple-500' : 'text-gray-400 hover:text-gray-300'}`}
          >
            Strategic Analysis
          </button>
        </div>

        {/* Test Tab */}
        {activeTab === 'test' && (
          <div className="bg-gray-800 rounded-lg shadow-lg p-6 border border-gray-700">


        {/* Instructions */}
        <div className="mb-8 p-4 bg-gray-800 rounded-lg border border-gray-700">
          <h2 className="text-xl font-semibold mb-2 text-purple-200">How to Use This Test Suite</h2>
          <ol className="list-decimal list-inside space-y-2 text-gray-200">
            <li>Select an agent to test from the options below</li>
            <li>Choose a predefined test or enter your own custom prompt</li>
            <li>Click "Run Test" to process your request</li>
            <li>View the agent's output response, output, and any generated documents in the results section</li>
            <li>For document generation, use prompts that include "document" or "PDF" keywords</li>
          </ol>
        </div>

        {/* Agent Selection */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4 text-purple-200">Select Agent to Test</h2>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {Object.keys(predefinedTests).map((agent) => (
              <button
                key={agent}
                onClick={() => handleAgentChange(agent as AgentType)}
                className={`p-4 rounded-lg transition-colors ${
                  selectedAgent === agent
                    ? 'bg-purple-700 text-white border border-purple-400'
                    : 'bg-gray-800 hover:bg-gray-700 text-gray-200 border border-gray-700'
                }`}
              >
                {agent.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
              </button>
            ))}
          </div>

          {/* Agent Description */}
          <div className="mt-4 p-4 bg-gray-800 rounded-lg border border-gray-700">
            <p className="text-amber-500">{getAgentDescription(selectedAgent)}</p>
          </div>
        </div>

        {/* Model Provider and Model Selection - Compact Layout */}
        <div className="mb-6">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-2">
            <h2 className="text-lg font-medium text-purple-200">Model Selection</h2>
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-300">Currently using:</span>
              <span className="bg-purple-700 text-white text-sm px-2 py-1 rounded-md">
                {selectedProvider.charAt(0).toUpperCase() + selectedProvider.slice(1)} / {selectedModel}
              </span>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            {/* Provider Selection */}
            <div className="bg-gray-800 p-3 rounded-lg border border-gray-700">
              <h3 className="text-sm font-medium mb-2 text-purple-300">Provider</h3>
              <div className="flex flex-wrap gap-2">
                {Object.keys(providerModels).map((provider) => (
                  <button
                    key={provider}
                    onClick={() => handleProviderChange(provider as ModelProvider)}
                    className={`px-3 py-1 text-xs rounded transition-colors ${
                      selectedProvider === provider
                        ? 'bg-purple-700 text-white'
                        : 'bg-gray-700 hover:bg-gray-600 text-gray-200'
                    }`}
                  >
                    {provider.charAt(0).toUpperCase() + provider.slice(1)}
                  </button>
                ))}
              </div>
            </div>

            {/* Model Selection */}
            <div className="bg-gray-800 p-3 rounded-lg border border-gray-700">
              <h3 className="text-sm font-medium mb-2 text-purple-300">Model</h3>
              <div className="flex flex-wrap gap-2">
                {providerModels[selectedProvider].map((model) => (
                  <button
                    key={model}
                    onClick={() => handleModelChange(model)}
                    className={`px-3 py-1 text-xs rounded transition-colors ${
                      selectedModel === model
                        ? 'bg-purple-700 text-white'
                        : 'bg-[#2d2a3a] hover:bg-[#3d2e5a] text-gray-200'
                    }`}
                  >
                    {model}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Test Form */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4 text-purple-200">Test Input</h2>

          {/* Predefined Tests */}
          <div className="mb-4">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-lg font-medium text-purple-300">Predefined Tests</h3>
              <button
                onClick={handleGenerateTestCases}
                disabled={isGeneratingTests}
                className="flex items-center px-3 py-1.5 bg-indigo-700 hover:bg-indigo-800 text-white rounded-lg text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {isGeneratingTests ? (
                  <>
                    <svg className="animate-spin w-4 h-4 mr-1.5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Generating...
                  </>
                ) : (
                  <>
                    <Sparkles className="w-4 h-4 mr-1.5" />
                    Generate Fresh Tests with Claude Sonnet 4.0
                  </>
                )}
              </button>
            </div>

            {/* Display custom generated test cases if available */}
            {customTestCases[selectedAgent] && customTestCases[selectedAgent].length > 0 ? (
              <div className="mb-6">
                <div className="flex items-center mb-2">
                  <div className="h-0.5 flex-grow bg-indigo-700/30"></div>
                  <span className="px-3 text-xs font-medium text-indigo-400">Claude 4.0 GENERATED TESTS</span>
                  <div className="h-0.5 flex-grow bg-indigo-700/30"></div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
                  {customTestCases[selectedAgent].map((test, index) => (
                    <button
                      key={`custom-${index}`}
                      onClick={() => handlePredefinedTest(test)}
                      className="p-3 bg-indigo-900/30 hover:bg-indigo-800/40 rounded-lg text-left text-sm border border-indigo-700/50"
                    >
                      {test}
                    </button>
                  ))}
                </div>
                <div className="flex items-center mb-4">
                  <div className="h-0.5 flex-grow bg-gray-700/30"></div>
                  <span className="px-3 text-xs font-medium text-gray-500">DEFAULT TESTS</span>
                  <div className="h-0.5 flex-grow bg-gray-700/30"></div>
                </div>
              </div>
            ) : null}

            {/* Default predefined tests */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {predefinedTests[selectedAgent].map((test, index) => (
                <button
                  key={index}
                  onClick={() => handlePredefinedTest(test)}
                  className="p-3 bg-gray-700 hover:bg-gray-600 rounded-lg text-left text-sm border border-gray-700"
                >
                  {test}
                </button>
              ))}
            </div>
          </div>

          {/* Custom Test Input */}
          <form id="test-input-form" onSubmit={handleSubmit}>
            <div className="mb-4">
              <label htmlFor="testInput" className="block text-lg font-medium mb-2 text-purple-300">
                Custom Test Input
              </label>
              <textarea
                id="testInput"
                value={testInput}
                onChange={(e) => setTestInput(e.target.value)}
                className="w-full p-4 bg-gray-800 text-gray-200 border border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                rows={5}
                placeholder={`Enter your test prompt for the ${selectedAgent.split('-').join(' ')}...`}
              />
            </div>

            <button
              type="submit"
              disabled={isLoading || !testInput.trim()}
              className="px-6 py-3 bg-purple-700 hover:bg-purple-800 text-white rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center"
            >
              {isLoading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Processing...
                </>
              ) : 'Run Test'}
            </button>
          </form>
        </div>

        {/* Test Results - Moved to the results section */}

        {/* Results Section */}
        <div id="results-section" className="mb-8 mt-12 border-t border-purple-700 pt-8">
          <h2 className="text-2xl font-bold mb-4 text-purple-300">Test Results</h2>

          {!isLoading && !testResult && (
            <div className="p-8 bg-gray-800 rounded-lg border border-gray-700">
              <p className="text-center text-gray-300">Run a test to see results here</p>
            </div>
          )}

          {isLoading && (
            <div className="p-8 bg-gray-800 rounded-lg mb-8 border border-gray-700">
              <div className="flex flex-col items-center justify-center space-y-4">
                <div className="relative">
                  <div className="w-16 h-16 border-t-4 border-b-4 border-purple-500 rounded-full animate-spin"></div>
                  <div className="w-16 h-16 border-l-4 border-r-4 border-transparent rounded-full absolute top-0 animate-spin-slow"></div>
                </div>
                <p className="text-center text-purple-300 font-medium">Agent is thinking...</p>
                <p className="text-center text-gray-400 text-sm">This may take a moment depending on the complexity of the request</p>
              </div>
            </div>
          )}

          {testResult && (
            <div className="border border-gray-700 rounded-lg p-6 bg-gray-800">
              <div className="flex flex-col md:flex-row md:justify-between mb-4">
                <h3 className="text-xl font-semibold text-purple-200">Results from {selectedAgent.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}</h3>
                <div className="mt-2 md:mt-0 text-gray-300">
                  <span className="font-medium">Model: </span>
                  <span className="bg-purple-700 px-2 py-1 rounded text-white text-sm">{selectedProvider.charAt(0).toUpperCase() + selectedProvider.slice(1)} / {selectedModel}</span>
                </div>
              </div>

            {/* Agent Thinking */}
            <div className="mb-6">
              <h3 className="text-lg font-medium mb-2 text-purple-300">Agent Output Response</h3>
              <div className="bg-gray-900 rounded-lg overflow-hidden border border-gray-700">
                {testResult.thinking ? (
                  <MarkdownRenderer content={testResult.thinking} />
                ) : (
                  <div className="p-4 text-gray-300">
                    <p>No output response available for this request. The agent processed this request directly.</p>
                  </div>
                )}
              </div>
            </div>

            {/* Agent Output */}
            <div className="mb-6">
              <h3 className="text-lg font-medium mb-2 text-purple-300">Agent Output</h3>
              <div className="bg-gray-900 rounded-lg overflow-hidden border border-gray-700">
                <MarkdownRenderer content={testResult.output} />
              </div>
            </div>

            {/* PDF Document Viewer */}
            {testResult.documentUrl && (
              <div className="mb-6">
                <h3 className="text-lg font-medium mb-2 text-purple-300">Generated Document</h3>
                <div className="bg-[#1a1625] rounded-lg p-4 border border-[#3d2e5a]">
                  <p className="text-gray-300 mb-4">A document has been generated. You can view or download it using the link below:</p>
                  <div className="flex flex-col sm:flex-row gap-4">
                    <a
                      href={testResult.documentUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="px-4 py-2 bg-purple-700 hover:bg-purple-800 text-white rounded-lg font-medium transition-colors inline-flex items-center justify-center"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                      Download PDF
                    </a>
                    <button
                      onClick={() => window.open(testResult.documentUrl, '_blank')}
                      className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg font-medium transition-colors inline-flex items-center justify-center"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                      View PDF
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Agent Messages (for team tests) */}
            {testResult.agentMessages && testResult.agentMessages.length > 0 && (
              <div>
                <h3 className="text-lg font-medium mb-2 text-purple-300">Team Communication</h3>
                <div className="bg-gray-900 rounded-lg p-4 border border-gray-700">
                  {testResult.agentMessages.map((msg, index) => (
                    <div key={index} className="mb-4 p-3 bg-gray-800 rounded-lg border border-gray-700">
                      <p className="text-purple-300 font-medium mb-1">
                        From: {msg.from} → To: {msg.to}
                      </p>
                      <p className="text-gray-300">{msg.message}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
          {/* Run Another Test Button */}
          {testResult && !isLoading && (
            <div className="mt-8 flex justify-center">
              <button
                onClick={() => {
                  const testForm = document.getElementById('test-input-form');
                  if (testForm) {
                    testForm.scrollIntoView({ behavior: 'smooth' });
                  }
                }}
                className="px-6 py-3 bg-purple-700 hover:bg-purple-800 text-white rounded-lg font-medium transition-colors inline-flex items-center justify-center"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                Run Another Test
              </button>
            </div>
          )}
        </div>
        </div>
        )}

        {/* Agent Outputs Tab */}
        {activeTab === 'outputs' && (
          <div className="bg-gray-800 rounded-lg shadow-lg p-6 border border-gray-700">
            <h2 className="text-xl font-semibold mb-6 text-purple-200">Marketing Agent Test Results</h2>
            <p className="text-gray-300 mb-4">Previous test results are displayed below. Select a result to view the agent's output, thinking process, and any generated documents.</p>
            <AgentOutputsTab />
          </div>
        )}

        {/* Documents Tab */}
        {activeTab === 'documents' && (
          <div className="bg-gray-800 rounded-lg shadow-lg p-6 border border-gray-700">
            <h2 className="text-xl font-semibold mb-6 text-purple-200">Marketing Agent Documents</h2>
            <p className="text-gray-300 mb-4">Documents generated by marketing agents are displayed below with metadata including date created, file size, and agent author.</p>
            <MarketingDocumentsTab />
          </div>
        )}

        {/* Agent Collaboration Tab */}
        {activeTab === 'collaboration' && (
          <div className="bg-gray-800 rounded-lg shadow-lg p-6 border border-gray-700">
            <h2 className="text-xl font-semibold mb-6 text-purple-200">Strategic Marketing Agent Analysis</h2>
            <p className="text-gray-300 mb-4">
              Watch the Strategic Director Agent reason through marketing problems by utilizing document search, question answering, and research capabilities in real-time.
              This demonstrates how a primary agent can leverage specialized tools and delegate to specialized agents like the Research Insights Agent to enhance its decision-making process.
            </p>
            <div className="mb-6 p-3 bg-indigo-900/30 border border-indigo-700/50 rounded-lg">
              <div className="flex items-start">
                <Sparkles className="w-5 h-5 text-indigo-400 mt-1 mr-2 flex-shrink-0" />
                <div>
                  <h3 className="text-sm font-medium text-indigo-300 mb-1">Research Insights Integration</h3>
                  <p className="text-xs text-gray-300">
                    Try submitting a research-related request like "Research the competitive landscape for AI fitness apps" or
                    "Analyze current trends in content marketing for SaaS products" to see the Strategic Director delegate to the
                    Research Insights Agent. The console logs will show the delegation process in detail.
                  </p>
                </div>
              </div>
            </div>
            <AgentCollaborationTab
              selectedProvider={selectedProvider}
              selectedModel={selectedModel}
              onNavigateToOutputs={() => setActiveTab('outputs')}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default MarketingAgentTests;

