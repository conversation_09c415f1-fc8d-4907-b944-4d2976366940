import type { QueryCodebaseRequest, StrategyRun, ABTestResult, QueryStrategyId } from './types';
import { dbQueryConfig } from './config';
import { executeStandalone } from './strategies/standalone';
import { executeContentSelectorAdapter } from './strategies/contentSelectorAdapter';
import { executeTwoStage } from './strategies/twoStage';
import { executeHybridInverted } from './strategies/hybridInverted';

const executors: Record<QueryStrategyId, { id: QueryStrategyId; execute: (req: QueryCodebaseRequest) => Promise<StrategyRun>; }> = {
  'standalone': { id: 'standalone', execute: executeStandalone },
  'content-selector': { id: 'content-selector', execute: executeContentSelectorAdapter },
  'two-stage': { id: 'two-stage', execute: executeTwoStage },
  'hybrid-inverted': { id: 'hybrid-inverted', execute: executeHybridInverted }
};

export async function queryCodebase(req: QueryCodebaseRequest, strategyId?: QueryStrategyId): Promise<StrategyRun> {
  const selected = strategyId || dbQueryConfig.defaultStrategy;
  const exec = executors[selected];
  if (!exec) throw new Error(`Unknown strategy: ${selected}`);
  return exec.execute(req);
}

export async function abTestQuery(req: QueryCodebaseRequest, strategies?: QueryStrategyId[]): Promise<ABTestResult> {
  const ids = strategies?.length
    ? strategies
    : (dbQueryConfig.abTest?.strategies?.length ? dbQueryConfig.abTest!.strategies : dbQueryConfig.enabledStrategies);

  const runs: StrategyRun[] = [];
  for (const id of ids) {
    const exec = executors[id];
    if (!exec) continue;
    try {
      const run = await exec.execute(req);
      runs.push(run);
    } catch (e: any) {
      runs.push({
        strategyId: id,
        response: { success: false, results: [], stats: { namespacesQueried: [], topKPerNamespace: req.topKPerNamespace || 5, totalMatches: 0, timingsMs: {} }, error: e?.message || 'Error' },
        metrics: { durationMs: 0, notes: [String(e?.message || e)] },
        error: e?.message || 'Error'
      });
    }
  }

  return { userId: req.userId, category: req.category, query: req.query, runs };
}

export function listStrategies(): QueryStrategyId[] {
  return ['standalone','content-selector','two-stage','hybrid-inverted'];
}

