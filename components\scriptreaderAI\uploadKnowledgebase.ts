/**
 * Knowledge Base Upload Utilities for ElevenLabs
 * 
 * This module provides utilities for uploading documents to ElevenLabs knowledge base
 * and managing RAG indexing for conversational AI agents.
 */

import { createElevenLabsClient } from './elevenlabs';

export interface KnowledgeBaseDocumentResponse {
  id: string;
  prompt_injectable: boolean;
}

export interface RagIndexResponse {
  status: 'created' | 'processing' | 'succeeded' | 'failed';
  progress?: number;
  message?: string;
}

/**
 * Uploads a document to ElevenLabs knowledge base
 * 
 * This function fetches a file from a URL and uploads it to the ElevenLabs
 * knowledge base system, making it available for association with agents.
 * 
 * @param fileUrl - URL of the file to upload
 * @param fileName - Name of the file for identification
 * @param fileType - MIME type of the file
 * @param apiKey - Optional API key to override the environment variable
 * @returns Knowledge base document ID and metadata
 * @throws Error if file type is unsupported, URL fetching fails, or API errors occur
 */
export async function uploadToKnowledgeBase(
  fileUrl: string,
  fileName: string,
  fileType: string,
  apiKey?: string
): Promise<KnowledgeBaseDocumentResponse> {
  try {
    const client = createElevenLabsClient(apiKey);

    // Validate file type against ElevenLabs supported formats
    const supportedTypes = [
      "application/pdf",
      "text/plain",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      "text/markdown",
    ];

    if (!supportedTypes.includes(fileType)) {
      throw new Error(`Unsupported file type: ${fileType}. Supported types: ${supportedTypes.join(", ")}`);
    }

    // Fetch the file from the URL with error handling
    let fileResponse;
    try {
      fileResponse = await fetch(fileUrl);
      if (!fileResponse.ok) {
        throw new Error(`HTTP error ${fileResponse.status}: ${fileResponse.statusText}`);
      }
    } catch (fetchError) {
      throw new Error(`Failed to fetch file: ${fetchError instanceof Error ? fetchError.message : String(fetchError)}`);
    }

    // Convert response to blob and validate
    let fileBlob;
    try {
      fileBlob = await fileResponse.blob();
      if (fileBlob.size === 0) {
        throw new Error("Fetched file is empty (0 bytes)");
      }
    } catch (blobError) {
      throw new Error(`Failed to process file data: ${blobError instanceof Error ? blobError.message : String(blobError)}`);
    }

    // Create File object and upload to knowledge base
    const file = new File([fileBlob], fileName, { type: fileType });

    try {
      // Call the ElevenLabs API to add to knowledge base
      const response = await client.conversationalAi.addToKnowledgeBase({
        file,
        name: fileName,
      });

      // Validate response
      if (!response || !response.id) {
        throw new Error("Invalid response from ElevenLabs API: Missing document ID");
      }

      return {
        id: response.id,
        prompt_injectable: response.prompt_injectable || false,
      };
    } catch (apiError) {
      // Format API errors for better debugging
      if (apiError instanceof Error) {
        throw new Error(`ElevenLabs API error: ${apiError.message}`);
      }
      throw new Error(`Unknown error from ElevenLabs API: ${String(apiError)}`);
    }
  } catch (error) {
    console.error("[ELEVENLABS] Error uploading to ElevenLabs Knowledge Base:", error);
    throw error;
  }
}

/**
 * Triggers RAG indexing for a knowledge base document
 * 
 * This function initiates RAG indexing for the specified document and waits until
 * the indexing process is complete (status: succeeded).
 * 
 * @param documentationId - ID of the knowledge base document to index
 * @param apiKey - Optional API key to override the environment variable
 * @param forceReindex - Whether to force reindexing if the document is already indexed (default: false)
 * @param model - The model to use for RAG indexing (default: "e5_mistral_7b_instruct")
 * @returns RAG index status and progress
 * @throws Error if RAG indexing fails or times out
 */
export async function computeRagIndex(
  documentationId: string,
  apiKey?: string,
  forceReindex: boolean = false,
  model: "e5_mistral_7b_instruct" | "gte_Qwen2_15B_instruct" = "e5_mistral_7b_instruct"
): Promise<RagIndexResponse> {
  try {
    // Validate input parameters
    if (!documentationId || typeof documentationId !== 'string') {
      throw new Error("Documentation ID is required and must be a string");
    }

    const apiKeyToUse = apiKey || process.env.ELEVENLABS_API_KEY || '***************************************************';

    // Construct the URL with the force_reindex query parameter
    const url = new URL(`https://api.elevenlabs.io/v1/convai/knowledge-base/${encodeURIComponent(documentationId)}/rag-index`);
    url.searchParams.append("force_reindex", forceReindex.toString());

    // Log the attempt
    console.log(`[ELEVENLABS] Triggering RAG indexing for document ${documentationId} with model ${model}${forceReindex ? " (force reindex)" : ""}`);

    // Initial request to trigger RAG indexing
    const initialResponse = await fetch(url.toString(), {
      method: "POST",
      headers: {
        "xi-api-key": apiKeyToUse,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ model }),
    });

    if (!initialResponse.ok) {
      const errorText = await initialResponse.text();
      throw new Error(`Failed to trigger RAG indexing: ${initialResponse.status} ${initialResponse.statusText} - ${errorText}`);
    }

    let result: RagIndexResponse = await initialResponse.json();
    console.log(`[ELEVENLABS] RAG indexing initiated:`, result);

    // If the status is already "succeeded", return immediately
    if (result.status === "succeeded") {
      console.log(`[ELEVENLABS] RAG indexing already completed for document ${documentationId}`);
      return result;
    }

    // If the status is "failed", throw an error
    if (result.status === "failed") {
      throw new Error(`RAG indexing failed for document ${documentationId}`);
    }

    // Poll the status until it becomes "succeeded" or "failed"
    const maxAttempts = 30; // Maximum number of polling attempts (30 * 5s = 150s timeout)
    const pollingInterval = 5000; // Poll every 5 seconds
    let attempts = 0;

    while (attempts < maxAttempts) {
      attempts++;
      await new Promise(resolve => setTimeout(resolve, pollingInterval));

      const statusResponse = await fetch(url.toString(), {
        method: "POST",
        headers: {
          "xi-api-key": apiKeyToUse,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ model }),
      });

      if (!statusResponse.ok) {
        const errorText = await statusResponse.text();
        throw new Error(`Failed to check RAG indexing status: ${statusResponse.status} ${statusResponse.statusText} - ${errorText}`);
      }

      result = await statusResponse.json();
      console.log(`[ELEVENLABS] RAG indexing status (attempt ${attempts}/${maxAttempts}):`, result);

      if (result.status === "succeeded") {
        console.log(`[ELEVENLABS] RAG indexing completed successfully for document ${documentationId}`);
        return result;
      }

      if (result.status === "failed") {
        throw new Error(`RAG indexing failed for document ${documentationId}`);
      }

      // Continue polling if status is "created" or "processing"
    }

    throw new Error(`RAG indexing for document ${documentationId} did not complete within the expected time (${maxAttempts * pollingInterval / 1000} seconds)`);
  } catch (error) {
    console.error("[ELEVENLABS] Error during RAG indexing:", error);
    throw error;
  }
}

/**
 * Checks if a document already exists in the knowledge base to prevent duplicates
 *
 * @param fileName - Name of the file to check for
 * @param apiKey - Optional API key to override the environment variable
 * @returns Document ID if found, null if not found
 */
export async function checkDocumentExists(fileName: string, apiKey?: string): Promise<string | null> {
  try {
    const client = createElevenLabsClient(apiKey);
    const response = await client.conversationalAi.getKnowledgeBaseList();

    console.log("[ELEVENLABS] checkDocumentExists - Raw response:", JSON.stringify(response, null, 2));

    // Handle different possible response structures
    let documents: any[] = [];

    if (Array.isArray(response)) {
      documents = response;
    } else if (response && typeof response === 'object') {
      const responseObj = response as any;
      if (Array.isArray(responseObj.documents)) {
        documents = responseObj.documents;
      } else if (Array.isArray(responseObj.data)) {
        documents = responseObj.data;
      } else if (Array.isArray(responseObj.items)) {
        documents = responseObj.items;
      } else if (Array.isArray(responseObj.results)) {
        documents = responseObj.results;
      } else {
        console.warn("[ELEVENLABS] Unexpected response format from getKnowledgeBaseList:", response);
        console.warn("[ELEVENLABS] Available properties:", Object.keys(responseObj));
        return null;
      }
    } else {
      console.warn("[ELEVENLABS] Unexpected response type from getKnowledgeBaseList:", typeof response);
      return null;
    }

    // Look for a document with the same name
    const existingDoc = documents.find(doc => doc.name === fileName);
    return existingDoc ? existingDoc.id : null;
  } catch (error) {
    console.error("[ELEVENLABS] Error checking document existence:", error);
    return null; // Return null on error to allow upload attempt
  }
}

/**
 * Enhanced document upload with deduplication
 * Checks for existing documents before uploading to prevent duplicates
 *
 * @param fileUrl - URL of the file to upload
 * @param fileName - Name of the file for identification
 * @param fileType - MIME type of the file
 * @param apiKey - Optional API key to override the environment variable
 * @param forceUpload - Whether to upload even if document exists (default: false)
 * @returns Upload result with document ID and status
 */
export async function uploadToKnowledgeBaseWithDeduplication(
  fileUrl: string,
  fileName: string,
  fileType: string,
  apiKey?: string,
  forceUpload: boolean = false
): Promise<{ id: string; prompt_injectable: boolean; wasExisting: boolean; message: string }> {
  try {
    console.log(`[ELEVENLABS] Checking for existing document: ${fileName}`);

    // Check if document already exists
    const existingDocId = await checkDocumentExists(fileName, apiKey);

    if (existingDocId && !forceUpload) {
      console.log(`[ELEVENLABS] Document already exists: ${fileName} (ID: ${existingDocId})`);
      return {
        id: existingDocId,
        prompt_injectable: true, // Assume existing docs are injectable
        wasExisting: true,
        message: `Document '${fileName}' already exists in knowledge base`
      };
    }

    if (existingDocId && forceUpload) {
      console.log(`[ELEVENLABS] Force uploading despite existing document: ${fileName}`);
    }

    // Upload the document
    const uploadResult = await uploadToKnowledgeBase(fileUrl, fileName, fileType, apiKey);

    return {
      ...uploadResult,
      wasExisting: false,
      message: `Document '${fileName}' uploaded successfully`
    };
  } catch (error) {
    console.error("[ELEVENLABS] Error in enhanced upload:", error);
    throw error;
  }
}

/**
 * Get all indexed documents for an agent to check what's already available
 *
 * @param agentId - ElevenLabs agent ID
 * @param apiKey - Optional API key to override the environment variable
 * @returns List of document IDs associated with the agent
 */
export async function getAgentIndexedDocuments(agentId: string, apiKey?: string): Promise<string[]> {
  try {
    const apiKeyToUse = apiKey || process.env.ELEVENLABS_API_KEY || '***************************************************';

    const response = await fetch(
      `https://api.elevenlabs.io/v1/convai/agents/${encodeURIComponent(agentId)}`,
      {
        method: "GET",
        headers: {
          "xi-api-key": apiKeyToUse,
          "Content-Type": "application/json",
        },
      }
    );

    if (!response.ok) {
      const errorText = await response.text();

      // Handle 404 specifically - agent doesn't exist in ElevenLabs yet
      if (response.status === 404) {
        console.warn(`[ELEVENLABS] Agent ${agentId} not found in ElevenLabs - returning empty document list`);
        return [];
      }

      throw new Error(`Failed to fetch agent configuration: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const agentData = await response.json();

    // Extract document IDs from agent configuration
    const documentIds = agentData.document_ids || [];

    console.log(`[ELEVENLABS] Agent ${agentId} has ${documentIds.length} indexed documents`);
    return documentIds;
  } catch (error) {
    console.error("[ELEVENLABS] Error getting agent indexed documents:", error);
    return [];
  }
}

/**
 * Check if a specific document is already indexed for an agent
 *
 * @param agentId - ElevenLabs agent ID
 * @param documentId - Document ID to check
 * @param apiKey - Optional API key to override the environment variable
 * @returns True if document is already indexed for the agent
 */
export async function isDocumentIndexedForAgent(
  agentId: string,
  documentId: string,
  apiKey?: string
): Promise<boolean> {
  try {
    const indexedDocs = await getAgentIndexedDocuments(agentId, apiKey);
    return indexedDocs.includes(documentId);
  } catch (error) {
    console.error("[ELEVENLABS] Error checking document indexing status:", error);
    return false;
  }
}
