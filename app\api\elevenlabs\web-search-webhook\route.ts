import { NextRequest, NextResponse } from 'next/server';
import { internetSearchTool } from '../../../../lib/tools/internetSearchTool';

/**
 * Webhook endpoint for ElevenLabs DocumentationGeneration agent web search
 * This endpoint is called by the voice agent when web search is requested
 */
export async function POST(request: NextRequest) {
  try {
    console.log('[WEB_SEARCH_WEBHOOK] Received web search request from voice agent');
    console.log('[WEB_SEARCH_WEBHOOK] Request headers:', Object.fromEntries(request.headers.entries()));
    console.log('[WEB_SEARCH_WEBHOOK] Request method:', request.method);
    console.log('[WEB_SEARCH_WEBHOOK] Request URL:', request.url);

    // Verify webhook authentication
    const authHeader = request.headers.get('authorization');
    const expectedAuth = `Bearer ${process.env.ELEVENLABS_WEBHOOK_SECRET || 'pmo-webhook-secret-2024'}`;

    // Allow multiple auth tokens for testing and production flexibility
    const validAuthTokens = [
      expectedAuth,
      'Bearer pmo-webhook-secret-2024',
      'Bearer default-secret',
      `Bearer ${process.env.INTERNAL_API_SECRET}`
    ].filter(Boolean);

    if (!authHeader || !validAuthTokens.includes(authHeader)) {
      console.error('[WEB_SEARCH_WEBHOOK] Unauthorized webhook request');
      console.error('[WEB_SEARCH_WEBHOOK] Received auth:', authHeader);
      console.error('[WEB_SEARCH_WEBHOOK] Expected one of:', validAuthTokens);
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body
    const body = await request.json();
    console.log('[WEB_SEARCH_WEBHOOK] Request body:', JSON.stringify(body, null, 2));

    // Extract parameters from ElevenLabs webhook format
    // ElevenLabs may send data in different formats, so we need to handle both
    let query, context, searchPurpose = 'general';

    if (body.query) {
      // Direct object format
      query = body.query;
      context = body.context;
      searchPurpose = body.searchPurpose || 'general';
    } else if (body.body && typeof body.body === 'object') {
      // Nested body format
      query = body.body.query;
      context = body.body.context;
      searchPurpose = body.body.searchPurpose || 'general';
    } else if (typeof body === 'string') {
      // String format - try to parse
      try {
        const parsed = JSON.parse(body);
        query = parsed.query;
        context = parsed.context;
        searchPurpose = parsed.searchPurpose || 'general';
      } catch (e) {
        console.error('[WEB_SEARCH_WEBHOOK] Failed to parse string body:', e);
      }
    }

    console.log('[WEB_SEARCH_WEBHOOK] Extracted parameters:', { query, context, searchPurpose });

    // Validate required parameters
    if (!query) {
      console.error('[WEB_SEARCH_WEBHOOK] Missing required parameters');
      return NextResponse.json({
        error: 'Missing required parameter: query is required'
      }, { status: 400 });
    }

    console.log('[WEB_SEARCH_WEBHOOK] Performing web search:', {
      query,
      searchPurpose,
      hasContext: !!context
    });

    // Use the imported internet search tool directly
    console.log('[WEB_SEARCH_WEBHOOK] Using internetSearchTool directly');

    const searchResult = await internetSearchTool.search(query, {
      numResults: 5 // Limit results for voice conversation context
    });

    if (!searchResult.success) {
      console.error('[WEB_SEARCH_WEBHOOK] Search failed:', searchResult.metadata?.error);
      return NextResponse.json({
        success: false,
        error: searchResult.metadata?.error || 'Web search failed',
        message: 'I apologize, but I encountered an error while searching the web. Please try rephrasing your search query.'
      }, { status: 500 });
    }

    console.log('[WEB_SEARCH_WEBHOOK] Search completed successfully:', {
      resultsCount: searchResult.results?.length || 0,
      query,
      searchPurpose
    });

    // Format results for voice conversation
    const formattedMessage = formatSearchResultsForVoice(
      searchResult.results,
      searchPurpose,
      context
    );

    const response = {
      success: true,
      message: formattedMessage,
      results: searchResult.results,
      searchDetails: searchResult.metadata
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('[WEB_SEARCH_WEBHOOK] Unexpected error:', error);
    console.error('[WEB_SEARCH_WEBHOOK] Error stack:', error instanceof Error ? error.stack : 'No stack trace');
    console.error('[WEB_SEARCH_WEBHOOK] Error details:', {
      name: error instanceof Error ? error.name : 'Unknown',
      message: error instanceof Error ? error.message : String(error),
      cause: error instanceof Error ? error.cause : undefined
    });

    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      message: 'I apologize, but I encountered an unexpected error while searching the web. Please try again later.',
      debug: process.env.NODE_ENV === 'development' ? {
        error: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString()
      } : undefined
    }, { status: 500 });
  }
}

/**
 * Format search results for voice conversation context
 */
function formatSearchResultsForVoice(
  results: any[], 
  searchPurpose: string, 
  context?: string
): string {
  if (!results || results.length === 0) {
    return "I couldn't find any relevant information for your search query. You might want to try different keywords or a more specific search term.";
  }

  let formattedMessage = '';

  // Add context-aware introduction
  switch (searchPurpose) {
    case 'research':
      formattedMessage = `I found ${results.length} research sources for your query. Here's what I discovered:\n\n`;
      break;
    case 'verification':
      formattedMessage = `I've verified the information and found ${results.length} relevant sources:\n\n`;
      break;
    case 'examples':
      formattedMessage = `I found ${results.length} examples that might be helpful:\n\n`;
      break;
    case 'best_practices':
      formattedMessage = `Here are ${results.length} sources about best practices:\n\n`;
      break;
    case 'current_trends':
      formattedMessage = `I found ${results.length} sources about current trends:\n\n`;
      break;
    case 'technical_info':
      formattedMessage = `Here's the technical information I found from ${results.length} sources:\n\n`;
      break;
    default:
      formattedMessage = `I found ${results.length} relevant results for your search:\n\n`;
  }

  // Add the search results
  results.slice(0, 3).forEach((result, index) => {
    formattedMessage += `${index + 1}. **${result.title}**\n`;
    formattedMessage += `   ${result.snippet}\n`;
    formattedMessage += `   Source: ${result.link}\n\n`;
  });

  // Add summary if more results available
  if (results.length > 3) {
    formattedMessage += `I found ${results.length - 3} additional sources that might be relevant. `;
  }

  // Add context-aware conclusion
  if (context) {
    formattedMessage += `\nThis information should help with ${context}. Would you like me to search for more specific information or generate a document incorporating these findings?`;
  } else {
    formattedMessage += `\nWould you like me to search for more specific information or use these findings to create documentation?`;
  }

  return formattedMessage;
}

// Handle OPTIONS for CORS if needed
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
