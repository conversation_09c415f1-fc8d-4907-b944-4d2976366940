"use client";

import React from 'react';
import { 
  FileText, 
  Folder, 
  Calendar,
  ChevronDown,
  ChevronRight
} from 'lucide-react';

// Define the interface for an Agent's output document
interface AgentOutput {
  id: string;
  title: string;
  content: string;
  agentType: string;
  createdAt: string;
  updatedAt?: string;
  metadata?: Record<string, any>;
  category?: string;
  fileUrl?: string;
}

// Define the interface for the document context state
interface DocumentContext {
  documents: AgentOutput[];
  selectedDocuments: string[];
  queuedDocuments: string[];
  isLoading: boolean;
}

// Define the props for the component
interface SelectedDocumentsDisplayProps {
  documentContext: DocumentContext;
  className?: string;
  maxHeight?: string;
}

export default function SelectedDocumentsDisplay({
  documentContext,
  className = '',
  maxHeight = '300px'
}: SelectedDocumentsDisplayProps) {
  const [expandedDocuments, setExpandedDocuments] = React.useState<Set<string>>(new Set());

  // Filter the full document list to get only the selected ones
  const selectedDocuments = documentContext.documents.filter(doc => 
    documentContext.selectedDocuments.includes(doc.id)
  );

  // Function to toggle the expanded state of a single document
  const toggleDocumentExpansion = (documentId: string) => {
    const newExpanded = new Set(expandedDocuments);
    if (newExpanded.has(documentId)) {
      newExpanded.delete(documentId);
    } else {
      newExpanded.add(documentId);
    }
    setExpandedDocuments(newExpanded);
  };

  // Utility function to format date strings
  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
      });
    } catch {
      return 'Unknown date';
    }
  };

  // Utility function to truncate long strings
  const truncateContent = (content: string, maxLength: number = 150) => {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  };

  // Render a placeholder if no documents are selected
  if (selectedDocuments.length === 0) {
    return (
      <div className={`bg-gray-800/50 rounded-lg p-6 flex flex-col ${className}`}>
        <div className="flex items-center space-x-2 mb-3 flex-shrink-0">
          <FileText className="h-5 w-5 text-gray-400" />
          <h4 className="text-lg font-medium text-gray-300">Meeting Context</h4>
        </div>
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <FileText className="h-12 w-12 text-gray-600 mx-auto mb-3" />
            <p className="text-sm text-gray-500 mb-2">No documents selected</p>
            <p className="text-xs text-gray-600">
              Use the Documents panel to select relevant context for this meeting.
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Render the main component with the list of selected documents
  return (
    <div
      className={`bg-gray-800/50 rounded-lg flex flex-col ${className}`}
      style={{
        height: maxHeight === '100%' ? '100%' : 'auto',
        maxHeight: maxHeight !== '100%' ? maxHeight : undefined
      }}
    >
      {/* Fixed Header */}
      <div className="flex items-center justify-between p-6 pb-4 flex-shrink-0 border-b border-gray-600/50">
        <div className="flex items-center space-x-2">
          <FileText className="h-5 w-5 text-purple-400" />
          <h4 className="text-lg font-medium text-white">Meeting Context</h4>
          <span className="bg-purple-500 text-white text-xs rounded-full px-2 py-1">
            {selectedDocuments.length}
          </span>
        </div>
      </div>

      {/* Documents List - Scrollable Container */}
      <div className="flex-1 overflow-hidden px-6">
        <div
          className="overflow-y-auto space-y-3 pr-2 py-4"
          style={{
            height: 'calc(100vh - 400px)', // Fixed height to enable scrolling
            minHeight: '300px',
            maxHeight: '600px',
            scrollbarWidth: 'thin',
            scrollbarColor: '#4B5563 #1F2937',
            overflowAnchor: 'none'
          }}
        >
        {selectedDocuments.map((document) => {
          const isExpanded = expandedDocuments.has(document.id);
          
          return (
            <div
              key={document.id}
              className="border border-gray-600 rounded-lg p-4 bg-gray-700/30 hover:bg-gray-700/50 transition-colors"
            >
              {/* Document Header */}
              <div className="mb-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    {/* Document Title */}
                    <div className="flex items-center space-x-2 mb-2">
                      <FileText className="h-4 w-4 text-blue-400 flex-shrink-0" />
                      <h5 className="text-base font-semibold text-white leading-tight">
                        {document.title}
                      </h5>
                    </div>

                    {/* Category */}
                    {document.category && (
                      <div className="flex items-center space-x-2 mb-2">
                        <Folder className="h-4 w-4 text-purple-400" />
                        <span className="text-2xl font-medium text-purple-300 bg-purple-500/20 px-2 py-1 rounded">
                          {document.category}
                        </span>
                      </div>
                    )}

                    {/* Date */}
                    <div className="flex items-center space-x-2 text-xs text-gray-400">
                      <Calendar className="h-3 w-3" />
                      <span>{formatDate(document.createdAt)}</span>
                    </div>
                  </div>

                  <div className="ml-3 flex-shrink-0">
                    <button
                      onClick={() => toggleDocumentExpansion(document.id)}
                      className="p-1 hover:bg-gray-600 rounded transition-colors"
                    >
                      {isExpanded ? (
                        <ChevronDown className="h-4 w-4 text-gray-400" />
                      ) : (
                        <ChevronRight className="h-4 w-4 text-gray-400" />
                      )}
                    </button>
                  </div>
                </div>

                {/* Document Context Preview - Always visible */}
                <div className="mt-3 p-3 bg-gray-800/50 rounded border-l-2 border-blue-400">
                  <p className="text-sm text-gray-300 leading-relaxed">
                    {document.content ? (
                      truncateContent(document.content, 120)
                    ) : (
                      <span className="italic text-gray-500">No content preview available</span>
                    )}
                  </p>
                </div>
              </div>

              {/* Expanded Content (Additional Details) */}
              {isExpanded && (
                <div className="mt-3 pt-3 border-t border-gray-600">
                  <div className="space-y-3">
                    {/* Full Content */}
                    <div>
                      <h6 className="text-xs font-medium text-gray-400 mb-2 uppercase tracking-wide">Full Content</h6>
                      <div className="text-sm text-gray-300 leading-relaxed bg-gray-800/30 p-3 rounded">
                        {document.content ? (
                          <div className="whitespace-pre-wrap">
                            {truncateContent(document.content, 500)}
                          </div>
                        ) : (
                          <span className="italic text-gray-500">No content available</span>
                        )}
                      </div>
                    </div>

                    {/* Metadata */}
                    <div className="grid grid-cols-2 gap-3 text-xs">
                      <div>
                        <span className="text-gray-400 font-medium">Agent Type:</span>
                        <div className="text-gray-300 mt-1">{document.agentType || 'Unknown'}</div>
                      </div>
                      <div>
                        <span className="text-gray-400 font-medium">Created:</span>
                        <div className="text-gray-300 mt-1">{formatDate(document.createdAt)}</div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          );
        })}

        {/* Summary Footer - Inside scrollable area */}
        {selectedDocuments.length > 0 && (
          <div className=" pt-4 border-t border-gray-600 ">
            <div className="flex items-center justify-between text-xs -mt-3 mb-5">
              <span className="text-gray-500">
                {selectedDocuments.length} document{selectedDocuments.length !== 1 ? 's' : ''} providing context
              </span>
              <span className="text-purple-400 font-medium  ">
                Ready for meeting
              </span>
            </div>
          </div>
        )}
        </div>
      </div>

    </div>
  );
}