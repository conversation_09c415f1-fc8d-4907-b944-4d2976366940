import { NextRequest, NextResponse } from 'next/server';
import { deleteFolderAndChatsByCategory } from '../../../components/DocViewer/deleteDocumentsAndChatsByNamespace ';

export async function POST(req: NextRequest) {
  console.log('[DELETE FOLDER API] Received request');
  let userId: string | undefined;
  let category: string | undefined;
  try {
    const body = await req.json();
    userId = body.userId;
    category = body.category;
    console.log(`[DELETE FOLDER API] Params userId=${userId}, category=${category}`);
    if (!userId || !category) {
      return NextResponse.json({ error: 'Missing required parameters: userId and category' }, { status: 400 });
    }
  } catch (e) {
    console.error('[DELETE FOLDER API] Bad request body', e);
    return NextResponse.json({ error: 'Invalid request body' }, { status: 400 });
  }

  try {
    const summary = await deleteFolderAndChatsByCategory(userId!, category!);
    return NextResponse.json({ success: true, summary }, { status: 200 });
  } catch (e) {
    console.error('[DELETE FOLDER API] Error during deletion', e);
    return NextResponse.json({ success: false, error: 'Error deleting folder and chats' }, { status: 500 });
  }
}

