# ElevenLabs Webhook Configuration Fix

## 🎯 Problem Solved

**Issue**: ElevenLabs webhooks were missing critical `agent_id` and `conversation_id` parameters, causing the save-meeting-summary-webhook to fall back to summary-only PDFs instead of complete conversation transcripts.

**Root Cause**: The webhook configuration in the create-agent API was missing the proper dynamic variable placeholders that tell Eleven<PERSON><PERSON><PERSON> to automatically populate these system variables.

## ✅ Solution Implemented

### 1. Updated Dynamic Variable Configuration

**File**: `app/api/elevenlabs/create-agent/route.ts`

**Before** (Lines 628-636):
```javascript
dynamic_variables: {
  dynamic_variable_placeholders: {
    agent_id: '{{system__agent_id}}',        // ❌ Wrong format
    conversation_id: '{{system__conversation_id}}',  // ❌ Wrong format
    agent_name: '{{system__agent_name}}'     // ❌ Wrong format
  }
}
```

**After** (Lines 628-636):
```javascript
dynamic_variable_placeholders: {
  agent_id: '{{agent_id}}',                 // ✅ Correct format
  conversation_id: '{{conversation_id}}',   // ✅ Correct format
  agent_name: '{{agent_name}}',             // ✅ Correct format
  user_id: '{{user_id}}',                   // ✅ Added
  conversation_history: '{{conversation_history}}'  // ✅ Added
}
```

### 2. Enhanced Request Body Schema

**Added Missing Fields** (Lines 595-622):
```javascript
user_id: {
  type: 'string',
  description: 'The ID of the user in the conversation (automatically populated by ElevenLabs)'
},
conversation_history: {
  type: 'array',
  description: 'The complete conversation history (automatically populated by ElevenLabs)',
  items: {
    type: 'object',
    properties: {
      role: { type: 'string', description: 'The role of the speaker (user or assistant)' },
      content: { type: 'string', description: 'The content of the message' },
      timestamp: { type: 'string', description: 'The timestamp of the message' }
    }
  }
},
conversation_initiation_client_data: {
  type: 'object',
  description: 'Client data passed during conversation initiation (automatically populated by ElevenLabs)'
},
dynamic_variables: {
  type: 'object',
  description: 'Dynamic variables from the conversation (automatically populated by ElevenLabs)'
}
```

## 🔧 How It Works

### Before the Fix
```
ElevenLabs Agent → Webhook Call → Missing agent_id/conversation_id → 
Firestore Query Fails → Falls back to summary-only PDF
```

### After the Fix
```
ElevenLabs Agent → Webhook Call → Includes agent_id/conversation_id → 
Firestore Query Succeeds → Complete conversation PDF generated
```

## 📋 Expected Webhook Payload After Fix

```json
{
  "agent_id": "agent_abc123456789",           // ✅ Now populated
  "conversation_id": "conv_xyz987654321",     // ✅ Now populated
  "user_id": "user_123",                      // ✅ Now populated
  "agent_name": "Marketing Director",         // ✅ Now populated
  "conversation_history": [                   // ✅ Now populated
    {
      "role": "user",
      "content": "Hello, I need help with marketing strategy.",
      "timestamp": "2024-01-15T10:00:00Z"
    },
    {
      "role": "assistant",
      "content": "I'd be happy to help you develop a marketing strategy...",
      "timestamp": "2024-01-15T10:00:05Z"
    }
  ],
  "summary": "User discussed marketing strategy...",
  "generate_document": true,
  "transcript_metadata": {
    "agentType": "Marketing",
    "documentCategory": "PMO - Marketing Strategy"
  }
}
```

## 🚀 Deployment Steps

### 1. Deploy the Updated Code
```bash
# The changes are already made to app/api/elevenlabs/create-agent/route.ts
# Deploy to your production environment
```

### 2. Create New Agent or Update Existing
- **Option A**: Create a new agent using the updated API
- **Option B**: Update existing agent configuration in ElevenLabs dashboard

### 3. Test the Fix
1. Start a voice conversation with your agent
2. Have a meaningful dialogue (3-4 exchanges minimum)
3. Request the agent to save the meeting summary
4. Check server logs for success indicators

## 📊 Success Indicators

### Server Logs - Before Fix
```
❌ ELEVENLABS SYSTEM VARIABLE ANALYSIS: {
  agentIdIsUnknown: true,
  conversationIdIsUnknown: true,
  systemVariableSubstitutionFailed: true
}

❌ TRANSCRIPT SOURCE ANALYSIS: {
  source: "SUMMARY_FALLBACK",
  note: "Using summary because no transcript found"
}
```

### Server Logs - After Fix
```
✅ ELEVENLABS SYSTEM VARIABLE ANALYSIS: {
  agentIdIsUnknown: false,
  conversationIdIsUnknown: false,
  systemVariableSubstitutionFailed: false
}

✅ Retrieved transcript from UI capture with resolved agent ID: {
  foundKey: "agent_abc123456789",
  dialogueLength: 15,
  note: "SUCCESS: Retrieved actual conversation using resolved ElevenLabs agent ID"
}

✅ FINAL TRANSCRIPT SOURCE ANALYSIS: {
  transcriptLength: 15,
  source: "UI_TRANSCRIPT_CAPTURE",
  note: "Using full conversation transcript"
}
```

## 🎯 Expected Results

1. **Complete PDFs**: Generated documents will contain full conversation transcripts
2. **Reliable Retrieval**: Webhook will consistently find stored transcript data
3. **No More Fallbacks**: No more "SUMMARY_FALLBACK" in server logs
4. **Proper Categorization**: Documents will be properly categorized using agent metadata

## 🔗 Related Files

- **Main Fix**: `app/api/elevenlabs/create-agent/route.ts`
- **Webhook Handler**: `app/api/elevenlabs/save-meeting-summary-webhook/route.ts`
- **Transcript Storage**: `app/api/transcript-state/route.ts` (Firebase-based)
- **Test Script**: `scripts/test-webhook-config.js`

## 📝 Notes

- This fix works in conjunction with the Firebase-based transcript state API
- Both fixes are required for complete functionality
- The dynamic variable format `{{variable_name}}` is the correct ElevenLabs syntax
- All existing agents will need to be recreated or updated to use the new configuration
