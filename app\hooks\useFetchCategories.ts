import { useState, useEffect, useRef, useCallback } from 'react';
import { collection, getDocs, query, doc, getDoc } from 'firebase/firestore';
import { db } from 'components/firebase';
import { useSession } from 'next-auth/react';

export interface UseFetchCategoriesResult {
  categories: string[];
  loading: boolean;
  error: Error | null;
  refresh: () => Promise<void>;
  lastFetched: Date | null;
}

/**
 * Custom hook to fetch unique categories from a user's files in Firestore
 *
 * Features:
 * - Automatically fetches categories when session is available
 * - Provides loading and error states
 * - Allows manual refresh
 * - Implements caching to prevent excessive fetches
 * - Handles session availability
 */
function useFetchCategories(cacheDurationMs: number = 5 * 60 * 1000): UseFetchCategoriesResult {
  const [categories, setCategories] = useState<string[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  const [lastFetched, setLastFetched] = useState<Date | null>(null);
  const { data: session } = useSession();

  // Use a ref to track if the component is mounted
  const isMountedRef = useRef<boolean>(true);
  // Cache reference
  const cacheTimeRef = useRef<number>(0);

  // Define the fetch function with useCallback to prevent unnecessary re-renders
  const fetchCategories = useCallback(async (forceRefresh: boolean = false) => {
    // Don't fetch if no session
    if (!session?.user?.email) {
      setError(new Error('User session is not available'));
      return;
    }

    // Check cache unless force refresh is requested
    const now = Date.now();
    if (!forceRefresh && now - cacheTimeRef.current < cacheDurationMs) {
      console.log('Using cached categories data');
      return;
    }

    // Start loading
    setLoading(true);
    setError(null);

    const userId = session.user.email;
    const categoriesSet = new Set<string>();

    try {
      console.log(`Fetching categories for user: ${userId}`);

      // First check if the user document exists
      const userDocRef = doc(db, 'users', userId);
      const userDoc = await getDoc(userDocRef);

      if (!userDoc.exists()) {
        console.warn(`User document not found for ${userId}`);
        // Still proceed to check for files
      }

      // Reference to the user's files collection
      const filesCollectionRef = collection(db, 'users', userId, 'files');
      const q = query(filesCollectionRef);
      const querySnapshot = await getDocs(q);

      if (querySnapshot.empty) {
        console.log(`No files found for user: ${userId}`);
      } else {
        console.log(`Found ${querySnapshot.size} files for user: ${userId}`);

        querySnapshot.forEach((doc) => {
          const fileData = doc.data();
          const category = fileData.category;

          if (category) {
            categoriesSet.add(category);
          }
        });
      }

      // Only update state if the component is still mounted
      if (isMountedRef.current) {
        const categoriesArray = Array.from(categoriesSet);
        setCategories(categoriesArray);
        setLastFetched(new Date());
        cacheTimeRef.current = now;
        console.log(`Fetched ${categoriesArray.length} unique categories`);
      }
    } catch (err) {
      console.error('Error fetching categories:', err);

      // Only update state if the component is still mounted
      if (isMountedRef.current) {
        setError(err instanceof Error ? err : new Error(String(err)));
      }
    } finally {
      // Only update state if the component is still mounted
      if (isMountedRef.current) {
        setLoading(false);
      }
    }
  }, [session, cacheDurationMs]);

  // Fetch categories when the session changes
  useEffect(() => {
    fetchCategories();

    // Cleanup function to prevent memory leaks
    return () => {
      isMountedRef.current = false;
    };
  }, [fetchCategories]);

  // Create a refresh function that forces a refresh
  const refresh = useCallback(async () => {
    await fetchCategories(true);
  }, [fetchCategories]);

  return {
    categories,
    loading,
    error,
    refresh,
    lastFetched
  };
}

export default useFetchCategories;
