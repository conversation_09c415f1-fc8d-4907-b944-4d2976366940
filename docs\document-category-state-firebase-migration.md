# Document Category State API - Firebase Migration

## 🚨 Problem Identified

**Error**: `[CATEGORY_STATE_API] ⚠️ No category state found for user: <EMAIL>`

**Root Cause**: The document category state API was using **in-memory storage** (`Map<string, any>`) which gets cleared in serverless environments and server restarts, causing the webhook to lose access to document category information.

## ✅ Solution Implemented

### Migration from In-Memory to Firebase Firestore

**File**: `app/api/document-category-state/route.ts`

#### Before (In-Memory Storage)
```javascript
// ❌ Volatile storage - lost on server restart
const categoryStateStore = new Map<string, any>();

// Store
categoryStateStore.set(stateKey, categoryState);

// Retrieve  
const categoryState = categoryStateStore.get(userId);

// Delete
const existed = categoryStateStore.delete(userId);
```

#### After (Firebase Firestore)
```javascript
// ✅ Persistent storage - survives server restarts
const CATEGORY_STATE_COLLECTION = 'document_category_sessions';

// Store with multiple keys for reliability
const batch = adminDb.batch();
for (const key of storeKeys) {
  const docRef = adminDb.collection(CATEGORY_STATE_COLLECTION).doc(key);
  batch.set(docRef, categoryState);
}
await batch.commit();

// Retrieve with priority-based lookup
for (const key of lookupKeys) {
  const docSnapshot = await adminDb.collection(CATEGORY_STATE_COLLECTION).doc(key).get();
  if (docSnapshot.exists) {
    categoryState = docSnapshot.data();
    break;
  }
}

// Delete with batch operations
const batch = adminDb.batch();
for (const key of deleteKeys) {
  const docRef = adminDb.collection(CATEGORY_STATE_COLLECTION).doc(key);
  batch.delete(docRef);
}
await batch.commit();
```

## 🔧 Key Improvements

### 1. Multiple Storage Keys
Documents are stored under multiple keys for reliable access:
- **Primary**: `userId` (e.g., "<EMAIL>")
- **Fallback**: `<EMAIL>` (system fallback)
- **Conversation**: `conversationId` (if available)
- **Agent Type**: `agent-type-{agentType}` (if available)

### 2. Priority-Based Retrieval
The API tries multiple lookup strategies:
1. Direct userId lookup
2. Fallback to system user
3. Conversation ID lookup
4. Agent ID lookup

### 3. TTL Support
Added `createdAt: Timestamp.now()` for automatic cleanup via Firebase TTL policies.

### 4. Atomic Operations
Uses Firestore batch operations for consistency:
- **Batch writes** for storing under multiple keys
- **Batch deletes** for cleanup operations

## 📊 Data Structure

### Category State Document
```javascript
{
  userId: "<EMAIL>",
  selectedDocuments: [
    {
      id: "doc-123",
      title: "Marketing Strategy 2024", 
      category: "PMO - Marketing Strategy"
    }
  ],
  primaryCategory: "PMO - Marketing Strategy",
  conversationId: "conv_abc123",
  agentType: "Marketing",
  timestamp: 1705123456789,
  createdAt: Timestamp.now(),
  storedAt: "2024-01-13T10:30:56.789Z"
}
```

### Storage Keys
```javascript
// Document stored under these keys:
[
  "<EMAIL>",           // Primary user key
  "<EMAIL>",           // Fallback key (same in this case)
  "conv_abc123",                   // Conversation key
  "agent-type-Marketing"           // Agent type key
]
```

## 🔄 API Endpoints

### POST `/api/document-category-state`
**Purpose**: Store document category selection from UI

**Request Body**:
```json
{
  "userId": "<EMAIL>",
  "selectedDocuments": [...],
  "primaryCategory": "PMO - Marketing Strategy",
  "conversationId": "conv_abc123",
  "agentType": "Marketing",
  "timestamp": 1705123456789
}
```

**Response**:
```json
{
  "success": true,
  "message": "Category state stored successfully",
  "primaryCategory": "PMO - Marketing Strategy"
}
```

### GET `/api/document-category-state`
**Purpose**: Retrieve category state for webhooks

**Query Parameters**:
- `userId` (required): User identifier
- `conversationId` (optional): Conversation identifier  
- `agentId` (optional): Agent identifier

**Response**:
```json
{
  "success": true,
  "categoryState": {
    "primaryCategory": "PMO - Marketing Strategy",
    "selectedDocuments": [...],
    "agentType": "Marketing",
    "conversationId": "conv_abc123",
    "timestamp": 1705123456789
  }
}
```

### DELETE `/api/document-category-state`
**Purpose**: Clean up category state

**Request Body**:
```json
{
  "userId": "<EMAIL>"
}
```

## 🎯 Expected Results

### Before Fix
```
❌ [CATEGORY_STATE_API] ⚠️ No category state found for user: <EMAIL>
❌ Webhook cannot determine document category
❌ Documents saved without proper categorization
❌ State lost on server restart
```

### After Fix
```
✅ [CATEGORY_STATE_API] ✅ Retrieved category state: {
  userId: "<EMAIL>",
  foundKey: "<EMAIL>", 
  primaryCategory: "PMO - Marketing Strategy",
  selectedDocumentsCount: 2,
  agentType: "Marketing"
}
✅ Webhook receives proper document category
✅ Documents saved with correct categorization
✅ State persists across server restarts
```

## 🚀 Deployment Steps

1. **Deploy Updated API**: The changes to `app/api/document-category-state/route.ts` need to be deployed
2. **Configure Firebase TTL**: Set up TTL policy for `document_category_sessions` collection
3. **Test Category Selection**: Verify UI properly stores category state
4. **Test Webhook Retrieval**: Confirm webhooks can retrieve category information

## 🔗 Related Components

### UI Components That Store Category State
- `hooks/useDocumentCategoryCapture.ts` - Captures category selection
- `components/DocViewer/SelectedDocumentsDisplay.tsx` - Document selection UI

### Webhooks That Retrieve Category State  
- `app/api/elevenlabs/save-meeting-summary-webhook/route.ts` - Meeting summary generation
- Any other webhooks that need document categorization

## 📝 Firebase TTL Configuration

**Collection**: `document_category_sessions`
**TTL Field**: `createdAt`
**Duration**: 1 day (24 hours)

This ensures automatic cleanup of old category state data to prevent database bloat.

## 🧪 Testing

Use the test script to verify the migration:
```bash
node scripts/test-document-category-state.js
```

The test covers:
- ✅ Storing category state
- ✅ Retrieving by userId
- ✅ Retrieving by conversationId  
- ✅ Fallback retrieval mechanisms
- ✅ Cleanup operations

This migration resolves the "No category state found" warnings and ensures reliable document categorization in serverless environments.
