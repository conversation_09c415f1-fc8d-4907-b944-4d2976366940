/**
 * Test script to validate PDF generation improvements via API
 */

const testContent = `
# Metadata Generation Process for Custom RAG Pipeline

## Overview

This document demonstrates the **improved PDF generation** with proper formatting.

## Metadata Fields Generated for Each Code Chunk

### 1. Core File Metadata

The following code block should now render properly:

\`\`\`javascript
{
  filePath: string,      // Full path to the source file
  fileName: string,      // Just relative to project root
  fileExtension: string, // Name of the file
  fileCreation: string,  // File extension (.js, .ts, .py)
  
  // Content metadata
  chunkIndex: number,    // Sequential index of chunk within file
  lineNumbers: {
    start: number,       // Starting line number in original file
    end: number         // Ending line number in original file
  },
  
  // Contextual metadata
  functionName: string,  // Name of the relevant context
  className: string     // Name of the relevant context
}
\`\`\`

### 2. **AI-Generated Enrichment Metadata**

The system uses Google's Gemini AI (\`analyzeFileWithLLM\` function) to generate intelligent metadata:

\`\`\`typescript
interface EnrichmentMetadata {
  summary: string;
  purpose: string;
  keyFeatures: string[];
  dependencies: string[];
  complexity: 'low' | 'medium' | 'high';
}
\`\`\`

## Storage Architecture Mapping

### Pinecone Vector Storage

The vector embeddings are stored in Pinecone with the following structure:

\`\`\`json
{
  "id": "unique-chunk-id",
  "values": [0.1, 0.2, 0.3, ...],
  "metadata": {
    "filePath": "/path/to/file.ts",
    "chunkIndex": 0,
    "summary": "Function that handles user authentication"
  }
}
\`\`\`

### Data Flow and Synchronization

1. **Indexing Process Flow**

The process follows these steps:
- Scan project files and directories
- Extract code chunks using intelligent splitting
- Generate embeddings using OpenAI API
- Store in both Pinecone and Firestore

2. **Storage Synchronization**

The system maintains consistency between Pinecone and Firestore:
- Pinecone stores the vector ID as a reference to the Firestore document
- Firestore stores the complete metadata including the Pinecone vector ID
- Both systems use the same unique chunk ID for cross-referencing

## Key Implementation Details

### Chunk Processing

\`\`\`python
def process_code_chunk(chunk_content, file_metadata):
    """
    Process a single code chunk for indexing
    """
    # Generate embeddings
    embeddings = openai_client.embeddings.create(
        model="text-embedding-ada-002",
        input=chunk_content
    )
    
    # Analyze with LLM
    analysis = gemini_client.analyze_code(chunk_content)
    
    return {
        'embeddings': embeddings.data[0].embedding,
        'metadata': {**file_metadata, **analysis}
    }
\`\`\`

This architecture enables powerful semantic search capabilities while maintaining detailed metadata for code intelligence.
`;

async function testPdfViaAPI() {
  try {
    console.log('🧪 Testing improved PDF generation via API...');
    
    const response = await fetch('http://localhost:3000/api/generate-pdf', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        title: 'PDF Generation Test - Improved Formatting',
        content: testContent,
        documentType: 'test-document',
        timestamp: new Date().toISOString(),
        modelInfo: {
          provider: 'PDF Improvement Test'
        }
      }),
    });

    if (response.ok) {
      const blob = await response.blob();
      const buffer = Buffer.from(await blob.arrayBuffer());
      
      const filename = `test-pdf-improvements-${Date.now()}.pdf`;
      require('fs').writeFileSync(filename, buffer);
      
      console.log(`✅ Test PDF generated successfully: ${filename}`);
      console.log('📋 Test includes:');
      console.log('   - Code blocks with proper syntax highlighting');
      console.log('   - HTML formatting (bold, italic, inline code)');
      console.log('   - Section page breaks');
      console.log('   - Optimized font sizing');
      console.log('   - Page numbers');
      console.log('');
      console.log('🔍 Please review the generated PDF to verify improvements.');
    } else {
      console.error('❌ API request failed:', response.status, response.statusText);
    }
    
  } catch (error) {
    console.error('❌ Error testing PDF generation:', error.message);
    console.log('💡 Make sure the development server is running on localhost:3000');
  }
}

// Run the test
testPdfViaAPI();
