"use client";

import React from 'react';
import { useToast } from './use-toast';
import { X, CheckCircle, AlertCircle, Info } from 'lucide-react';
import { Button } from './button';

export function Toaster() {
  const { toasts, dismiss } = useToast();

  if (toasts.length === 0) return null;

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2 max-w-sm">
      {toasts.map((toast) => (
        <Toast key={toast.id} {...toast} onDismiss={() => dismiss(toast.id)} />
      ))}
    </div>
  );
}

interface ToastProps {
  id: string;
  title?: string;
  description?: string;
  variant?: 'default' | 'success' | 'warning' | 'destructive';
  visible: boolean;
  onDismiss: () => void;
  action?: { label: string; onClick: () => void };
}

function Toast({ title, description, variant = 'default', visible, onDismiss, action }: ToastProps) {
  const getIcon = () => {
    switch (variant) {
      case 'destructive':
        return <AlertCircle className="h-4 w-4 text-red-300" />;
      case 'warning':
        return <AlertCircle className="h-4 w-4 text-amber-300" />;
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-300" />;
      default:
        return <Info className="h-4 w-4 text-gray-300" />;
    }
  };

  const getStyles = () => {
    switch (variant) {
      case 'destructive':
        return 'bg-red-900/90 border-red-700 text-red-100';
      case 'warning':
        return 'bg-amber-900/90 border-amber-700 text-amber-100';
      case 'success':
        return 'bg-green-900/90 border-green-700 text-green-100';
      default:
        return 'bg-gray-800/90 border-gray-700 text-gray-100';
    }
  };

  return (
    <div
      className={`
        relative flex items-start space-x-3 p-4 rounded-lg border backdrop-blur-sm shadow-lg
        ${visible ? 'animate-in slide-in-from-right-full duration-300' : 'animate-out fade-out slide-out-to-right-full duration-200'}
        ${getStyles()}
      `}
    >
      <div className="flex-shrink-0 mt-0.5">
        {getIcon()}
      </div>

      <div className="flex-1 min-w-0">
        {title && (
          <div className="text-sm font-medium">
            {title}
          </div>
        )}
        {description && (
          <div className="text-sm opacity-90 mt-1">
            {description}
          </div>
        )}
      </div>

      {action && (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => { action.onClick(); onDismiss(); }}
          className="mr-1 flex-shrink-0 h-6 px-2 text-xs text-white/90 hover:text-white border border-white/10"
        >
          {action.label}
        </Button>
      )}

      <Button
        variant="ghost"
        size="sm"
        onClick={onDismiss}
        className="flex-shrink-0 h-6 w-6 p-0 text-gray-300 hover:text-white"
      >
        <X className="h-3 w-3" />
      </Button>
    </div>
  );
}
