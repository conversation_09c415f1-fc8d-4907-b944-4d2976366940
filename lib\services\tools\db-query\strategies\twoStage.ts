import type { QueryCodebaseRequest, StrategyRun } from '../types';
import { OpenAIEmbeddings } from '@langchain/openai';
import { Pinecone } from '@pinecone-database/pinecone';
import { FirestoreStore } from 'lib/FirestoreStore';
import { adminDb } from 'components/firebase-admin';
import { fetchDocumentChunksByChunkIds } from 'lib/fetchDocumentChunksByChunkIds';

export const TwoStageStrategyId = 'two-stage' as const;

async function getNamespacesForCategory(userId: string, category: string): Promise<string[]> {
  try {
    const snap = await adminDb.collection('users').doc(userId).collection('files').where('category', '==', category).get();
    return snap.docs.map(d => (d.data() as any).namespace).filter(Boolean);
  } catch { return []; }
}

function regexFromPattern(filePath?: string | { pattern: string; flags?: string }) {
  if (!filePath) return undefined;
  if (typeof filePath === 'string') return new RegExp(filePath, 'i');
  return new RegExp(filePath.pattern, filePath.flags || 'i');
}

export async function executeTwoStage(req: QueryCodebaseRequest): Promise<StrategyRun> {
  const t0 = Date.now();
  const { userId, category, query, namespaces: explicitNamespaces, filters, topKPerNamespace = 5 } = req;

  const namespaces = explicitNamespaces?.length ? explicitNamespaces : await getNamespacesForCategory(userId, category);
  const store = new FirestoreStore({ collectionPath: `users/${userId}/byteStoreCollection` });

  // Stage 1: Firestore prefilter to produce candidate chunkIds
  const preStart = Date.now();
  let candidateIds: string[] = [];
  try {
    const col = (store as any).collectionRef;
    let q: FirebaseFirestore.Query = col;

    if (filters?.indexingSessionId) q = q.where('metadata.indexingSessionId', '==', filters.indexingSessionId);
    if (filters?.processed_at?.gte) q = q.where('metadata.processed_at', '>=', filters.processed_at.gte);
    if (filters?.processed_at?.lte) q = q.where('metadata.processed_at', '<=', filters.processed_at.lte);
    if (filters?.codeEntityType) q = q.where('metadata.codeEntityType', 'in', Array.isArray(filters.codeEntityType) ? filters.codeEntityType : [filters.codeEntityType]);

    const snap = await q.limit(1000).get();
    candidateIds = snap.docs.map(d => d.id);
    const rx = regexFromPattern(filters?.filePath);
    if (rx) candidateIds = candidateIds.filter(id => rx.test((snap.docs.find(s => s.id === id)?.data()?.metadata?.filePath) || ''));
  } catch (e) {
    // best-effort prefilter
  }
  const prefilterMs = Date.now() - preStart;

  // Stage 2: Vector refine per-namespace
  const embedder = new OpenAIEmbeddings();
  const vector = await embedder.embedQuery(query);
  const pinecone = new Pinecone();
  const index = pinecone.Index(process.env.PINECONE_INDEX!);

  const allMatches: any[] = [];
  for (const ns of namespaces) {
    try {
      const filter = candidateIds.length ? { chunkId: { $in: candidateIds } } : undefined;
      const res = await index.namespace(ns).query({ vector, topK: topKPerNamespace, includeMetadata: true, includeValues: false, ...(filter ? { filter } : {}) });
      allMatches.push(...(res?.matches || []).map((m: any) => ({ ...m, _namespace: ns })));
    } catch {}
  }

  const ids = Array.from(new Set(allMatches.map(m => (m?.metadata?.chunkId || m?.metadata?.chunk_id || `${m._namespace}_${m.id}`))));
  const docs = await fetchDocumentChunksByChunkIds(ids, store);
  const docMap = new Map(docs.map(d => [String(d.metadata?.chunk_id || d.metadata?.chunkId), d]));

  const results = allMatches.map(m => {
    const chunkId = String(m?.metadata?.chunkId || m?.metadata?.chunk_id || `${m._namespace}_${m.id}`);
    const doc = docMap.get(chunkId);
    return {
      chunkId,
      namespace: m._namespace,
      score: m.score || 0,
      content: doc?.pageContent || '',
      pineconeMetadata: m.metadata || {},
      firestoreMetadata: doc?.metadata || {}
    };
  }).filter(r => r.content);

  // Mandatory LLM synthesis over results
  let llmAnalysis = '';
  let llmModel = '';
  let llmMs = 0;
  if (results.length) {
    const { synthesizeAnswerOverResults } = await import('lib/tools/synthesizeAnswerOverResults');
    const synth = await synthesizeAnswerOverResults(query, category, results, { provider: req.modelProvider, model: req.modelName });
    llmAnalysis = synth.llmAnalysis;
    llmModel = synth.llmModel;
    llmMs = synth.timingMs;
  }

  const durationMs = Date.now() - t0;
  return {
    strategyId: 'two-stage',
    response: {
      success: true,
      results,
      llmAnalysis,
      llmModel,
      stats: { namespacesQueried: namespaces, topKPerNamespace, totalMatches: results.length, timingsMs: { prefilterMs, llm: llmMs, total: durationMs }, filtersApplied: filters }
    },
    metrics: { durationMs, prefilterMs, namespacesCount: namespaces.length, candidateCount: results.length },
    quality: {
      avgScore: results.length ? results.reduce((s, r) => s + (r.score || 0), 0) / results.length : 0,
      diversity: new Set(results.map(r => r.firestoreMetadata?.filePath || r.pineconeMetadata?.filePath)).size / Math.max(1, results.length),
      coverage: (namespaces.length ? new Set(results.map(r => r.namespace)).size / namespaces.length : 0)
    }
  };
}

