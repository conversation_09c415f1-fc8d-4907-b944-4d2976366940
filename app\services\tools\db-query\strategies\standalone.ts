import type { QueryCodebaseRequest, StrategyRun } from '../types';
import { queryCodebaseStorage } from '../../../../../lib/tools/queryCodebaseStorage';

export const StandaloneStrategyId = 'standalone' as const;

export async function executeStandalone(req: QueryCodebaseRequest): Promise<StrategyRun> {
  const start = Date.now();
  const response = await queryCodebaseStorage(req);
  const durationMs = Date.now() - start;

  const avgScore = response.results.length
    ? response.results.reduce((s, r) => s + (r.score ?? 0), 0) / response.results.length
    : 0;

  const paths = new Set(
    response.results.map(r => r.firestoreMetadata?.filePath || r.pineconeMetadata?.filePath).filter(Boolean)
  );

  return {
    strategyId: StandaloneStrategyId,
    response,
    metrics: {
      durationMs,
      namespacesCount: response.stats.namespacesQueried.length,
      candidateCount: response.results.length,
      timingsMs: response.stats.timingsMs
    },
    quality: {
      avgScore,
      diversity: paths.size / Math.max(1, response.results.length),
      coverage: (response.stats.namespacesQueried.length
        ? new Set(response.results.map(r => r.namespace)).size / response.stats.namespacesQueried.length
        : 0)
    }
  };
}

