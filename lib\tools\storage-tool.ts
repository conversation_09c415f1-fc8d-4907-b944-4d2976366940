import { db } from "../../components/firebase/config";
import { collection, addDoc, updateDoc, doc, getDoc, serverTimestamp, setDoc, writeBatch } from "firebase/firestore";
import { getStorage, ref, uploadBytes, getDownloadURL } from "firebase/storage";
import { v4 as uuidv4 } from 'uuid';
import { RecursiveCharacterTextSplitter } from 'langchain/text_splitter';
import { OpenAIEmbeddings } from '@langchain/openai';
import { Pinecone } from '@pinecone-database/pinecone';
import { vectorEmbeddingTool } from './vector-embeddings';

import { CodebaseIndexingReport, StoredCodebaseIndexingReport } from "../interfaces/CodebaseIndexingReport";

// Define interfaces for storage tool
export interface FileObject {
  data: Buffer | Blob | Uint8Array;
  name: string;
  path: string;
  metadata?: Record<string, any>;
}

export interface SaveContentWithFilesResult {
  docId: string;
  fileUrls: Record<string, string>;
}

export interface SaveContentChunksResult {
  parentId: string;
  chunkIds: string[];
  chunkCount: number;
}

export interface SavePdfToByteStoreResult {
  documentId: string;
  downloadUrl: string;
  totalChunks: number;
  success: boolean;
}

/**
 * Storage Tool for saving content and files to Firebase
 */
// Constants for text chunking
const CHUNK_SIZE = 1000;
const CHUNK_OVERLAP = 200;

// Helper function to clean metadata
function cleanMetadata(metadata: Record<string, any>): Record<string, any> {
  const cleaned: Record<string, any> = {};

  for (const [key, value] of Object.entries(metadata)) {
    if (value !== undefined && value !== null) {
      cleaned[key] = value;
    } else {
      cleaned[key] = '';
    }
  }

  return cleaned;
}


// Pinecone metadata must be only strings, numbers, booleans or list of strings.
// This helper selects a very small, safe subset from arbitrary metadata.
function pineconeSafeMetadata(metadata?: Record<string, any>): Record<string, any> {
  const m = metadata || {};
  const out: Record<string, any> = {};
  const setIf = (k: string, v: any) => {
    if (v !== undefined && v !== null) out[k] = Array.isArray(v) ? undefined : v;
  };
  // Scalars only
  setIf('projectName', typeof m.projectName === 'string' ? m.projectName : undefined);
  setIf('reportId', typeof m.reportId === 'string' ? m.reportId : undefined);
  setIf('reportType', typeof m.reportType === 'string' ? m.reportType : undefined);
  setIf('generatedBy', typeof m.generatedBy === 'string' ? m.generatedBy : undefined);
  setIf('totalFiles', typeof m.totalFiles === 'number' ? m.totalFiles : undefined);
  setIf('totalChunks', typeof m.totalChunks === 'number' ? m.totalChunks : undefined);
  setIf('processingTimeMs', typeof m.processingTimeMs === 'number' ? m.processingTimeMs : undefined);
  setIf('createdAt', typeof m.createdAt === 'string' ? m.createdAt : undefined);
  setIf('completedAt', typeof m.completedAt === 'string' ? m.completedAt : undefined);
  // Derive counts for arrays (do not include arrays themselves)
  if (Array.isArray(m.selectedPaths)) out['selectedPathsCount'] = m.selectedPaths.length;
  // Never include heavy objects/arrays like fileAnalysisCondensed in Pinecone metadata
  return out;
}

export class StorageTool {
  /**
   * Save content to Firestore
   * @param data - The data to save
   * @param collectionName - The collection to save to
   * @returns The document ID
   */
  async saveToFirestore(data: Record<string, any>, collectionName = "documents"): Promise<string> {
    try {
      // Add timestamp
      const dataWithTimestamp = {
        ...data,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      };

      // Save to Firestore
      const docRef = await addDoc(collection(db, collectionName), dataWithTimestamp);

      return docRef.id;
    } catch (error) {
      console.error("Error saving to Firestore:", error);
      throw error;
    }
  }

  /**
   * Update content in Firestore
   * @param docId - The document ID to update
   * @param data - The data to update
   * @param collectionName - The collection containing the document
   * @returns Promise<void>
   */
  async updateInFirestore(docId: string, data: Record<string, any>, collectionName = "documents"): Promise<void> {
    try {
      // Add timestamp
      const dataWithTimestamp = {
        ...data,
        updatedAt: serverTimestamp()
      };

      // Update in Firestore
      const docRef = doc(db, collectionName, docId);
      await updateDoc(docRef, dataWithTimestamp);
    } catch (error) {
      console.error("Error updating in Firestore:", error);
      throw error;
    }
  }

  /**
   * Save a file to Firebase Storage
   * @param fileData - The file data to save
   * @param path - The storage path
   * @param fileName - The file name
   * @param metadata - File metadata
   * @returns The download URL
   */
  async saveToStorage(
    fileData: Buffer | Blob | Uint8Array,
    path: string,
    fileName: string,
    metadata: Record<string, any> = {}
  ): Promise<string> {
    try {
      const storage = getStorage();
      const fullPath = `${path}/${fileName}`;
      const storageRef = ref(storage, fullPath);

      // Upload file
      await uploadBytes(storageRef, fileData, metadata);

      // Get download URL
      const downloadURL = await getDownloadURL(storageRef);

      return downloadURL;
    } catch (error) {
      console.error("Error saving to Storage:", error);
      throw error;
    }
  }

  /**
   * Save content and related files
   * @param content - The content to save
   * @param files - Array of file objects with data, name, and path
   * @param collectionName - The collection to save content to
   * @returns Result with document ID and file URLs
   */
  async saveContentWithFiles(
    content: Record<string, any>,
    files: FileObject[] = [],
    collectionName = "documents"
  ): Promise<SaveContentWithFilesResult> {
    try {
      // First save the content to get a document ID
      const docId = await this.saveToFirestore(content, collectionName);

      // Save each file and collect URLs
      const fileUrls: Record<string, string> = {};

      for (const file of files) {
        if (!file.data || !file.name || !file.path) {
          console.warn("Skipping invalid file:", file);
          continue;
        }

        // Use document ID in the path for organization
        const storagePath = `${file.path}/${docId}`;
        const downloadUrl = await this.saveToStorage(
          file.data,
          storagePath,
          file.name,
          file.metadata || {}
        );

        fileUrls[file.name] = downloadUrl;
      }

      // Update the document with file URLs if any were saved
      if (Object.keys(fileUrls).length > 0) {
        await this.updateInFirestore(
          docId,
          { fileUrls },
          collectionName
        );
      }

      return {
        docId,
        fileUrls
      };
    } catch (error) {
      console.error("Error saving content with files:", error);
      throw error;
    }
  }

  /**
   * Split content into chunks and save to Firestore
   * @param content - The content to split and save
   * @param metadata - Metadata for the content
   * @param chunkSize - Maximum size of each chunk
   * @param collectionName - The collection to save chunks to
   * @returns Result with parent document ID and chunk IDs
   */
  async saveContentChunks(
    content: string,
    metadata: Record<string, any> = {},
    chunkSize = 1000,
    collectionName = "content_chunks"
  ): Promise<SaveContentChunksResult> {
    try {
      if (!content) {
        throw new Error("Content is required");
      }

      // Create parent document
      const parentData = {
        ...metadata,
        contentLength: content.length,
        chunkCount: 0,
        chunkSize,
        isParent: true
      };

      const parentId = await this.saveToFirestore(parentData, collectionName);

      // Split content into chunks
      const chunks = this.splitContentIntoChunks(content, chunkSize);
      const chunkIds: string[] = [];

      // Save each chunk
      for (let i = 0; i < chunks.length; i++) {
        const chunkData = {
          content: chunks[i],
          parentId,
          chunkIndex: i,
          ...metadata,
          isChunk: true
        };

        const chunkId = await this.saveToFirestore(chunkData, collectionName);
        chunkIds.push(chunkId);
      }

      // Update parent with chunk count and IDs
      await this.updateInFirestore(
        parentId,
        {
          chunkCount: chunks.length,
          chunkIds
        },
        collectionName
      );

      return {
        parentId,
        chunkIds,
        chunkCount: chunks.length
      };
    } catch (error) {
      console.error("Error saving content chunks:", error);
      throw error;
    }
  }

  /**
   * Split content into chunks
   * @param content - The content to split
   * @param chunkSize - Maximum size of each chunk
   * @returns Array of content chunks
   * @private
   */
  private splitContentIntoChunks(content: string, chunkSize = 1000): string[] {
    if (!content || typeof content !== "string") {
      return [];
    }

    // Simple paragraph-based splitting
    const paragraphs = content.split("\n\n");
    const chunks: string[] = [];
    let currentChunk = "";

    for (const paragraph of paragraphs) {
      if (currentChunk.length + paragraph.length > chunkSize) {
        // Add current chunk to results
        chunks.push(currentChunk.trim());

        // Start new chunk
        currentChunk = paragraph;
      } else {
        // Add paragraph to current chunk
        currentChunk += (currentChunk ? "\n\n" : "") + paragraph;
      }
    }

    // Add the last chunk if not empty
    if (currentChunk.trim()) {
      chunks.push(currentChunk.trim());
    }

    return chunks;
  }

  /**
   * Save a PDF to byteStoreCollection and files collection
   * @param pdfBuffer - The PDF buffer to save
   * @param title - The title of the PDF
   * @param content - The text content of the PDF
   * @param category - The category of the PDF (defaults to 'Marketing Agent Team')
   * @param metadata - Additional metadata for the PDF
   * @returns Promise<SavePdfToByteStoreResult> - Result of the operation
   */

  async savePdfToByteStore(
    pdfBuffer: Buffer,
    title: string,
    content: string,
    category: string = 'Marketing Agent Team',
    metadata: Record<string, any> = {},
    agentType: string = 'MarketingAgent',
    onProgress?: (u: { step: string; message: string; percent?: number; batchIndex?: number; totalBatches?: number }) => void
  ): Promise<SavePdfToByteStoreResult> {
    try {
      // Generate a unique ID using UUIDv4
      const documentId = uuidv4();
      const fileName = `${title.replace(/[^a-zA-Z0-9]/g, '_')}_${documentId}.pdf`;
      const sysAdmin = process.env.SYS_ADMIN || '<EMAIL>';
      // IMPORTANT: Use the logged-in SYS_ADMIN user consistently for both files and byteStore
      const byteStoreCollection = `users/${sysAdmin}/byteStoreCollection`;

      // 1. Upload PDF to Firebase Storage
      const storagePath = `${agentType ? agentType.toLowerCase() : 'marketing'}_pdfs/${fileName}`;
      const storage = getStorage();
      const storageRef = ref(storage, storagePath);

      await uploadBytes(storageRef, pdfBuffer, {
        contentType: 'application/pdf',
        customMetadata: {
          title,
          category,
          generatedBy: agentType || 'MarketingAgent'
        }
      });

      // Get download URL
      const downloadUrl = await getDownloadURL(storageRef);

      // 2. Store metadata in files collection for the same sysAdmin user
      const progress = (msg: string, data?: any, evt?: { step: string; message: string; percent?: number; batchIndex?: number; totalBatches?: number }) => {
        try { console.log(`[PROGRESS] ${msg}`, data ? JSON.stringify(data) : ''); } catch {}
        try { onProgress && onProgress(evt || { step: 'info', message: msg }); } catch {}
      };
      progress('Uploading PDF to storage...', undefined, { step: 'upload', message: 'Uploading PDF to storage...' });
      const filesCollection = `users/${sysAdmin}/files`;
      // Prepare document data, filtering out undefined values
      const documentData: Record<string, any> = {
        category: (category || '').trim(),
        createdAt: new Date().toISOString(),
        downloadUrl,
        name: fileName,
        namespace: documentId,
        ref: storagePath,
        size: pdfBuffer.length,
        type: 'application/pdf',
        title,
        generatedBy: agentType || 'MarketingAgent',
        ...metadata
      };

      // Only include reportType if it has a valid value
      if (metadata?.reportType && metadata.reportType !== undefined) {
        documentData.reportType = metadata.reportType;
      }

      // Filter out any undefined values from the spread metadata
      Object.keys(documentData).forEach(key => {
        if (documentData[key] === undefined) {
          delete documentData[key];
        }
      });

      await setDoc(doc(db, filesCollection, documentId), documentData);

      progress('Generating text chunks...', undefined, { step: 'chunking', message: 'Chunking text...' });
      // 3. Process content for byteStoreCollection
      progress('Saving file metadata...');
      // Initialize the text splitter for chunking
      const textSplitter = new RecursiveCharacterTextSplitter({
        chunkSize: CHUNK_SIZE,
        chunkOverlap: CHUNK_OVERLAP,
        separators: ["\n\n", "\n", ". ", " ", ""]
      });

      // Split the content into chunks
      const textChunks = await textSplitter.createDocuments([content]);

      // Initialize OpenAI embeddings
      const embeddings = new OpenAIEmbeddings({
        apiKey: process.env.OPENAI_API_KEY,
      });

      // Initialize Pinecone index
      const pinecone = new Pinecone();
      const pineconeIndex = pinecone.Index(process.env.PINECONE_INDEX || 'ikedia');

      const t0 = Date.now();
      console.log(`[TIMING] Starting chunking for document ${documentId} ...`);

      const chunkingStart = Date.now();
      // Prepare chunk ids and metadata first
      const chunkIds: string[] = [];
      const pineconeMetas: any[] = [];
      const contents: string[] = [];

      for (let i = 0; i < textChunks.length; i++) {
        const chunk = textChunks[i];
        const chunkId = `${documentId}_${i + 1}`;
        chunkIds.push(chunkId);
        contents.push(chunk.pageContent);
        const chunkMetadata = cleanMetadata({
          doc_id: documentId,
          chunk_id: chunkId,
          chunkId: chunkId,
          namespace: documentId,
          document_title: title,
          category: (category || '').trim(),
          file_type: 'application/pdf',
          position: i + 1,
          total_chunks: textChunks.length,
          is_summary: i === 0,
          source_url: downloadUrl,
          processed_at: new Date().toISOString(),
          ...pineconeSafeMetadata(metadata)
        } as any);
        pineconeMetas.push(chunkMetadata);
      }
      console.log(`[TIMING] Text chunking prepared ${textChunks.length} chunks in ${Date.now() - chunkingStart}ms`);

      // Firestore writes (raw chunks) - batch writes in groups to reduce round trips
      const writeStart = Date.now();
      const batchWriteSize = 400; // safe under Firestore 500 op limit
      for (let i = 0; i < textChunks.length; i += batchWriteSize) {
        const batchEnd = Math.min(i + batchWriteSize, textChunks.length);
        const batch = writeBatch(db);
        for (let j = i; j < batchEnd; j++) {
          batch.set(doc(db, byteStoreCollection, chunkIds[j]), {
            content: contents[j],
            metadata: pineconeMetas[j]
          });
        }
        await batch.commit();
        progress(`Wrote chunk batch ${Math.floor(i / batchWriteSize) + 1}/${Math.ceil(textChunks.length / batchWriteSize)}`);
      }
      console.log(`[TIMING] Firestore write of ${textChunks.length} chunks completed in ${Date.now() - writeStart}ms`);

      // Embeddings in batches
      const embedBatchSize = 64; // 32-64 recommended
      const allEmbeddings: number[][] = [];
      onProgress && onProgress({ step: 'embedding', message: 'Generating embeddings...', batchIndex: 1, totalBatches: Math.ceil(contents.length / embedBatchSize) });
      const totalEmbedBatches = Math.ceil(contents.length / embedBatchSize);
      for (let b = 0; b < contents.length; b += embedBatchSize) {
        const batchIndex = Math.floor(b / embedBatchSize) + 1;
        progress(`Generating embeddings in ${totalEmbedBatches} batch(es)...`);
        const embedStart = Date.now();
        const batchDocs = contents.slice(b, b + embedBatchSize);
        const vectors = await embeddings.embedDocuments(batchDocs);
        onProgress && onProgress({ step: 'embedding', message: `Embedding batch ${batchIndex}/${totalEmbedBatches}`, batchIndex, totalBatches: totalEmbedBatches });
        allEmbeddings.push(...vectors);
        console.log(`[TIMING] Embedding batch ${batchIndex}/${totalEmbedBatches} completed in ${Date.now() - embedStart}ms`);
      }
      // Pinecone upserts in batches
      const upsertBatchSize = 100; // 50-100 recommended
      const totalUpsertBatches = Math.ceil(allEmbeddings.length / upsertBatchSize);
      for (let b = 0; b < allEmbeddings.length; b += upsertBatchSize) {
        const batchIndex = Math.floor(b / upsertBatchSize) + 1;
        const upsertStart = Date.now();
        const vectorBatch = [] as any[];
        for (let j = b; j < Math.min(b + upsertBatchSize, allEmbeddings.length); j++) {
          vectorBatch.push({ id: chunkIds[j], values: allEmbeddings[j], metadata: pineconeMetas[j] });
          onProgress && onProgress({ step: 'upsert', message: `Upsert batch ${batchIndex}/${totalUpsertBatches}`, batchIndex, totalBatches: totalUpsertBatches });
        }
        await pineconeIndex.namespace(documentId).upsert(vectorBatch);
        console.log(`[TIMING] Pinecone upsert batch ${batchIndex}/${totalUpsertBatches} completed in ${Date.now() - upsertStart}ms`);
      }

      console.log(`[TIMING] Total processing for ${documentId} completed in ${Date.now() - t0}ms`);

      return {
        documentId,
        downloadUrl,
        totalChunks: textChunks.length,
        success: true
      };
    } catch (error) {
      console.error('Error saving PDF to byteStore:', error);
      throw error;
    }
  }
  /**
   * Save a codebase indexing report PDF to byteStoreCollection and files collection
      const progress = (msg: string, data?: any) => {
        try { console.log(`[PROGRESS] ${msg}`, data ? JSON.stringify(data) : ''); } catch {}
      };
      progress('Uploading PDF to storage...');
   * @param pdfBuffer - The PDF buffer to save
   * @param title - The title of the PDF
   * @param content - The text content of the PDF
   * @param category - The category of the PDF
   * @param userId - The user ID for storage location
   * @param documentId - The document ID to use (from indexing session)
   * @param metadata - Additional metadata for the PDF
   * @returns Promise<SavePdfToByteStoreResult> - Result of the operation
   */
  async saveCodebaseReportToByteStore(
    pdfBuffer: Buffer,
    title: string,
    content: string,
    category: string,
    userId: string,
    documentId: string,
    metadata: Record<string, any> = {},
    onProgress?: (u: { step: string; message: string; percent?: number; batchIndex?: number; totalBatches?: number }) => void
  ): Promise<SavePdfToByteStoreResult> {
    try {
      const fileName = `${title.replace(/[^a-zA-Z0-9]/g, '_')}_${documentId}.pdf`;
      const byteStoreCollection = `users/${userId}/byteStoreCollection`;
      const progress = (msg: string, data?: any, evt?: { step: string; message: string; percent?: number; batchIndex?: number; totalBatches?: number }) => {
        try { console.log(`[PROGRESS] ${msg}`, data ? JSON.stringify(data) : ''); } catch {}
        try { onProgress && onProgress(evt || { step: 'info', message: msg }); } catch {}
      };
      progress('Uploading PDF to storage...', undefined, { step: 'upload', message: 'Uploading PDF to storage...' });

      // 1. Upload PDF to Firebase Storage
      const storagePath = `codebase-reports/${userId}/${fileName}`;
      const storage = getStorage();
      const storageRef = ref(storage, storagePath);

      // IMPORTANT: Firebase Storage customMetadata only allows flat string key/values and small size.
      // Do NOT spread the full metadata object here (it can contain large arrays/objects like fileAnalysisCondensed).
      const safeCustomMetadata: Record<string, string> = {
        title,
        category,
        generatedBy: 'CodebaseIndexingSystem',
      };
      if (metadata && typeof metadata === 'object') {
        if (metadata.reportId) safeCustomMetadata.reportId = String(metadata.reportId);
        if (metadata.totalFiles !== undefined) safeCustomMetadata.totalFiles = String(metadata.totalFiles);
        if (metadata.totalChunks !== undefined) safeCustomMetadata.totalChunks = String(metadata.totalChunks);
        if (metadata.createdAt) safeCustomMetadata.createdAt = String(metadata.createdAt);
        if (metadata.completedAt) safeCustomMetadata.completedAt = String(metadata.completedAt);
      }

      await uploadBytes(storageRef, pdfBuffer, {
        contentType: 'application/pdf',
        customMetadata: safeCustomMetadata
      });

      // Get download URL
      const downloadUrl = await getDownloadURL(storageRef);

      // 2. Store metadata in files collection
      const filesCollection = `users/${userId}/files`;

      // Store full metadata (including fileAnalysisCondensed) ONLY in Firestore,
      // not in Storage customMetadata above.
      // Prepare document data, filtering out undefined values
      const documentData: Record<string, any> = {
        category,
        createdAt: new Date().toISOString(),
        downloadUrl,
        name: fileName,
        namespace: documentId,
        ref: storagePath,
        size: pdfBuffer.length,
        type: 'application/pdf',
        title,
        ...metadata
      };

      // Filter out any undefined values
      Object.keys(documentData).forEach(key => {
        if (documentData[key] === undefined) {
          delete documentData[key];
        }
      });

      await setDoc(doc(db, filesCollection, documentId), documentData);

      // 3. Process content for byteStoreCollection
      // Initialize the text splitter for chunking
      const textSplitter = new RecursiveCharacterTextSplitter({
        chunkSize: CHUNK_SIZE,
        chunkOverlap: CHUNK_OVERLAP,
        separators: ["\n\n", "\n", ". ", " ", ""]
      });

      // Split the content into chunks
      const textChunks = await textSplitter.createDocuments([content]);

      // Initialize OpenAI embeddings
      const embeddings = new OpenAIEmbeddings({
        apiKey: process.env.OPENAI_API_KEY,
      });

      // Initialize Pinecone index
      const pinecone = new Pinecone();
      const pineconeIndex = pinecone.Index(process.env.PINECONE_INDEX || 'ikedia');

      const t0 = Date.now();
      console.log(`[TIMING] Starting chunking for document ${documentId} ...`);

      const chunkingStart = Date.now();
      // Prepare chunk ids and metadata first
      const chunkIds: string[] = [];
      const pineconeMetas: any[] = [];
      const contents: string[] = [];

      for (let i = 0; i < textChunks.length; i++) {
        const chunk = textChunks[i];
        const chunkId = `${documentId}_${i + 1}`;
        chunkIds.push(chunkId);
        contents.push(chunk.pageContent);
        const chunkMetadata = cleanMetadata({
          doc_id: documentId,
          chunk_id: chunkId,
          chunkId: chunkId,
          namespace: documentId,
          document_title: title,
          category,
          file_type: 'application/pdf',
          position: i + 1,
          total_chunks: textChunks.length,
          is_summary: i === 0,
          source_url: downloadUrl,
          processed_at: new Date().toISOString(),
          ...pineconeSafeMetadata(metadata)
        } as any);
        pineconeMetas.push(chunkMetadata);
      }
      console.log(`[TIMING] Text chunking prepared ${textChunks.length} chunks in ${Date.now() - chunkingStart}ms`);

      // Firestore writes (raw chunks) - batch writes in groups to reduce round trips
      const writeStart = Date.now();
      const batchWriteSize = 400; // safe under Firestore 500 op limit
      for (let i = 0; i < textChunks.length; i += batchWriteSize) {
        const batchEnd = Math.min(i + batchWriteSize, textChunks.length);
        const batch = writeBatch(db);
        for (let j = i; j < batchEnd; j++) {
          batch.set(doc(db, byteStoreCollection, chunkIds[j]), {
            content: contents[j],
            metadata: pineconeMetas[j]
          });
        }
        await batch.commit();
      }
      console.log(`[TIMING] Firestore write of ${textChunks.length} chunks completed in ${Date.now() - writeStart}ms`);

      // Embeddings in batches
      const embedBatchSize = 64; // 32-64 recommended
      const allEmbeddings: number[][] = [];
      const totalEmbedBatches = Math.ceil(contents.length / embedBatchSize);
      for (let b = 0; b < contents.length; b += embedBatchSize) {
        const batchIndex = Math.floor(b / embedBatchSize) + 1;
        const embedStart = Date.now();
        const batchDocs = contents.slice(b, b + embedBatchSize);
        const vectors = await embeddings.embedDocuments(batchDocs);
        allEmbeddings.push(...vectors);
        console.log(`[TIMING] Embedding batch ${batchIndex}/${totalEmbedBatches} completed in ${Date.now() - embedStart}ms`);
      }

      // Pinecone upserts in batches
      const upsertBatchSize = 100; // 50-100 recommended
      const totalUpsertBatches = Math.ceil(allEmbeddings.length / upsertBatchSize);
      for (let b = 0; b < allEmbeddings.length; b += upsertBatchSize) {
        const batchIndex = Math.floor(b / upsertBatchSize) + 1;
        const upsertStart = Date.now();
        const vectorBatch = [] as any[];
        for (let j = b; j < Math.min(b + upsertBatchSize, allEmbeddings.length); j++) {
          vectorBatch.push({ id: chunkIds[j], values: allEmbeddings[j], metadata: pineconeMetas[j] });
        }
        await pineconeIndex.namespace(documentId).upsert(vectorBatch);
        console.log(`[TIMING] Pinecone upsert batch ${batchIndex}/${totalUpsertBatches} completed in ${Date.now() - upsertStart}ms`);
      }

      console.log(`[TIMING] Total processing for ${documentId} completed in ${Date.now() - t0}ms`);

      return {
        documentId,
        downloadUrl,
        totalChunks: textChunks.length,
        success: true
      };
    } catch (error) {
      console.error('Error saving codebase report to byteStore:', error);
      throw error;
    }
  }
  /**
   * Write a full per-file analysis document to users/{userId}/files/{fileDocId}
   * Stores complete LLM summary and code intelligence fields.
   */
  async writeCodeFileDoc(
    userId: string,
    fileDocId: string,
    payload: Record<string, any>,
    onProgress?: (u: { step: string; message: string }) => void
  ): Promise<void> {
    const filesCollection = `users/${userId}/files`;

    // Defensive mapping: accept legacy keys (fileId, fileName) and map to the
    // canonical schema (namespace, name). Do not persist legacy fields.
    const { fileId, fileName, ...rest } = payload || {};

    // Minimal file doc per spec: name, namespace, category, createdAt, chunkCount, downloadUrl (optional)
    // Compute a safe download URL if provided; do NOT include undefined in Firestore writes
    const safeDownloadUrl = typeof (rest as any).downloadUrl === 'string'
      ? (rest as any).downloadUrl
      : (typeof (rest as any).fileUrl === 'string' ? (rest as any).fileUrl : undefined);

    const minimalDoc: Record<string, any> = {
      name: (rest as any).name ?? fileName ?? 'Untitled',
      namespace: (rest as any).namespace ?? fileId ?? fileDocId,
      category: typeof (rest as any).category === 'string' ? (rest as any).category.trim() : 'Unknown',
      chunkCount: typeof (rest as any).chunkCount === 'number' ? (rest as any).chunkCount : 0,
      createdAt: serverTimestamp(),
    };
    // Optional light-weight fields for discoverability
    if (typeof (rest as any).type === 'string') minimalDoc.type = (rest as any).type;
    if (typeof (rest as any).language === 'string') minimalDoc.language = (rest as any).language;
    if (typeof (rest as any).indexingSessionId === 'string') minimalDoc.indexingSessionId = (rest as any).indexingSessionId;
    if (typeof (rest as any).ref === 'string') minimalDoc.ref = (rest as any).ref;
    if (typeof safeDownloadUrl === 'string' && safeDownloadUrl) {
      minimalDoc.downloadUrl = safeDownloadUrl;
    }

    await setDoc(doc(db, filesCollection, fileDocId), minimalDoc, { merge: true });
    try { onProgress && onProgress({ step: 'file-doc:write', message: `Saved minimal file doc ${fileDocId}` }); } catch {}

    // Move detailed analysis into byteStoreCollection as chunked content
    try {
      const normalizeArr = (v: any): string[] => Array.isArray(v) ? v.filter((x) => typeof x === 'string' && x.trim()).map((x) => x.trim()) : [];
      const analysisSummary = typeof (rest as any).llmSummary === 'string' ? (rest as any).llmSummary : '';
      const imports = normalizeArr((rest as any).imports);
      const exportsArr = normalizeArr((rest as any).exports);
      const usedLibraries = normalizeArr((rest as any).usedLibraries);
      const usedComponentsOrHooks = normalizeArr((rest as any).usedComponentsOrHooks);
      const apiEndpoints = normalizeArr((rest as any).apiEndpoints);
      const definedEntities = normalizeArr((rest as any).definedEntities);

      const filePath = (rest as any).filePath || '';
      const language = (rest as any).language || '';
      const category = minimalDoc.category;
      const title = minimalDoc.name;

      // Build a rich analysis text payload for RAG
      const analysisTextParts: string[] = [];
      analysisTextParts.push(`# Code Analysis for ${title}`);
      if (filePath) analysisTextParts.push(`File: ${filePath}`);
      if (language) analysisTextParts.push(`Language: ${language}`);
      if (analysisSummary) analysisTextParts.push(`\nSummary:\n${analysisSummary}`);
      if (imports.length) analysisTextParts.push(`\nImports:\n- ${imports.join('\n- ')}`);
      if (exportsArr.length) analysisTextParts.push(`\nExports:\n- ${exportsArr.join('\n- ')}`);
      if (usedLibraries.length) analysisTextParts.push(`\nUsed Libraries:\n- ${usedLibraries.join('\n- ')}`);
      if (usedComponentsOrHooks.length) analysisTextParts.push(`\nUsed Components/Hooks:\n- ${usedComponentsOrHooks.join('\n- ')}`);
      if (definedEntities.length) analysisTextParts.push(`\nDefined Entities:\n- ${definedEntities.join('\n- ')}`);
      if (apiEndpoints.length) analysisTextParts.push(`\nAPI Endpoints:\n- ${apiEndpoints.join('\n- ')}`);

      const analysisText = analysisTextParts.join('\n');

      if (analysisText.trim().length > 0) {
        const splitter = new RecursiveCharacterTextSplitter({
          chunkSize: CHUNK_SIZE,
          chunkOverlap: CHUNK_OVERLAP,
          separators: ['\n\n', '\n', '. ', ' ', '']
        });
        const docs = await splitter.createDocuments([analysisText]);

        const baseCount = typeof (rest as any).chunkCount === 'number' ? (rest as any).chunkCount : 0;
        const chunkIds: string[] = [];
        const contents: string[] = [];
        const metas: Record<string, any>[] = [];
        for (let i = 0; i < docs.length; i++) {
          // Continue numeric chunk_id sequence after any raw code chunks
          const cid = `${fileDocId}_${baseCount + i + 1}`;
          chunkIds.push(cid);
          contents.push(docs[i].pageContent);
          metas.push(cleanMetadata({
            doc_id: fileDocId,
            chunk_id: cid,
            namespace: fileDocId,
            document_title: title,
            category,
            file_path: filePath,
            language,
            file_type: 'application/code-analysis',
            position: baseCount + i + 1,
            total_chunks: baseCount + docs.length,
            is_summary: i === 0,
            processed_at: new Date().toISOString(),
            // Preserve key analysis fields in metadata for filtering
            codeEntityType: (rest as any).codeEntityType || (rest as any).entityType,
            imports,
            exports: exportsArr,
            usedLibraries,
            usedComponentsOrHooks,
            definedEntities,
            apiEndpoints,
          }));
        }

        // Persist the analysis chunks to the user's byteStore
        await this.persistCodeChunksToByteStore(userId, chunkIds, contents, metas, (u) => {
          try { onProgress && onProgress({ step: 'analysis-chunks:write', message: u.message }); } catch {}
        });

        // Ensure these analysis chunks are also embedded for retrieval
        try {
          if (!vectorEmbeddingTool.isInitialized()) {
            await vectorEmbeddingTool.initialize();
          }
          for (let e = 0; e < contents.length; e++) {
            await vectorEmbeddingTool.createEmbedding(
              contents[e],
              chunkIds[e],
              metas[e],
              fileDocId // namespace = fileDocId so category search across namespaces finds them
            );
          }
          try { onProgress && onProgress({ step: 'analysis-chunks:embed', message: `Embedded ${contents.length} analysis chunks` }); } catch {}
        } catch (embedErr) {
          console.warn('writeCodeFileDoc: failed to embed analysis chunks:', embedErr);
        }

        // Update file doc chunkCount to reflect analysis chunking (raw code + analysis chunks)
        await setDoc(doc(db, filesCollection, fileDocId), { chunkCount: baseCount + docs.length }, { merge: true });
      }
    } catch (err) {
      console.warn('writeCodeFileDoc: failed to create analysis chunks:', err);
    }
  }

  /**
   * Persist raw code chunks to users/{userId}/byteStoreCollection in batches.
   * Uses provided chunkIds (e.g., {fileDocId}_{index}).
   */
  async persistCodeChunksToByteStore(
    userId: string,
    chunkIds: string[],
    contents: string[],
    metas: Array<Record<string, any>>,
    onProgress?: (u: { step: string; message: string; batchIndex?: number; totalBatches?: number }) => void
  ): Promise<void> {
    if (chunkIds.length !== contents.length || chunkIds.length !== metas.length) {
      throw new Error('persistCodeChunksToByteStore: array length mismatch');
    }

    const byteStoreCollection = `users/${userId}/byteStoreCollection`;
    const batchWriteSize = 400; // Safe under Firestore 500 op limit
    const totalBatches = Math.ceil(chunkIds.length / batchWriteSize) || 1;

    for (let i = 0; i < chunkIds.length; i += batchWriteSize) {
      const batchEnd = Math.min(i + batchWriteSize, chunkIds.length);
      const batch = writeBatch(db);
      for (let j = i; j < batchEnd; j++) {
        batch.set(doc(db, byteStoreCollection, chunkIds[j]), {
          content: contents[j],
          metadata: metas[j]
        });
      }
      await batch.commit();
      try { onProgress && onProgress({ step: 'chunks:write', message: `Wrote chunk batch ${Math.floor(i / batchWriteSize) + 1}/${totalBatches}` , batchIndex: Math.floor(i / batchWriteSize) + 1, totalBatches }); } catch {}
    }
  }


  /**
   * Save structured report metadata that the UI lists under users/{userId}/codebase-indexing-reports
   */
  async saveCodebaseIndexingReportMetadata(userId: string, report: CodebaseIndexingReport): Promise<string> {
    try {
      const reportsCollection = `users/${userId}/codebase-indexing-reports`;
      const stored: StoredCodebaseIndexingReport = {
        ...report,
        createdAt: report.createdAt instanceof Date ? report.createdAt.toISOString() : (report.createdAt as any),
        completedAt: report.completedAt instanceof Date ? report.completedAt.toISOString() : (report.completedAt as any),
      } as any;

      const docRef = doc(db, reportsCollection, report.indexingSessionId);
      await setDoc(docRef, stored, { merge: true });
      return docRef.id;
    } catch (error) {
      console.error('Error saving codebase indexing report metadata:', error);
      throw error;
    }
  }
}

// Export a singleton instance
export const storageTool = new StorageTool();
