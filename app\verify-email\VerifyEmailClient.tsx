'use client'

import { useEffect, useState } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import { collection, query, where, getDocs, updateDoc } from 'firebase/firestore'
import { db } from 'components/firebase'
import Link from 'next/link'
import Image from 'next/image'

export default function VerifyEmailClient() {
  const [status, setStatus] = useState<string>('Verifying...')
  const [isVerified, setIsVerified] = useState(false)
  const searchParams = useSearchParams()
  const router = useRouter()

  useEffect(() => {
    async function verifyEmail() {
      // Handle case where searchParams might be null
      const token = searchParams?.get('token')
      if (!token) {
        setStatus('No verification token provided.')
        return
      }

      try {
        const accountsRef = collection(db, 'Accounts')
        const q = query(accountsRef, where('verificationToken', '==', token))
        const querySnapshot = await getDocs(q)

        if (!querySnapshot.empty) {
          const userDoc = querySnapshot.docs[0]
          await updateDoc(userDoc.ref, {
            isVerified: true,
            verificationToken: null,
          })
          setStatus('iKe email verified successfully! You can now log in.')
          setIsVerified(true)
        } else {
          setStatus('Invalid or expired verification token.')
        }
      } catch (error) {
        console.error('Error verifying email:', error)
        setStatus('An error occurred while verifying your email.')
      }
    }

    verifyEmail()
  }, [searchParams])

  const handleClose = () => {
    router.push('/') // Redirect to home page
  }

  return (
    <div className="bg-white text-ike-purple text-center p-8 rounded-lg shadow-md">
      <Link href="/" className="flex items-center space-x-1 text-indigo-950 hover:text-gray-600 transition-colors duration-200">
        <Image
          src="/faviconb.png"
          alt="Company Logo"
          width={80}
          height={80}
          className="hidden sm:block"
        />
      </Link>
      <h1 className="text-2xl font-bold text-green-500 mt-2 mb-4">Email Verification</h1>
      <p className="mb-4">{status}</p>
      {isVerified && (
        <button 
          onClick={handleClose}
          className="bg-ike-purple text-white px-4 py-2 rounded hover:bg-opacity-90 transition-colors duration-200"
        >
          Close
        </button>
      )}
    </div>
  )
}