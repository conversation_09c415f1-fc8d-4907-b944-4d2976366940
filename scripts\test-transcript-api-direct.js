/**
 * Direct test of the transcript state API route without requiring a running server
 * This tests the Firebase integration directly
 */

// Mock Next.js request/response objects
class MockNextRequest {
  constructor(method, url, body = null) {
    this.method = method;
    this.url = url;
    this.body = body;
    this.headers = new Map();
  }

  async json() {
    return this.body;
  }
}

class MockNextResponse {
  static json(data, options = {}) {
    return {
      status: options.status || 200,
      data: data
    };
  }
}

// Test data
const testTranscript = {
  userId: 'test-user-123',
  agentId: 'test-agent-456',
  conversationId: 'test-conversation-789',
  dialogue: [
    {
      role: 'user',
      content: 'Hello, I need help with my project.',
      timestamp: new Date().toISOString()
    },
    {
      role: 'assistant',
      content: 'I\'d be happy to help you with your project. What specific area would you like to focus on?',
      timestamp: new Date().toISOString()
    }
  ],
  agentName: 'Marketing Director',
  agentType: 'Marketing',
  timestamp: Date.now()
};

async function testTranscriptAPI() {
  console.log('🧪 Testing Firebase-based Transcript State API (Direct)...\n');

  try {
    // Import the API route functions
    const { POST, GET, DELETE } = await import('../app/api/transcript-state/route.ts');

    // Test 1: Store transcript
    console.log('📝 Test 1: Storing transcript...');
    const storeRequest = new MockNextRequest('POST', '/api/transcript-state', testTranscript);
    const storeResponse = await POST(storeRequest);
    
    console.log('Store Response:', {
      status: storeResponse.status,
      data: storeResponse.data
    });

    if (storeResponse.status !== 200) {
      throw new Error(`Failed to store transcript: ${JSON.stringify(storeResponse.data)}`);
    }

    console.log('✅ Transcript stored successfully\n');

    // Test 2: Retrieve by userId
    console.log('📖 Test 2: Retrieving by userId...');
    const retrieveRequest = new MockNextRequest('GET', `/api/transcript-state?userId=${encodeURIComponent(testTranscript.userId)}`);
    const retrieveResponse = await GET(retrieveRequest);
    
    console.log('Retrieve Response:', {
      status: retrieveResponse.status,
      success: retrieveResponse.data.success,
      foundKey: retrieveResponse.data.foundKey,
      dialogueLength: retrieveResponse.data.transcriptState?.dialogue?.length
    });

    if (retrieveResponse.data.success) {
      console.log('✅ Retrieved by userId successfully\n');
    } else {
      console.log('❌ Failed to retrieve by userId\n');
    }

    // Test 3: Clean up
    console.log('🗑️ Test 3: Cleaning up...');
    const deleteRequest = new MockNextRequest('DELETE', '/api/transcript-state', {
      userId: testTranscript.userId,
      agentId: testTranscript.agentId,
      conversationId: testTranscript.conversationId
    });
    const deleteResponse = await DELETE(deleteRequest);
    
    console.log('Delete Response:', {
      status: deleteResponse.status,
      data: deleteResponse.data
    });

    if (deleteResponse.status === 200) {
      console.log('✅ Transcript deleted successfully\n');
    } else {
      console.log('❌ Failed to delete transcript\n');
    }

    console.log('🎉 Direct API test completed!');

  } catch (error) {
    console.error('❌ Direct test failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

// Run the test
testTranscriptAPI();
