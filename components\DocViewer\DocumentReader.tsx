"use client";

import 'react-pdf/dist/esm/Page/AnnotationLayer.css';
import 'react-pdf/dist/esm/Page/TextLayer.css';

import { Document, Page, pdfjs } from 'react-pdf';
import { useEffect, useState } from 'react';
import { Loader2Icon, RotateCw, ZoomInIcon, ZoomOutIcon } from 'lucide-react';
import React from 'react';

//console.log(pdfjs.version); // Logs the PDF.js version to the console

// Configure PDF.js worker dynamically to match the bundled version and major format
const __pdfjsVersion = (pdfjs as any).version || '4.0.0';
const __pdfjsMajor = parseInt(String(__pdfjsVersion).split('.')[0] || '4', 10);
const __workerExt = __pdfjsMajor >= 4 ? 'mjs' : 'js';
pdfjs.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@${__pdfjsVersion}/build/pdf.worker.min.${__workerExt}`;

interface DocumentReaderProps {
  url: string;
}

const DocumentReader: React.FC<DocumentReaderProps> = ({ url }) => {
  const [numPages, setNumPages] = useState<number>();
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [file, setFile] = useState<Blob | null>(null);
  const [rotation, setRotation] = useState<number>(0);
  const [scale, setScale] = useState<number>(1);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    const fetchFile = async () => {
      try {
        console.log('Fetching PDF from URL:', url);  // Log the URL for debugging
        const response = await fetch(url);
        if (!response.ok) {
          throw new Error(`Failed to fetch PDF: ${response.status} ${response.statusText}`);
        }
        const file = await response.blob();
        console.log('Fetched file:', file);
        setFile(file);
        setLoading(false); // File is loaded, so stop loading
      } catch (error) {
        console.error('Error fetching the PDF file:', error);
        setLoading(false); // Stop loading even if there's an error
      }
    };

    fetchFile();
  }, [url]);

  const onDocumentLoadSuccess = ({ numPages }: { numPages: number }) => {
    console.log('PDF loaded successfully with number of pages:', numPages);
    setNumPages(numPages);
  };

  return (
    <div>
      {loading ? (
        <div className="flex justify-center items-center h-screen">
          <Loader2Icon className="animate-spin w-12 h-12 text-gray-500" />
        </div>
      ) : (
        file && (
          <div>
           <div className="flex justify-center mt-4">
              <button onClick={() => setPageNumber(pageNumber - 1)} disabled={pageNumber <= 1}>
                Previous Page
              </button>
              <span className="mx-2">
                Page {pageNumber} of {numPages}
              </span>
              <button onClick={() => setPageNumber(pageNumber + 1)} disabled={pageNumber >= numPages!}>
                Next Page
              </button>
              <button onClick={() => setRotation((rotation + 90) % 360)}>
                <RotateCw className="ml-4" />
              </button>
              <button onClick={() => setScale(scale + 0.1)}>
                <ZoomInIcon className="ml-4" />
              </button>
              <button onClick={() => setScale(scale - 0.1)} disabled={scale <= 0.5}>
                <ZoomOutIcon className="ml-4" />
              </button>
            </div>            
            <Document
              loading={<Loader2Icon className="animate-spin w-12 h-12 text-gray-500" />}
              file={file}
              rotate={rotation}
              onLoadSuccess={onDocumentLoadSuccess}
              onLoadError={(err) => { console.error('[DocumentReader] PDF load error:', err); }}
              error={<div className="p-4 text-red-600">Failed to load PDF. Check console for details.</div>}
              className="m-4 overflow-scroll"
            >
              <Page
                className="shadow-lg"
                scale={scale}
                pageNumber={pageNumber}
              />
            </Document>
 
          </div>
        )
      )}
    </div>
  );
};

export default DocumentReader;
