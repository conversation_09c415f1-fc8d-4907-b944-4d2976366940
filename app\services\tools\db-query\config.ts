import type { DBQueryConfig } from './types';

export const dbQueryConfig: DBQueryConfig = {
  defaultStrategy: (process.env.DB_QUERY_DEFAULT_STRATEGY as any) || 'standalone',
  enabledStrategies: (process.env.DB_QUERY_ENABLED_STRATEGIES?.split(',').filter(Boolean) as any) || [
    'standalone',
    'content-selector',
    'two-stage',
    'hybrid-inverted'
  ],
  abTest: {
    enabled: process.env.DB_QUERY_AB_TEST === 'true',
    strategies: (process.env.DB_QUERY_AB_STRATEGIES?.split(',').filter(Boolean) as any) || []
  }
};

