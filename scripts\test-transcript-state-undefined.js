/**
 * Test the transcript state API with undefined values to ensure Firestore compatibility
 */

const testCases = [
  {
    name: "Complete data (all fields defined)",
    data: {
      userId: 'test-user-123',
      agentId: 'test-agent-456',
      conversationId: 'test-conversation-789',
      dialogue: [
        {
          role: 'user',
          content: 'Hello, I need help with my project.',
          timestamp: new Date().toISOString()
        }
      ],
      agentName: 'Marketing Director',
      agentType: 'Marketing'
    }
  },
  {
    name: "Missing agentId (undefined)",
    data: {
      userId: 'test-user-123',
      agentId: undefined,
      conversationId: 'test-conversation-789',
      dialogue: [
        {
          role: 'user',
          content: 'Hello, I need help with my project.',
          timestamp: new Date().toISOString()
        }
      ],
      agentName: 'Marketing Director',
      agentType: 'Marketing'
    }
  },
  {
    name: "Missing conversationId (undefined)",
    data: {
      userId: 'test-user-123',
      agentId: 'test-agent-456',
      conversationId: undefined,
      dialogue: [
        {
          role: 'user',
          content: 'Hello, I need help with my project.',
          timestamp: new Date().toISOString()
        }
      ],
      agentName: 'Marketing Director',
      agentType: 'Marketing'
    }
  },
  {
    name: "Missing both agentId and conversationId (undefined)",
    data: {
      userId: 'test-user-123',
      agentId: undefined,
      conversationId: undefined,
      dialogue: [
        {
          role: 'user',
          content: 'Hello, I need help with my project.',
          timestamp: new Date().toISOString()
        }
      ],
      agentName: undefined,
      agentType: undefined
    }
  }
];

async function testTranscriptStateAPI() {
  console.log('🧪 Testing Transcript State API with Undefined Values...\n');

  const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
  
  for (let i = 0; i < testCases.length; i++) {
    const testCase = testCases[i];
    console.log(`📝 Test ${i + 1}: ${testCase.name}`);
    
    try {
      // Test POST (store transcript)
      const response = await fetch(`${baseUrl}/api/transcript-state`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testCase.data)
      });

      const result = await response.json();
      
      if (response.ok) {
        console.log(`✅ POST successful: ${result.message}`);
        
        // Test GET (retrieve transcript)
        const getResponse = await fetch(`${baseUrl}/api/transcript-state?userId=${encodeURIComponent(testCase.data.userId)}`);
        const getResult = await getResponse.json();
        
        if (getResponse.ok && getResult.success) {
          console.log(`✅ GET successful: Found transcript with ${getResult.transcriptState.dialogue.length} messages`);
          
          // Test DELETE (cleanup)
          const deleteResponse = await fetch(`${baseUrl}/api/transcript-state`, {
            method: 'DELETE',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              userId: testCase.data.userId,
              agentId: testCase.data.agentId,
              conversationId: testCase.data.conversationId
            })
          });
          
          const deleteResult = await deleteResponse.json();
          if (deleteResponse.ok) {
            console.log(`✅ DELETE successful: ${deleteResult.message}`);
          } else {
            console.log(`⚠️ DELETE failed: ${deleteResult.error}`);
          }
        } else {
          console.log(`❌ GET failed: ${getResult.error || 'Unknown error'}`);
        }
      } else {
        console.log(`❌ POST failed: ${result.error || 'Unknown error'}`);
      }
    } catch (error) {
      console.log(`❌ Test failed with exception: ${error.message}`);
    }
    
    console.log(''); // Empty line between tests
  }
  
  console.log('🎯 Summary:');
  console.log('- The API should now handle undefined values gracefully');
  console.log('- Firestore errors about undefined values should be resolved');
  console.log('- Transcript storage should work even when agentId/conversationId are missing');
  console.log('- This allows the system to work during the transition period while ElevenLabs webhook is being updated');
}

// Check if we're running in a browser environment
if (typeof window === 'undefined') {
  // Node.js environment - use node-fetch if available
  try {
    const fetch = require('node-fetch');
    global.fetch = fetch;
    testTranscriptStateAPI().catch(console.error);
  } catch (error) {
    console.log('❌ node-fetch not available. Please run this test in a browser or install node-fetch.');
    console.log('Alternative: Test manually using curl or Postman with the test cases above.');
  }
} else {
  // Browser environment
  testTranscriptStateAPI().catch(console.error);
}
