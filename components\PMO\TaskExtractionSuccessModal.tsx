"use client"

import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { CheckCircle, X, Users, Calendar, Target, FileText, ArrowRight } from 'lucide-react'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '../ui/dialog'

interface TaskExtractionResult {
  taskCount: number
  extractionMode: 'PMO' | 'Standard'
  modelUsed: 'Gemini' | 'Groq'
  tasks: Array<{
    title: string
    category: string
    assignedTo?: string
    dueDate?: string
  }>
  confidence: number
  processingTime: number
}

interface TaskExtractionSuccessModalProps {
  isOpen: boolean
  onClose: () => void
  result: TaskExtractionResult
  projectName?: string
  agentType?: string
}

export const TaskExtractionSuccessModal: React.FC<TaskExtractionSuccessModalProps> = ({
  isOpen,
  onClose,
  result,
  projectName = 'Strategic Analysis',
  agentType = 'Strategic Director'
}) => {
  const { taskCount, extractionMode, modelUsed, tasks, confidence, processingTime } = result

  const getExtractionModeInfo = () => {
    if (extractionMode === 'PMO') {
      return {
        icon: Target,
        color: 'text-blue-400',
        bgColor: 'bg-blue-500/10',
        borderColor: 'border-blue-500/20',
        title: 'Enhanced PMO Task Extraction',
        description: 'Advanced extraction mode for structured PMO assessments'
      }
    } else {
      return {
        icon: FileText,
        color: 'text-green-400',
        bgColor: 'bg-green-500/10',
        borderColor: 'border-green-500/20',
        title: 'Standard Task Extraction',
        description: 'General task extraction from strategic analysis'
      }
    }
  }

  const modeInfo = getExtractionModeInfo()
  const ModeIcon = modeInfo.icon

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl bg-gray-900 border-gray-700 text-white">
        <DialogHeader>
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              <CheckCircle className="w-8 h-8 text-green-400" />
            </div>
            <div>
              <DialogTitle className="text-xl font-semibold text-white">
                Task Extraction Successful
              </DialogTitle>
              <DialogDescription className="text-gray-300 mt-1">
                Successfully extracted {taskCount} tasks from {agentType} analysis
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <div className="space-y-6 mt-6">
          {/* Extraction Mode Info */}
          <div className={`p-4 rounded-lg border ${modeInfo.bgColor} ${modeInfo.borderColor}`}>
            <div className="flex items-center space-x-3">
              <ModeIcon className={`w-6 h-6 ${modeInfo.color}`} />
              <div>
                <h3 className="font-medium text-white">{modeInfo.title}</h3>
                <p className="text-sm text-gray-300">{modeInfo.description}</p>
              </div>
            </div>
          </div>

          {/* Extraction Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-gray-800 p-3 rounded-lg text-center">
              <div className="text-2xl font-bold text-green-400">{taskCount}</div>
              <div className="text-xs text-gray-400">Tasks Extracted</div>
            </div>
            <div className="bg-gray-800 p-3 rounded-lg text-center">
              <div className="text-lg font-bold text-blue-400">{modelUsed}</div>
              <div className="text-xs text-gray-400">AI Model</div>
            </div>
            <div className="bg-gray-800 p-3 rounded-lg text-center">
              <div className="text-lg font-bold text-purple-400">{(confidence * 100).toFixed(0)}%</div>
              <div className="text-xs text-gray-400">Confidence</div>
            </div>
            <div className="bg-gray-800 p-3 rounded-lg text-center">
              <div className="text-lg font-bold text-orange-400">{processingTime}s</div>
              <div className="text-xs text-gray-400">Processing</div>
            </div>
          </div>

          {/* Project Info */}
          <div className="bg-gray-800 p-4 rounded-lg">
            <h4 className="font-medium text-white mb-2 flex items-center">
              <FileText className="w-4 h-4 mr-2 text-gray-400" />
              Project Details
            </h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-400">Project:</span>
                <span className="text-white italic">{projectName}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Agent Type:</span>
                <span className="text-white">{agentType}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Extraction Mode:</span>
                <span className={modeInfo.color}>{extractionMode}</span>
              </div>
            </div>
          </div>

          {/* Task Preview */}
          {tasks.length > 0 && (
            <div className="bg-gray-800 p-4 rounded-lg">
              <h4 className="font-medium text-white mb-3 flex items-center">
                <Users className="w-4 h-4 mr-2 text-gray-400" />
                Extracted Tasks Preview
              </h4>
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {tasks.slice(0, 5).map((task, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-gray-700 rounded text-sm">
                    <div className="flex-1">
                      <div className="text-white font-medium truncate">{task.title}</div>
                      <div className="text-gray-400 text-xs">{task.category}</div>
                    </div>
                    {task.assignedTo && (
                      <div className="text-xs text-blue-400 ml-2">{task.assignedTo}</div>
                    )}
                  </div>
                ))}
                {tasks.length > 5 && (
                  <div className="text-center text-gray-400 text-xs py-2">
                    +{tasks.length - 5} more tasks...
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Next Steps */}
          <div className="bg-gradient-to-r from-green-500/10 to-blue-500/10 border border-green-500/20 p-4 rounded-lg">
            <h4 className="font-medium text-white mb-2 flex items-center">
              <ArrowRight className="w-4 h-4 mr-2 text-green-400" />
              Next Steps
            </h4>
            <ul className="text-sm text-gray-300 space-y-1">
              <li>• Tasks have been created and assigned to team members</li>
              <li>• Check the project dashboard for detailed task information</li>
              <li>• Team members will receive notifications about their assignments</li>
              <li>• Monitor progress through the task management system</li>
            </ul>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-700">
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
            >
              Close
            </button>
            <button
              onClick={() => {
                // Navigate to project dashboard or task list
                window.location.href = '/dashboard/projects'
              }}
              className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors flex items-center"
            >
              View Tasks
              <ArrowRight className="w-4 h-4 ml-2" />
            </button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default TaskExtractionSuccessModal
