import NodePolyfillPlugin from 'node-polyfill-webpack-plugin';
import { fileURLToPath } from 'url';
import path from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  // Disable Fast Refresh on window focus changes to prevent unwanted rebuilds
  experimental: {
    // Disable automatic refresh when switching browser tabs
    refreshOnWindowFocus: false,
  },
  webpack: (config, { isServer, webpack }) => {
    // Enable WebAssembly and layers
    config.experiments = {
      asyncWebAssembly: true,
      layers: true,
    };

    // Add rule to handle `.wasm` files as WebAssembly modules
    config.module.rules.push({
      test: /\.wasm$/,
      type: "webassembly/async",
    });

    // Add Node polyfill plugin with exclusions
    config.plugins.push(new NodePolyfillPlugin({
      excludeAliases: ['net', 'tls', 'fs', 'child_process', 'http', 'https']
    }));

    // Alias 'node:util' to 'util' to handle the scheme issue
    config.resolve.alias = {
      ...config.resolve.alias,
      'node:util': 'util',
    };

    // Handle @langchain/community module
    config.module.rules.push({
      test: /\.m?js$/,
      type: "javascript/auto",
      resolve: {
        fullySpecified: false,
      },
    });

    // Suppress specific Webpack warnings
    config.ignoreWarnings = [
      // Original warning
      (warning) =>
        warning.message.includes("Critical dependency: the request of a dependency is an expression"),
      // Package.json warnings for platform-specific binaries
      (warning) => {
        return (
          warning.message.includes("[webpack.cache.PackFileCacheStrategy/webpack.FileSystemInfo]") &&
          (
            warning.message.includes("isn't a directory or doesn't contain a package.json") ||
            warning.message.includes("@next/swc-") ||
            warning.message.includes("@sentry/cli-") ||
            warning.message.includes("fsevents")
          )
        );
      },
    ];

    // Handle Node.js modules in browser environment
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: false,
        crypto: false,
        net: false,
        tls: false,
        child_process: false,
        http: false,
        https: false,
        stream: false,
        zlib: false,
        os: false,
        dns: false,
        dgram: false,
        url: false,
        querystring: false,
        util: false,
        assert: false,
        buffer: false,
        events: false,
      };
    }

    // Ignore the specific problematic files
    config.plugins.push(
      new webpack.IgnorePlugin({
        resourceRegExp: /test\/data\/05-versions-space\.pdf$/,
      })
    );

    // Ignore agent-base and related networking modules in browser
    if (!isServer) {
      config.plugins.push(
        new webpack.IgnorePlugin({
          resourceRegExp: /^(net|tls|dns|dgram)$/,
        })
      );

      // Specifically ignore agent-base and https-proxy-agent in browser
      config.plugins.push(
        new webpack.IgnorePlugin({
          resourceRegExp: /^agent-base$/,
        })
      );

      config.plugins.push(
        new webpack.IgnorePlugin({
          resourceRegExp: /^https-proxy-agent$/,
        })
      );

      // Ignore Firebase Admin SDK and Google Cloud Storage in browser
      config.plugins.push(
        new webpack.IgnorePlugin({
          resourceRegExp: /^firebase-admin$/,
        })
      );

      config.plugins.push(
        new webpack.IgnorePlugin({
          resourceRegExp: /^@google-cloud\/storage$/,
        })
      );
    }

    // Resolve paths for better compatibility
    config.resolve.modules.push(path.resolve(__dirname, 'node_modules'));

    return config;
  },
  // Add custom headers for security
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
        ],
      },
    ];
  },
  // Optimize images
  images: {
    remotePatterns: process.env.NODE_ENV === 'production'
      ? [
          { protocol: 'https', hostname: 'cheri-ai.com' },
          { protocol: 'https', hostname: 'www.cheri-ai.com' },
          { protocol: 'https', hostname: 'ike-ai.com' },
          { protocol: 'https', hostname: 'www.ike-ai.com' },
          { protocol: 'https', hostname: 'lh3.googleusercontent.com' },
        ]
      : [
          { protocol: 'http', hostname: 'localhost' },
          { protocol: 'https', hostname: 'lh3.googleusercontent.com' },
        ],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },
  // Enable SWC minification
  swcMinify: true,
};

export default nextConfig;