/**
 * OpenAI integration for the LLM tool
 */

// Import the OpenAI SDK
import { openai } from "@ai-sdk/openai";
import { streamText } from "ai";

// Define interfaces for OpenAI processing
export interface OpenAIProcessingOptions {
  prompt: string;
  model?: string;
  modelOptions?: Record<string, any>;
}

/**
 * Process content with OpenAI
 * @param options - Processing options
 * @param options.prompt - The prompt to send to the LLM
 * @param options.model - The model to use (default: "gpt-4o")
 * @param options.modelOptions - Additional model-specific options
 * @returns The generated content
 */
export async function processWithOpenAI(options: OpenAIProcessingOptions): Promise<string> {
  try {
    const {
      prompt,
      model = "gpt-5-2025-08-07",
      modelOptions = {}
    } = options;

    console.log(`Processing with OpenAI model: ${model}`);

    // Initialize the OpenAI provider
    const modelProvider = openai(model);

    // Use streamText to generate the response
    const { textStream } = streamText({
      model: modelProvider,
      messages: [{ role: 'user', content: prompt }],
      temperature: modelOptions.temperature || 0.7,
      maxTokens: modelOptions.maxTokens || 10000
    });

    // Collect the response
    let result = "";
    for await (const delta of textStream) {
      result += delta;
    }

    return result.trim();
  } catch (error: any) {
    console.error("Error processing content with OpenAI:", error);
    return `Error from OpenAI: ${error.message || "Unknown error"}`;
  }
}

/**
 * Get available OpenAI models
 * @returns List of available models
 */
export function getOpenAIModels(): string[] {
  return [
    "gpt-5-2025-08-07", // New model
    "gpt-4.1-2025-04-14",
    "o3-pro-2025-06-10",
    "o3-2025-04-16",
    "o3-mini-2025-01-31",
    "o1-mini-2024-09-12"
  ];
}
