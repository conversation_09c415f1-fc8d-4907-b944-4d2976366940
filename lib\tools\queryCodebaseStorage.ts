/**
 * queryCodebaseStorage.ts
 *
 * Purpose: Specialized, metadata-driven querying over indexed codebases.
 * - Accepts a natural-language query and a codebase category
 * - Resolves category -> namespaces
 * - Performs Pinecone namespace-scoped vector search (with optional metadata filters)
 * - Maps Pinecone matches to Firestore users/{userId}/byteStoreCollection documents
 * - Returns ranked, structured results and (optionally) an LLM analysis summary
 */

import { Pinecone } from "@pinecone-database/pinecone";
import { OpenAIEmbeddings } from "@langchain/openai";
import { FirestoreStore } from "lib/FirestoreStore";
import { fetchDocumentChunksByChunkIds } from "lib/fetchDocumentChunksByChunkIds";
import { adminDb } from "components/firebase-admin";
import { processWithOpenAI } from "./openai-ai";
import { processWithGoogleAI } from "./google-ai";

// ----------------------------
// Types
// ----------------------------

export type CodeEntityType = "Component" | "Function" | "Class" | "Hook" | "Configuration" | "Util" | "Unknown";

export interface CodebaseMetadataFilters {
  filePath?: string | { pattern: string; flags?: string }; // exact or regex-like pattern
  codeEntityType?: CodeEntityType | CodeEntityType[];
  usedLibraries?: string | string[];
  apiEndpoints?: string | string[];
  imports?: string | string[];
  exports?: string | string[];
  definedEntities?: string | string[];
  indexingSessionId?: string | string[];
  processed_at?: { gte?: string; lte?: string }; // ISO strings
  [key: string]: any; // enable filtering for any metadata field
}

export interface QueryCodebaseRequest {
  userId: string;
  category: string; // codebase category identifier
  query: string; // natural language query
  namespaces?: string[]; // optional explicit namespaces override
  topKPerNamespace?: number; // default 5
  filters?: CodebaseMetadataFilters;
  includeLLMAnalysis?: boolean; // deprecated; analysis is now mandatory
  // Optional UI preferences (not required by strategies; used for future extension)
  modelProvider?: string;
  modelName?: string;
}

export interface CodebaseHit {
  chunkId: string;
  namespace: string;
  score: number;
  content: string;
  pineconeMetadata: Record<string, any>;
  firestoreMetadata: Record<string, any>;
}

export interface QueryCodebaseResponse {
  success: boolean;
  results: CodebaseHit[];
  stats: {
    namespacesQueried: string[];
    topKPerNamespace: number;
    totalMatches: number;
    timingsMs: Record<string, number>;
    filtersApplied?: CodebaseMetadataFilters;
  };
  llmAnalysis?: string;
  llmModel?: string;
  llmProvider?: string;
  error?: string;
}

// ----------------------------
// Helpers
// ----------------------------

async function getNamespacesForCategory(userId: string, category: string): Promise<string[]> {
  try {
    const snap = await adminDb
      .collection('users')
      .doc(userId)
      .collection('files')
      .where('category', '==', category)
      .get();
    return snap.docs.map(d => (d.data() as any).namespace).filter(Boolean);
  } catch (e) {
    console.error('getNamespacesForCategory error:', e);
    return [];
  }
}

// Pinecone filters support only primitive comparisons. We construct a conservative filter
// and perform any advanced pattern matching as a post-filter step.
function buildPineconeFilter(filters?: CodebaseMetadataFilters): { pineconeFilter?: any; postFilter?: (m: any) => boolean } {
  if (!filters) return {};

  const f: any = {};
  const postChecks: Array<(m: any) => boolean> = [];

  const setEqOrIn = (key: string, value?: string | string[]) => {
    if (value === undefined) return;
    if (Array.isArray(value) && value.length > 0) f[key] = { $in: value };
    else if (typeof value === 'string' && value.trim()) f[key] = { $eq: value };
  };

  // Direct, safe fields
  setEqOrIn('codeEntityType', filters.codeEntityType as any);
  setEqOrIn('indexingSessionId', filters.indexingSessionId as any);

  // Time range
  if (filters.processed_at?.gte || filters.processed_at?.lte) {
    const range: any = {};
    if (filters.processed_at?.gte) range.$gte = filters.processed_at.gte;
    if (filters.processed_at?.lte) range.$lte = filters.processed_at.lte;
    f.processed_at = range; // may not exist on all chunks
  }

  // Array membership fields (use $in; if not supported for arrays in your Pinecone tier,
  // results will still be post-filtered below)
  const arrayFields: Array<keyof CodebaseMetadataFilters> = [
    'usedLibraries', 'apiEndpoints', 'imports', 'exports', 'definedEntities'
  ];
  for (const key of arrayFields) {
    const v = filters[key];
    if (v === undefined) continue;
    if (Array.isArray(v) && v.length > 0) f[key as string] = { $in: v };
    else if (typeof v === 'string' && v.trim()) f[key as string] = { $in: [v] };
  }

  // filePath pattern handling (post filter)
  if (filters.filePath) {
    const { pattern, flags } = typeof filters.filePath === 'string'
      ? { pattern: filters.filePath, flags: '' }
      : filters.filePath;

    // If exact match (no wildcards), try Pinecone eq
    if (!/[.*+?^${}()|[\]\\]/.test(pattern)) {
      f.filePath = { $eq: pattern };
    }

    // Always add post-filter to support wildcards/regex and partials
    const regex = new RegExp(pattern, flags || 'i');
    postChecks.push((m) => typeof m?.filePath === 'string' ? regex.test(m.filePath) : true);
  }

  // Generic passthrough keys (best-effort eq)
  for (const [k, v] of Object.entries(filters)) {
    if (k in f) continue; // already handled
    if (v === undefined) continue;
    if (typeof v === 'string' || typeof v === 'number' || typeof v === 'boolean') {
      f[k] = { $eq: v };
    }
  }

  const postFilter = (m: any) => postChecks.every(fn => fn(m));
  return { pineconeFilter: Object.keys(f).length ? f : undefined, postFilter };
}

function toChunkId(namespace: string, match: any): string {
  const m = match?.metadata || {};
  const fromMeta = m.chunkId || m.chunk_id;
  if (fromMeta) return String(fromMeta);
  const id = match?.id || match?.metadata?.id;
  return `${namespace}_${id}`;
}

// ----------------------------
// Core query
// ----------------------------

export async function queryCodebaseStorage(req: QueryCodebaseRequest): Promise<QueryCodebaseResponse> {
  const t0 = Date.now();
  const timings: Record<string, number> = {};

  try {
    const {
      userId,
      category,
      query,
      namespaces: explicitNamespaces,
      topKPerNamespace = 1,
      filters
    } = req;

    // Step 1: Determine namespaces
    const nsStart = Date.now();
    const namespaces = (explicitNamespaces && explicitNamespaces.length)
      ? explicitNamespaces
      : await getNamespacesForCategory(userId, category);
    timings.namespaces = Date.now() - nsStart;

    if (!namespaces || namespaces.length === 0) {
      return { success: false, results: [], stats: { namespacesQueried: [], topKPerNamespace, totalMatches: 0, timingsMs: timings, filtersApplied: filters }, error: `No namespaces found for category "${category}"` };
    }

    // Step 2: Embed the query
    const embedStart = Date.now();
    const embedder = new OpenAIEmbeddings();
    const queryVector = await embedder.embedQuery(query);
    timings.embedding = Date.now() - embedStart;

    // Step 3: Build filter
    const { pineconeFilter, postFilter } = buildPineconeFilter(filters);

    // Step 4: Query Pinecone across namespaces
    const pinecone = new Pinecone();
    const index = pinecone.Index(process.env.PINECONE_INDEX!);

    const qStart = Date.now();
    const perNamespaceResults = await Promise.all(
      namespaces.map(async (ns) => {
        try {
          const res = await index.namespace(ns).query({
            vector: queryVector,
            topK: topKPerNamespace,
            includeValues: true,
            includeMetadata: true,
            ...(pineconeFilter ? { filter: pineconeFilter } : {})
          });
          const matches = (res?.matches || []).map((m: any) => ({ ...m, _namespace: ns }));
          return postFilter ? matches.filter((m: any) => postFilter(m.metadata)) : matches;
        } catch (e) {
          console.error(`Pinecone query failed for namespace ${ns}:`, e);
          return [] as any[];
        }
      })
    );
    const pineconeMatches = perNamespaceResults.flat();
    timings.pineconeQuery = Date.now() - qStart;

    if (pineconeMatches.length === 0) {
      const { synthesizeAnswerOverResults } = await import('./synthesizeAnswerOverResults');
      const synth = await synthesizeAnswerOverResults(query, category, [], { provider: req.modelProvider, model: req.modelName });
      timings.llm = synth.timingMs;
      return {
        success: true,
        results: [],
        stats: { namespacesQueried: namespaces, topKPerNamespace, totalMatches: 0, timingsMs: { ...timings, total: Date.now() - t0 }, filtersApplied: filters },
        llmAnalysis: synth.llmAnalysis,
        llmModel: synth.llmModel,
        llmProvider: synth.llmProvider
      };
    }

    // Step 5: Map to chunk IDs and fetch Firestore content
    const ids = Array.from(new Set(pineconeMatches.map(m => toChunkId(m._namespace, m))));

    const fsStart = Date.now();
    const store = new FirestoreStore({ collectionPath: `users/${userId}/byteStoreCollection` });
    const docs = await fetchDocumentChunksByChunkIds(ids, store);
    timings.firestoreFetch = Date.now() - fsStart;

    // Convert docs array into map by chunkId for quick join
    const docMap = new Map<string, any>();
    for (const d of docs) {
      const chunkId = (d.metadata?.chunk_id || d.metadata?.chunkId || '').toString() || ids.find(id => d.metadata?.chunk_id?.toString() === id) || '';
      if (chunkId) docMap.set(chunkId, d);
    }

    // Step 6: Build hits
    const hits: CodebaseHit[] = pineconeMatches.map((m: any) => {
      const chunkId = toChunkId(m._namespace, m);
      const doc = docMap.get(chunkId);
      return {
        chunkId,
        namespace: m._namespace,
        score: typeof m.score === 'number' ? m.score : (m?.values ? 0.5 : 0),
        content: doc?.pageContent || '',
        pineconeMetadata: m?.metadata || {},
        firestoreMetadata: doc?.metadata || {}
      };
    }).filter(h => h.content && h.content.trim());

    // Step 7: Rank and aggregate
    hits.sort((a, b) => b.score - a.score);

    const response: QueryCodebaseResponse = {
      success: true,
      results: hits,
      stats: {
        namespacesQueried: namespaces,
        topKPerNamespace,
        totalMatches: hits.length,
        timingsMs: { ...timings, total: Date.now() - t0 },
        filtersApplied: filters
      }
    };

    // Step 8: Mandatory LLM analysis/synthesis over retrieved content (if there are hits)
    if (hits.length > 0) {
      const { synthesizeAnswerOverResults } = await import('./synthesizeAnswerOverResults');
      const { llmAnalysis, llmModel, llmProvider, timingMs } = await synthesizeAnswerOverResults(query, category, hits, { provider: req.modelProvider, model: req.modelName });
      response.llmAnalysis = llmAnalysis;
      response.llmModel = llmModel;
      response.llmProvider = llmProvider;
      timings.llm = timingMs;
      response.stats.timingsMs = { ...timings, total: Date.now() - t0 };
    }

    return response;
  } catch (error: any) {
    console.error('queryCodebaseStorage failed:', error);
    return {
      success: false,
      results: [],
      stats: { namespacesQueried: [], topKPerNamespace: req.topKPerNamespace || 5, totalMatches: 0, timingsMs: {} },
      error: error?.message || 'Unknown error'
    };
  }
}

// Convenience overload if you already have a namespace set (skips category resolution)
export async function queryCodebaseByNamespaces(params: Omit<QueryCodebaseRequest, 'category'> & { category?: string }): Promise<QueryCodebaseResponse> {
  return queryCodebaseStorage({ ...params, category: params.category || 'Unknown' });
}