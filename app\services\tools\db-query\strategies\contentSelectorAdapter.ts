import type { QueryCodebaseRequest, StrategyRun } from '../types';
import { OpenAIEmbeddings } from '@langchain/openai';
import { ContentSelector } from '../../../../src/processContent/selectContent';
import { FirestoreStore } from 'lib/FirestoreStore';
import { adminDb } from 'components/firebase-admin';

export const ContentSelectorStrategyId = 'content-selector' as const;

async function getNamespacesForCategory(userId: string, category: string): Promise<string[]> {
  try {
    const snap = await adminDb
      .collection('users').doc(userId).collection('files')
      .where('category', '==', category).get();
    return snap.docs.map(d => (d.data() as any).namespace).filter(Boolean);
  } catch {
    return [];
  }
}

export async function executeContentSelectorAdapter(req: QueryCodebaseRequest): Promise<StrategyRun> {
  const start = Date.now();
  const { userId, category, query, namespaces: explicitNamespaces } = req;

  const namespaces = explicitNamespaces?.length
    ? explicitNamespaces
    : await getNamespacesForCategory(userId, category);

  const embedder = new OpenAIEmbeddings();
  const vector = await embedder.embedQuery(query);

  const selector = new ContentSelector(userId);
  const tokenManager = {
    getTokenConfig: () => ({ maxTokens: 8000, reservedTokens: 1000, model: 'text-embedding-3-large' })
  } as any;

  const result = await selector.selectContent(vector, namespaces, tokenManager);

  // Convert to QueryCodebaseResponse-compatible shape
  const firestoreStore = new FirestoreStore({ collectionPath: `users/${userId}/byteStoreCollection` });
  const response = result ? {
    success: true,
    results: (result.metadata.sources || []).map(s => ({
      chunkId: (s as any).chunkId || s.doc_id,
      namespace: s.doc_id || 'unknown',
      score: s.relevance || 0,
      content: (s as any).text_content || '',
      pineconeMetadata: { title: s.title, page: s.page },
      firestoreMetadata: { ...(s as any), storePath: firestoreStore.collectionPath }
    })),
    stats: {
      namespacesQueried: namespaces,
      topKPerNamespace: 5,
      totalMatches: result.metadata.chunkCount,
      timingsMs: {},
      filtersApplied: req.filters
    }
  } : {
    success: true,
    results: [],
    stats: { namespacesQueried: namespaces, topKPerNamespace: 5, totalMatches: 0, timingsMs: {}, filtersApplied: req.filters }
  };

  // Ensure contents are populated when sources only provide doc_id
  if (response.results.length) {
    const rawIds = Array.from(new Set(response.results.map(r => String(r.chunkId))));
    const isChunkId = (id: string) => /_\d+$/.test(id);
    const chunkIds = rawIds.filter(isChunkId);
    const docIds = rawIds.filter(id => !isChunkId(id));

    try {
      const fetchMod = await import('lib/fetchDocumentChunksByChunkIds');
      const byChunk = chunkIds.length ? await fetchMod.fetchDocumentChunksByChunkIds(chunkIds, firestoreStore) : [];
      const byDoc = docIds.length ? await fetchMod.fetchDocumentChunksByDocIds(docIds, firestoreStore) : [];
      const docs = [...byChunk, ...byDoc];
      const docMap = new Map<string, any>(docs.map(d => [String(d.metadata?.chunk_id || d.metadata?.chunkId), d]));
      const expanded: typeof response.results = [] as any;
      for (const r of response.results) {
        if (r.content && r.content.length) { expanded.push(r); continue; }
        if (isChunkId(String(r.chunkId))) {
          const d = docMap.get(String(r.chunkId));
          expanded.push({ ...r, content: r.content || (d?.pageContent || '') });
        } else {
          const prefix = `${r.chunkId}_`;
          const matches = [...docMap.entries()].filter(([k]) => k.startsWith(prefix)).map(([, d]) => d);
          matches.sort((a, b) => {
            const ai = Number(String(a.metadata?.chunk_id || '').split('_').pop());
            const bi = Number(String(b.metadata?.chunk_id || '').split('_').pop());
            return (isNaN(ai) ? 0 : ai) - (isNaN(bi) ? 0 : bi);
          });
          if (matches.length) {
            for (const d of matches) {
              expanded.push({ ...r, chunkId: String(d.metadata?.chunk_id), content: d.pageContent || '' });
            }
          } else {
            expanded.push(r);
          }
        }
      }
      response.results = expanded;
    } catch {}
  }

  const durationMs = Date.now() - start;
  const avgScore = response.results.length
    ? response.results.reduce((s, r) => s + (r.score ?? 0), 0) / response.results.length
    : 0;

  return {
    strategyId: ContentSelectorStrategyId,
    response,
    metrics: { durationMs, namespacesCount: namespaces.length, candidateCount: response.results.length },
    quality: { avgScore, diversity: 0, coverage: (namespaces.length ? new Set(response.results.map(r => r.namespace)).size / namespaces.length : 0) }
  };
}

