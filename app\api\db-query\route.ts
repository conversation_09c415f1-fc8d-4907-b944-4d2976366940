import { NextRequest, NextResponse } from 'next/server';
import { queryCodebase, abTestQuery, listStrategies } from 'lib/services/tools/db-query';
import type { QueryCodebaseRequest } from 'lib/services/tools/db-query/types';

export async function GET() {
  try {
    return NextResponse.json({ ok: true, strategies: listStrategies() }, { status: 200 });
  } catch (e: any) {
    return NextResponse.json({ error: e?.message || 'Internal error' }, { status: 500 });
  }
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { request, strategyId, abTest, abStrategies } = body || {};

    if (!request || !request.userId || !request.category || !request.query) {
      return NextResponse.json({ error: 'Missing fields: request.userId, request.category, request.query required' }, { status: 400 });
    }

    const reqObj: QueryCodebaseRequest = {
      userId: String(request.userId),
      category: String(request.category),
      query: String(request.query),
      namespaces: Array.isArray(request.namespaces) ? request.namespaces : undefined,
      topKPerNamespace: request.topKPerNamespace ? Number(request.topKPerNamespace) : undefined,
      filters: request.filters || undefined,
      modelProvider: typeof request.modelProvider === 'string' ? request.modelProvider : undefined,
      modelName: typeof request.modelName === 'string' ? request.modelName : undefined
    };

    if (abTest) {
      const result = await abTestQuery(reqObj, Array.isArray(abStrategies) ? abStrategies : undefined);
      return NextResponse.json({ mode: 'ab', result }, { status: 200 });
    }

    const run = await queryCodebase(reqObj, strategyId);
    return NextResponse.json({ mode: 'single', run }, { status: 200 });
  } catch (err: any) {
    console.error('db-query app/api error:', err);
    return NextResponse.json({ error: err?.message || 'Internal server error' }, { status: 500 });
  }
}