/**
 * Test script for the new Firebase-based transcript state API
 * Run with: node scripts/test-transcript-state.js
 */

const BASE_URL = process.env.NEXTAUTH_URL || 'http://localhost:3000';

// Test data
const testTranscript = {
  userId: 'test-user-123',
  agentId: 'test-agent-456',
  conversationId: 'test-conversation-789',
  dialogue: [
    {
      role: 'user',
      content: 'Hello, I need help with my project.',
      timestamp: new Date().toISOString()
    },
    {
      role: 'assistant',
      content: 'I\'d be happy to help you with your project. What specific area would you like to focus on?',
      timestamp: new Date().toISOString()
    },
    {
      role: 'user',
      content: 'I need to create a marketing strategy for our new product launch.',
      timestamp: new Date().toISOString()
    }
  ],
  agentName: 'Marketing Director',
  agentType: 'Marketing',
  timestamp: Date.now()
};

async function testTranscriptStateAPI() {
  console.log('🧪 Testing Firebase-based Transcript State API...\n');

  try {
    // Test 1: Store transcript
    console.log('📝 Test 1: Storing transcript...');
    const storeResponse = await fetch(`${BASE_URL}/api/transcript-state`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testTranscript)
    });

    const storeResult = await storeResponse.json();
    console.log('Store Response:', {
      status: storeResponse.status,
      success: storeResult.success,
      message: storeResult.message,
      storeKeys: storeResult.storeKeys
    });

    if (!storeResult.success) {
      throw new Error(`Failed to store transcript: ${storeResult.error}`);
    }

    console.log('✅ Transcript stored successfully\n');

    // Test 2: Retrieve by userId
    console.log('📖 Test 2: Retrieving by userId...');
    const retrieveByUserResponse = await fetch(`${BASE_URL}/api/transcript-state?userId=${encodeURIComponent(testTranscript.userId)}`);
    const retrieveByUserResult = await retrieveByUserResponse.json();
    
    console.log('Retrieve by userId Response:', {
      status: retrieveByUserResponse.status,
      success: retrieveByUserResult.success,
      foundKey: retrieveByUserResult.foundKey,
      dialogueLength: retrieveByUserResult.transcriptState?.dialogue?.length
    });

    if (retrieveByUserResult.success) {
      console.log('✅ Retrieved by userId successfully\n');
    } else {
      console.log('❌ Failed to retrieve by userId\n');
    }

    // Test 3: Retrieve by agentId
    console.log('📖 Test 3: Retrieving by agentId...');
    const retrieveByAgentResponse = await fetch(`${BASE_URL}/api/transcript-state?agentId=${encodeURIComponent(testTranscript.agentId)}`);
    const retrieveByAgentResult = await retrieveByAgentResponse.json();
    
    console.log('Retrieve by agentId Response:', {
      status: retrieveByAgentResponse.status,
      success: retrieveByAgentResult.success,
      foundKey: retrieveByAgentResult.foundKey,
      dialogueLength: retrieveByAgentResult.transcriptState?.dialogue?.length
    });

    if (retrieveByAgentResult.success) {
      console.log('✅ Retrieved by agentId successfully\n');
    } else {
      console.log('❌ Failed to retrieve by agentId\n');
    }

    // Test 4: Retrieve by conversationId
    console.log('📖 Test 4: Retrieving by conversationId...');
    const retrieveByConversationResponse = await fetch(`${BASE_URL}/api/transcript-state?conversationId=${encodeURIComponent(testTranscript.conversationId)}`);
    const retrieveByConversationResult = await retrieveByConversationResponse.json();
    
    console.log('Retrieve by conversationId Response:', {
      status: retrieveByConversationResponse.status,
      success: retrieveByConversationResult.success,
      foundKey: retrieveByConversationResult.foundKey,
      dialogueLength: retrieveByConversationResult.transcriptState?.dialogue?.length
    });

    if (retrieveByConversationResult.success) {
      console.log('✅ Retrieved by conversationId successfully\n');
    } else {
      console.log('❌ Failed to retrieve by conversationId\n');
    }

    // Test 5: Clean up - Delete transcript
    console.log('🗑️ Test 5: Cleaning up...');
    const deleteResponse = await fetch(`${BASE_URL}/api/transcript-state`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        userId: testTranscript.userId,
        agentId: testTranscript.agentId,
        conversationId: testTranscript.conversationId
      })
    });

    const deleteResult = await deleteResponse.json();
    console.log('Delete Response:', {
      status: deleteResponse.status,
      success: deleteResult.success,
      message: deleteResult.message
    });

    if (deleteResult.success) {
      console.log('✅ Transcript deleted successfully\n');
    } else {
      console.log('❌ Failed to delete transcript\n');
    }

    console.log('🎉 All tests completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

// Run the test
testTranscriptStateAPI();
