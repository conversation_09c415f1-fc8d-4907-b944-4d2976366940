/**
 * Test script for the Firebase-based document category state API
 */

const testCategoryState = {
  userId: '<EMAIL>',
  selectedDocuments: [
    {
      id: 'doc-123',
      title: 'Marketing Strategy 2024',
      category: 'PMO - Marketing Strategy'
    },
    {
      id: 'doc-456', 
      title: 'Budget Analysis',
      category: 'PMO - Financial Planning'
    }
  ],
  primaryCategory: 'PMO - Marketing Strategy',
  conversationId: 'test-conversation-789',
  agentType: 'Marketing',
  timestamp: Date.now()
};

async function testDocumentCategoryStateAPI() {
  console.log('🧪 Testing Firebase-based Document Category State API...\n');

  const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
  
  try {
    // Test 1: Store category state
    console.log('📝 Test 1: Storing document category state...');
    const storeResponse = await fetch(`${baseUrl}/api/document-category-state`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testCategoryState)
    });

    const storeResult = await storeResponse.json();
    
    if (storeResponse.ok) {
      console.log('✅ Store successful:', storeResult.message);
      console.log('   Primary category:', storeResult.primaryCategory);
    } else {
      console.log('❌ Store failed:', storeResult.error);
      return;
    }

    // Test 2: Retrieve by userId
    console.log('\n📖 Test 2: Retrieving by userId...');
    const getUserResponse = await fetch(`${baseUrl}/api/document-category-state?userId=${encodeURIComponent(testCategoryState.userId)}`);
    const getUserResult = await getUserResponse.json();
    
    if (getUserResponse.ok && getUserResult.success) {
      console.log('✅ Retrieve by userId successful');
      console.log('   Primary category:', getUserResult.categoryState.primaryCategory);
      console.log('   Selected documents:', getUserResult.categoryState.selectedDocuments.length);
      console.log('   Agent type:', getUserResult.categoryState.agentType);
    } else {
      console.log('❌ Retrieve by userId failed:', getUserResult.message);
    }

    // Test 3: Retrieve by conversationId
    console.log('\n📖 Test 3: Retrieving by conversationId...');
    const getConvResponse = await fetch(`${baseUrl}/api/document-category-state?userId=${encodeURIComponent(testCategoryState.userId)}&conversationId=${encodeURIComponent(testCategoryState.conversationId)}`);
    const getConvResult = await getConvResponse.json();
    
    if (getConvResponse.ok && getConvResult.success) {
      console.log('✅ Retrieve by conversationId successful');
      console.log('   Primary category:', getConvResult.categoryState.primaryCategory);
    } else {
      console.log('❌ Retrieve by conversationId failed:', getConvResult.message);
    }

    // Test 4: Test fallback retrieval (using fallback key)
    console.log('\n📖 Test 4: Testing fallback retrieval...');
    const getFallbackResponse = await fetch(`${baseUrl}/api/document-category-state?userId=<EMAIL>`);
    const getFallbackResult = await getFallbackResponse.json();
    
    if (getFallbackResponse.ok && getFallbackResult.success) {
      console.log('✅ Fallback retrieval successful (<NAME_EMAIL> key)');
      console.log('   Primary category:', getFallbackResult.categoryState.primaryCategory);
    } else {
      console.log('⚠️ Fallback retrieval failed (expected if no fallback data):', getFallbackResult.message);
    }

    // Test 5: Clean up
    console.log('\n🗑️ Test 5: Cleaning up...');
    const deleteResponse = await fetch(`${baseUrl}/api/document-category-state`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userId: testCategoryState.userId
      })
    });
    
    const deleteResult = await deleteResponse.json();
    if (deleteResponse.ok) {
      console.log('✅ Delete successful:', deleteResult.message);
    } else {
      console.log('❌ Delete failed:', deleteResult.error);
    }

    console.log('\n🎉 Document Category State API test completed!');
    console.log('\n🎯 Expected Benefits:');
    console.log('1. ✅ Category state persists across server restarts');
    console.log('2. ✅ Works in serverless environments like Vercel');
    console.log('3. ✅ Multiple lookup keys for reliable webhook access');
    console.log('4. ✅ Automatic TTL cleanup prevents database bloat');
    console.log('5. ✅ Resolves "No category state found" warnings');

  } catch (error) {
    console.error('❌ Test failed with exception:', error.message);
  }
}

// Check if we're running in a browser environment
if (typeof window === 'undefined') {
  // Node.js environment - use node-fetch if available
  try {
    const fetch = require('node-fetch');
    global.fetch = fetch;
    testDocumentCategoryStateAPI().catch(console.error);
  } catch (error) {
    console.log('❌ node-fetch not available. Please run this test in a browser or install node-fetch.');
    console.log('Alternative: Test manually using curl or Postman with the test data above.');
    
    console.log('\n📋 Manual Test Commands:');
    console.log('# Store category state:');
    console.log(`curl -X POST http://localhost:3000/api/document-category-state \\
  -H "Content-Type: application/json" \\
  -d '${JSON.stringify(testCategoryState, null, 2)}'`);
    
    console.log('\n# Retrieve category state:');
    console.log(`curl "http://localhost:3000/api/document-category-state?userId=${encodeURIComponent(testCategoryState.userId)}"`);
    
    console.log('\n# Delete category state:');
    console.log(`curl -X DELETE http://localhost:3000/api/document-category-state \\
  -H "Content-Type: application/json" \\
  -d '{"userId": "${testCategoryState.userId}"}'`);
  }
} else {
  // Browser environment
  testDocumentCategoryStateAPI().catch(console.error);
}
