'use client';

import React from 'react';
import { Calendar, BarChart2, Clock } from 'lucide-react';

interface ProjectHeaderProps {
  projectName: string;
  projectDate: string;
  view: 'board' | 'timeline';
  setView: (view: 'board' | 'timeline') => void;
}

const ProjectHeader: React.FC<ProjectHeaderProps> = ({
  projectName,
  projectDate,
  view,
  setView
}) => {
  return (
    <div className="bg-gradient-to-r from-purple-700 to-indigo-800 text-white py-6 px-4 shadow-md">
      <div className="container mx-auto">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold italic">{projectName}</h1>
            <div className="flex items-center mt-2 text-purple-200">
              <Calendar className="w-4 h-4 mr-2" />
              <span>{projectDate}</span>
            </div>
          </div>
          
          <div className="mt-4 md:mt-0 flex items-center space-x-2">
            <div className="bg-purple-900/50 rounded-lg p-2 text-sm">
              <div className="flex items-center">
                <Clock className="w-4 h-4 mr-1" />
                <span>21 days until launch</span>
              </div>
            </div>
            
            <div className="bg-purple-900/50 rounded-lg p-2 text-sm">
              <div className="flex items-center">
                <BarChart2 className="w-4 h-4 mr-1" />
                <span>75% Complete</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProjectHeader;
