"use client";

import React, { useRef, useEffect } from "react";
import { MessageSquare, User, Bot, Download, Clock } from "lucide-react";
import { Button } from "../ui/button";
import EnhancedMarkdownContent from "../scriptreaderAI/EnhancedMarkdownContent";

export interface TranscriptMessage {
  role: 'user' | 'assistant' | 'agent';
  content: string;
  timestamp: Date;
}

interface TranscriptPanelProps {
  dialogue: TranscriptMessage[];
  currentResponse: string;
  isSpeaking: boolean;
  isListening: boolean;
  agentName?: string;
  onSaveTranscript?: () => void;
  className?: string;
}

export default function TranscriptPanel({
  dialogue,
  currentResponse,
  isSpeaking,
  isListening,
  agentName = "Agent",
  onSaveTranscript,
  className = ""
}: TranscriptPanelProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  // Role identification helpers
  const isUserRole = (role: string): boolean => role.toLowerCase() === "user";
  
  // Auto-scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [dialogue, currentResponse]);
  
  // Connection status indicator
  const connectionStatus = () => {
    if (!isListening) {
      return (
        <div className="text-center text-xs text-yellow-300 py-1 px-3 bg-yellow-900/20 rounded-md">
          No active connection - Voice conversation will appear here once connected
        </div>
      );
    }
    
    if (isSpeaking) {
      return (
        <div className="text-center text-xs text-green-300 py-1 px-3 bg-green-900/20 rounded-md">
          {agentName} is speaking
        </div>
      );
    }
    
    return (
      <div className="text-center text-xs text-blue-300 py-1 px-3 bg-blue-900/20 rounded-md">
        Connection active - Transcribing conversation
      </div>
    );
  };
  
  // Empty state display
  const renderEmptyState = () => (
    <div className="flex flex-col items-center justify-center h-full text-gray-400">
      <MessageSquare className="w-12 h-12 mb-4 opacity-30" />
      <p className="text-lg">Meeting Transcript</p>
      <p className="text-sm mt-2 mb-4 text-center">
        {isListening 
          ? "Start speaking to see the conversation transcript appear here" 
          : "Connect to start your meeting and see the live transcript"}
      </p>
      
      {connectionStatus()}
    </div>
  );

  // Format timestamp for display
  const formatTimestamp = (timestamp: Date): string => {
    return timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Code highlighting theme (matching DialogueDisplay)
  const codeTheme = {
    "hljs-comment": { color: "#6A9955" },
    "hljs-keyword": { color: "#569CD6" },
    "hljs-string": { color: "#CE9178" },
    "hljs-number": { color: "#B5CEA8" },
    "hljs-function": { color: "#DCDCAA" },
    "hljs-title": { color: "#DCDCAA" },
    "hljs-params": { color: "#9CDCFE" },
    "hljs-built_in": { color: "#4EC9B0" },
    "hljs-literal": { color: "#569CD6" },
    "hljs-type": { color: "#4EC9B0" },
    "hljs-tag": { color: "#569CD6" },
    "hljs-name": { color: "#9CDCFE" },
    "hljs-attr": { color: "#9CDCFE" },
    "hljs-selector-id": { color: "#D7BA7D" },
    "hljs-selector-class": { color: "#D7BA7D" },
    "hljs-attribute": { color: "#9CDCFE" },
    "hljs-regexp": { color: "#D16969" },
    "hljs-meta": { color: "#DCDCAA" }
  };

  return (
    <div className={`flex flex-col h-full bg-gray-800 ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-700 bg-gray-800 flex-shrink-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <MessageSquare className="h-5 w-5 text-purple-400" />
            <h3 className="text-lg font-semibold text-white">Live Transcript</h3>
          </div>
          
          {/* Save Transcript Button */}
          {dialogue.length > 0 && onSaveTranscript && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onSaveTranscript}
              className="text-gray-400 hover:text-white hover:bg-gray-700"
            >
              <Download className="h-4 w-4 mr-2" />
              Save
            </Button>
          )}
        </div>
        
        {/* Connection status bar */}
        {(dialogue.length > 0 || currentResponse) && (
          <div className="mt-3">
            {connectionStatus()}
          </div>
        )}
      </div>
      
      {/* Transcript display area */}
      <div className="flex-1 overflow-y-auto p-4 scrollbar-thin scrollbar-track-white/5 scrollbar-thumb-purple-500/20">
        {dialogue.length === 0 && !currentResponse ? (
          renderEmptyState()
        ) : (
          <div className="space-y-4">
            {/* Render historical dialogue messages */}
            {dialogue.map((message, index) => (
              <div 
                key={index} 
                className={`flex ${isUserRole(message.role) ? "justify-end" : "justify-start"}`}
              >
                <div 
                  className={`max-w-[80%] rounded-lg p-3 ${
                    isUserRole(message.role) 
                      ? "bg-purple-500/20 text-purple-100" 
                      : "bg-gray-700/50 text-gray-100"
                  }`}
                >
                  {/* Message sender identifier with timestamp */}
                  <div className="flex items-center justify-between mb-1">
                    <div className="flex items-center">
                      {isUserRole(message.role) ? (
                        <>
                          <span className="text-xs font-medium">You</span>
                          <User className="w-3 h-3 ml-1" />
                        </>
                      ) : (
                        <>
                          <span className="text-xs font-medium">{agentName}</span>
                          <Bot className="w-3 h-3 ml-1" />
                        </>
                      )}
                    </div>
                    <div className="flex items-center text-xs text-gray-400">
                      <Clock className="w-3 h-3 mr-1" />
                      {formatTimestamp(message.timestamp)}
                    </div>
                  </div>
                  
                  {/* Message content */}
                  <div className="text-sm">
                    <EnhancedMarkdownContent 
                      content={message.content || ""}
                      customTheme={codeTheme}
                    />
                  </div>
                </div>
              </div>
            ))}
            
            {/* Current streaming response */}
            {currentResponse && (
              <div className="flex justify-start">
                <div className="max-w-[80%] rounded-lg p-3 bg-gray-700/50 text-gray-100">
                  <div className="flex items-center justify-between mb-1">
                    <div className="flex items-center">
                      <span className="text-xs font-medium">{agentName}</span>
                      <Bot className="w-3 h-3 ml-1" />
                    </div>
                    <div className="flex items-center text-xs text-gray-400">
                      <Clock className="w-3 h-3 mr-1" />
                      {formatTimestamp(new Date())}
                    </div>
                  </div>
                  
                  {/* Streaming response content */}
                  <div className="text-sm">
                    <EnhancedMarkdownContent 
                      content={currentResponse || ""}
                      customTheme={codeTheme}
                    />
                    
                    {/* Speaking animation cursor */}
                    {isSpeaking && (
                      <span className="inline-block ml-1 animate-pulse">▌</span>
                    )}
                  </div>
                </div>
              </div>
            )}
            
            {/* Auto-scroll reference element */}
            <div ref={messagesEndRef} />
          </div>
        )}
      </div>
      
      {/* Footer with transcript stats */}
      <div className="p-3 border-t border-gray-700 bg-gray-800 flex-shrink-0">
        <div className="text-xs text-gray-400 flex items-center justify-between">
          <span>
            {dialogue.length > 0 ? 
              `${dialogue.length} messages (${dialogue.reduce((sum, msg) => sum + (msg.content?.length || 0), 0)} total chars)` : 
              "No messages yet"}
          </span>
          <span>
            {isListening ? "Live transcription active" : "Disconnected"}
          </span>
        </div>
      </div>
    </div>
  );
}
