/**
 * Hook for capturing and storing document category state from the UI
 * This provides a reliable way to capture the exact category value from SelectedDocumentsDisplay.tsx
 * and make it available to webhooks via API endpoint
 */

import { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';

export interface DocumentCategoryState {
  userId: string;
  selectedDocuments: Array<{
    id: string;
    title: string;
    category: string;
  }>;
  primaryCategory: string | null;
  conversationId: string | null;
  agentType: string | null;
  timestamp: number;
}

/**
 * Hook to capture and manage document category state
 */
export function useDocumentCategoryCapture() {
  const { data: session } = useSession();
  const [categoryState, setCategoryState] = useState<DocumentCategoryState | null>(null);

  /**
   * Capture the current document selection and category state
   */
  const captureDocumentState = useCallback(async (
    selectedDocuments: Array<{ id: string; title: string; category: string }>,
    agentType?: string,
    conversationId?: string
  ) => {
    if (!session?.user?.email) {
      console.warn('[CATEGORY_CAPTURE] No user session available');
      return;
    }

    const primaryCategory = selectedDocuments.length > 0 ? selectedDocuments[0].category : null;
    
    const newState: DocumentCategoryState = {
      userId: session.user.email,
      selectedDocuments,
      primaryCategory,
      conversationId: conversationId || null,
      agentType: agentType || null,
      timestamp: Date.now()
    };

    setCategoryState(newState);

    // Store in API for webhook access - store under both actual user email and fallback
    const userIdsToStore = [
      session.user.email,
      '<EMAIL>' // Fallback user that webhook might use
    ].filter((id, index, arr) => id && arr.indexOf(id) === index); // Remove duplicates

    for (const userId of userIdsToStore) {
      try {
        const stateForUser = { ...newState, userId };
        const response = await fetch('/api/document-category-state', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(stateForUser)
        });

        if (!response.ok) {
          console.error('[CATEGORY_CAPTURE] Failed to store category state for userId:', userId, response.statusText);
        } else {
          console.log('[CATEGORY_CAPTURE] ✅ Category state stored successfully for userId:', userId, {
            primaryCategory,
            selectedDocumentsCount: selectedDocuments.length,
            agentType,
            conversationId
          });
        }
      } catch (error) {
        console.error('[CATEGORY_CAPTURE] Error storing category state for userId:', userId, error);
      }
    }
  }, [session?.user?.email]);

  /**
   * Update conversation ID when conversation starts
   */
  const updateConversationId = useCallback(async (conversationId: string) => {
    if (!categoryState || !session?.user?.email) return;

    const updatedState = {
      ...categoryState,
      conversationId,
      timestamp: Date.now()
    };

    setCategoryState(updatedState);

    try {
      await fetch('/api/document-category-state', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updatedState)
      });

      console.log('[CATEGORY_CAPTURE] ✅ Conversation ID updated:', conversationId);
    } catch (error) {
      console.error('[CATEGORY_CAPTURE] Error updating conversation ID:', error);
    }
  }, [categoryState, session?.user?.email]);

  /**
   * Clear the captured state
   */
  const clearCategoryState = useCallback(async () => {
    if (!session?.user?.email) return;

    setCategoryState(null);

    try {
      await fetch('/api/document-category-state', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId: session.user.email })
      });

      console.log('[CATEGORY_CAPTURE] ✅ Category state cleared');
    } catch (error) {
      console.error('[CATEGORY_CAPTURE] Error clearing category state:', error);
    }
  }, [session?.user?.email]);

  return {
    categoryState,
    captureDocumentState,
    updateConversationId,
    clearCategoryState
  };
}
