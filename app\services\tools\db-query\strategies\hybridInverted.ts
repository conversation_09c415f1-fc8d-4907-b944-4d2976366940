import type { QueryCodebaseRequest, StrategyRun } from '../types';
import { executeTwoStage } from './twoStage';

export const HybridInvertedStrategyId = 'hybrid-inverted' as const;

// Placeholder strategy that reuses two-stage execution for now.
// In future, integrate Meilisearch/Elastic for stage 1.
export async function executeHybridInverted(req: QueryCodebaseRequest): Promise<StrategyRun> {
  const run = await executeTwoStage(req);
  return { ...run, strategyId: HybridInvertedStrategyId };
}

