// Simplified toast implementation
import { useState, useEffect } from 'react';

type ToastAction = {
  label: string;
  onClick: () => void;
};

type ToastProps = {
  title?: string;
  description?: string;
  // success: green, warning: amber/orange, destructive: red, default: gray
  variant?: 'default' | 'success' | 'warning' | 'destructive';
  duration?: number;
  action?: ToastAction;
};

type ToastState = ToastProps & {
  id: string;
  visible: boolean;
};

let toasts: ToastState[] = [];
let listeners: ((toasts: ToastState[]) => void)[] = [];

const notifyListeners = () => {
  listeners.forEach(listener => listener([...toasts]));
};

export function toast(props: ToastProps) {
  const id = Math.random().toString(36).substring(2, 9);
  const newToast: ToastState = {
    ...props,
    id,
    visible: true,
  };

  toasts.push(newToast);
  notifyListeners();

  // Auto-dismiss after duration with fade-out
  const autoDuration = props.duration || 3000;
  setTimeout(() => {
    // First hide (for fade-out animation)
    toasts = toasts.map(t => (t.id === id ? { ...t, visible: false } : t));
    notifyListeners();
    // Remove after animation
    setTimeout(() => {
      toasts = toasts.filter(t => t.id !== id);
      notifyListeners();
    }, 300);
  }, autoDuration);

  return {
    id,
    dismiss: () => {
      // Hide then remove to allow fade-out
      toasts = toasts.map(t => (t.id === id ? { ...t, visible: false } : t));
      notifyListeners();
      setTimeout(() => {
        toasts = toasts.filter(t => t.id !== id);
        notifyListeners();
      }, 300);
    },
    update: (props: ToastProps) => {
      toasts = toasts.map(t => (t.id === id ? { ...t, ...props } : t));
      notifyListeners();
    },
  };
}

export function useToast() {
  const [currentToasts, setCurrentToasts] = useState<ToastState[]>(toasts);

  useEffect(() => {
    const listener = (updatedToasts: ToastState[]) => {
      setCurrentToasts(updatedToasts);
    };

    listeners.push(listener);
    return () => {
      listeners = listeners.filter(l => l !== listener);
    };
  }, []);

  return {
    toast,
    toasts: currentToasts,
    dismiss: (id: string) => {
      toasts = toasts.filter(t => t.id !== id);
      notifyListeners();
    },
    dismissAll: () => {
      toasts = [];
      notifyListeners();
    },
  };
}
