// app/api/generate-title/route.ts
import { NextResponse } from 'next/server';
import { processWithGroq } from '../../../lib/tools/groq-ai';

export async function POST(req: Request) {
  try {
    const { query, category }: { query?: string; category?: string } = await req.json();
    if (!query || !query.trim()) {
      return NextResponse.json({ error: 'Missing query' }, { status: 400 });
    }

    // Prompt to create a short, professional document title from a user query
    const prompt = `You are a helpful assistant that converts a user query into a concise, professional, title-cased document title.

- Keep it under 12 words (ideally 6–10).
- Use Title Case.
- Do not include quotes, emojis, brackets, or trailing punctuation.
- Avoid leading words like "Create", "Generate", "Document", "Explain".
- If a category is provided, you may incorporate it naturally.

User Query: "${query}"
${category ? `Context Category: ${category}` : ''}

Return only the title text.`;

    let raw = '';

    try {
      console.log('generate-title: Attempting with llama-3.3-70b-versatile');
      raw = await processWithGroq({
        prompt,
        model: 'llama-3.3-70b-versatile',
        modelOptions: { temperature: 0.3, maxTokens: 128 }
      });
      console.log('generate-title: Raw response from Groq (llama):', raw);
    } catch (llamaError) {
      console.log('generate-title: Llama model failed:', llamaError);

      // If llama fails, try with deepseek as fallback
      console.log('generate-title: Trying fallback model deepseek-r1-distill-llama-70b');
      try {
        raw = await processWithGroq({
          prompt,
          model: 'deepseek-r1-distill-llama-70b',
          modelOptions: { temperature: 0.3, maxTokens: 128 }
        });
        console.log('generate-title: Fallback model response:', raw);
      } catch (fallbackError) {
        console.log('generate-title: Fallback model also failed:', fallbackError);
        raw = ''; // Ensure raw is empty so we fall back to the manual title generation
      }
    }

    // Basic cleanup: take first line, strip quotes and excessive whitespace
    raw = (raw || '').split('\n')[0].trim();
    let title = raw.replace(/^"|"$/g, '').replace(/^'|'$/g, '').replace(/[\t\r\n]+/g, ' ').trim();
    // Remove enclosing markdown formatting if any
    title = title.replace(/^#+\s*/, '').replace(/[`*]+/g, '').trim();
    // Remove trailing punctuation
    title = title.replace(/[\s]+[\-–—]+\s*$/, '').replace(/[\s]*[\.!?:]+$/, '');

    console.log('generate-title: Cleaned title:', title);

    if (!title) {
      // Improved fallback: create a better title from the query
      let fallbackTitle = query.trim();

      // Remove common query prefixes
      fallbackTitle = fallbackTitle.replace(/^(explain|describe|summarize|analyze|show|find|create|generate|document|tell me about|what is|how does|how to)\s+/i, '');

      // Capitalize first letter and limit length
      fallbackTitle = fallbackTitle.charAt(0).toUpperCase() + fallbackTitle.slice(1);

      // Limit to reasonable length and add category context if available
      fallbackTitle = fallbackTitle.slice(0, 80);
      if (category && category !== 'Codebase') {
        fallbackTitle = `${category}: ${fallbackTitle}`;
      }

      title = fallbackTitle;
      console.log('generate-title: Using improved fallback title:', title);
    }

    console.log('generate-title: Final title being returned:', title);
    return NextResponse.json({ title });
  } catch (err: any) {
    console.error('generate-title error:', err);
    return NextResponse.json({ error: err?.message || 'Failed to generate title' }, { status: 500 });
  }
}

