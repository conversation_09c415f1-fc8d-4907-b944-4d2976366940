/**
 * Process PMO Document
 *
 * This file contains functions for processing PMO documents and storing them in Firebase.
 * It handles chunking the document content and storing it in the files collection and byteStoreCollection.
 */

import { RecursiveCharacterTextSplitter } from 'langchain/text_splitter';
import { OpenAIEmbeddings } from '@langchain/openai';
import { FirestoreStore } from '../FirestoreStore';
import { getStorage, ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { doc, setDoc, serverTimestamp } from 'firebase/firestore';
import { db } from '../../components/firebase';
import { adminDb, adminStorage } from '../../components/firebase/admin';
import { v4 as uuidv4 } from 'uuid';
import { getPineconeIndex } from '../pinecone-client';
import { pdfGeneratorTool, PdfContent } from '../tools/pdf-generator';

// Constants
const CHUNK_SIZE = 1000;
const CHUNK_OVERLAP = 200;

// Interface for document processing options
export interface ProcessPMODocumentOptions {
  title: string;
  content: string;
  pmoId: string;
  userId: string;
  category?: string;
  metadata?: Record<string, any>;
  agentId?: string; // Agent ID for files metadata
  documentId?: string; // Optional document ID to use instead of generating new one
  useUserPath?: boolean; // Whether to use userId path instead of sysAdmin
}

// Interface for document processing result
export interface ProcessPMODocumentResult {
  success: boolean;
  documentId?: string;
  downloadUrl?: string;
  error?: string;
}

/**
 * Clean metadata by removing undefined values
 */
function cleanMetadata(metadata: Record<string, any>): Record<string, any> {
  const cleanedMetadata: Record<string, any> = {};

  Object.entries(metadata).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      cleanedMetadata[key] = value;
    }
  });

  return cleanedMetadata;
}

/**
 * Clean content by removing DB Query metadata and technical sections
 */
function cleanContentForPDF(content: string): string {
  let cleanedContent = content;

  // Remove DB Query Result section (everything from "DB Query Result" to "Answer")
  cleanedContent = cleanedContent.replace(
    /DB Query Result[\s\S]*?(?=Answer|$)/i,
    ''
  );

  // Remove Top Matches section (everything from "Top Matches" to the next major section)
  cleanedContent = cleanedContent.replace(
    /Top Matches[\s\S]*?(?=##|$)/i,
    ''
  );

  // Remove technical metadata lines (UUIDs, scores, etc.)
  cleanedContent = cleanedContent.replace(
    /^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}.*$/gm,
    ''
  );

  // Remove score lines
  cleanedContent = cleanedContent.replace(
    /.*— score \d+\.\d+.*$/gm,
    ''
  );

  // Remove provider/model lines
  cleanedContent = cleanedContent.replace(
    /Provider\/Model:.*$/gm,
    ''
  );

  // Remove total matches/time lines
  cleanedContent = cleanedContent.replace(
    /Total (matches|time):.*$/gm,
    ''
  );

  // Remove mode/strategy/namespaces lines
  cleanedContent = cleanedContent.replace(
    /^(Mode|Strategy|Namespaces):.*$/gm,
    ''
  );

  // Clean up multiple consecutive newlines
  cleanedContent = cleanedContent.replace(/\n{3,}/g, '\n\n');

  // Trim whitespace
  cleanedContent = cleanedContent.trim();

  return cleanedContent;
}

/**
 * Process a PMO document and store it in Firebase
 */
export async function processPMODocument(options: ProcessPMODocumentOptions): Promise<ProcessPMODocumentResult> {
  try {
    // Use provided document ID or generate a new one
    const documentId = options.documentId || uuidv4();
    // Ensure the filename has a .pdf extension and is sanitized for storage
    const sanitizedTitle = options.title.replace(/[^a-zA-Z0-9_-]/g, '_').substring(0, 50);
    const fileName = `PMO_Requirements_${sanitizedTitle}_${options.pmoId}_${documentId}.pdf`;

    // Determine which user path to use for storage
    const sysAdmin = process.env.NEXT_PUBLIC_SYS_ADMIN || '<EMAIL>';
    const storageUserId = options.useUserPath ? options.userId : sysAdmin;

    // Clean content before creating PDF
    const cleanedContent = cleanContentForPDF(options.content);
    console.log('[PROCESS_PMO_DOC] Content cleaned, length:', cleanedContent.length);

    // Create PDF buffer from cleaned content
    console.log('[PROCESS_PMO_DOC] Creating PDF buffer...');
    const pdfBuffer = await createPDFBuffer(options.title, cleanedContent);
    console.log('[PROCESS_PMO_DOC] PDF buffer created, size:', pdfBuffer.length);

    // Upload PDF to Firebase Storage using Admin SDK for server-side operations
    const storagePath = `uploads/${storageUserId}/${documentId}.pdf`;
    console.log('[PROCESS_PMO_DOC] Uploading to Firebase Storage (Admin):', storagePath);

    // Use Firebase Admin Storage for server-side upload
    const bucket = adminStorage.bucket();
    const file = bucket.file(storagePath);

    await file.save(pdfBuffer, {
      metadata: {
        contentType: 'application/pdf',
        metadata: {
          title: options.title,
          category: options.category || 'PMO',
          generatedBy: 'PMOAgent',
          pmoId: options.pmoId
        }
      }
    });
    console.log('[PROCESS_PMO_DOC] PDF uploaded successfully');

    // Generate download URL
    const [downloadUrl] = await file.getSignedUrl({
      action: 'read',
      expires: '03-01-2500' // Long expiration
    });
    console.log('[PROCESS_PMO_DOC] Download URL generated:', downloadUrl);

    // Store metadata in files collection using Admin SDK
    // ✅ CRITICAL: Only include agentId if it's a valid ElevenLabs agent ID
    // Check for undefined, null, empty string, or 'unknown' values
    const validAgentId = options.agentId &&
                        typeof options.agentId === 'string' &&
                        options.agentId.trim() !== '' &&
                        options.agentId !== 'unknown' ? options.agentId : null;

    console.log('[PROCESS_PMO_DOC] 🔍 CRITICAL DEBUG - Agent ID being stored in Firestore:', {
      documentId,
      storageUserId,
      category: options.category,
      agentId: validAgentId,
      originalAgentId: options.agentId,
      title: options.title,
      agentIdType: typeof options.agentId,
      agentIdLength: options.agentId?.length,
      willStoreAgentId: !!validAgentId,
      firestorePath: `users/${storageUserId}/files/${documentId}`
    });

    // ✅ Build metadata object with conditional agentId
    // Filter out undefined values from options.metadata to prevent Firestore errors
    const cleanedOptionsMetadata = options.metadata ?
      Object.fromEntries(Object.entries(options.metadata).filter(([_, value]) => value !== undefined)) : {};

    console.log('[PROCESS_PMO_DOC] 🔍 CRITICAL CATEGORY TRACE - Category received by processPMODocument:', {
      receivedCategory: options.category,
      fallbackCategory: 'PMO',
      finalCategoryForFiles: options.category || 'PMO',
      title: options.title,
      source: 'processPMODocument function input'
    });

    const filesMetadata: any = {
      category: options.category || 'PMO',
      createdAt: new Date(), // Use regular Date for Admin SDK
      downloadUrl,
      name: fileName,
      namespace: documentId,
      ref: storagePath, // Use the same path with .pdf extension
      size: pdfBuffer.length,
      type: 'application/pdf',
      title: options.title,
      generatedBy: 'PMOAgent',
      pmoId: options.pmoId,
      ...cleanedOptionsMetadata
    };

    // ✅ Only include agentId if it's valid (prevents "unknown" from being stored)
    if (validAgentId) {
      filesMetadata.agentId = validAgentId;
      console.log('[PROCESS_PMO_DOC] 🔍 CRITICAL DEBUG - Including agentId in Firestore metadata:', validAgentId);
    } else {
      console.log('[PROCESS_PMO_DOC] 🔍 CRITICAL DEBUG - NOT including agentId in Firestore metadata (invalid/unknown)');
    }

    // ✅ Final safety check: Remove any undefined values that might have slipped through
    const cleanedFilesMetadata = Object.fromEntries(
      Object.entries(filesMetadata).filter(([_, value]) => value !== undefined)
    );

    console.log('[PROCESS_PMO_DOC] 🔍 CRITICAL CATEGORY TRACE - Final metadata being stored in files collection:', {
      category: cleanedFilesMetadata.category,
      title: cleanedFilesMetadata.title,
      agentId: cleanedFilesMetadata.agentId,
      originalOptionsCategory: options.category,
      firestorePath: `users/${storageUserId}/files/${documentId}`,
      allMetadataKeys: Object.keys(cleanedFilesMetadata)
    });

    await adminDb.collection(`users/${storageUserId}/files`).doc(documentId).set(cleanedFilesMetadata);
    console.log('[PROCESS_PMO_DOC] Files collection metadata stored successfully with final metadata:', {
      agentId: cleanedFilesMetadata.agentId,
      category: cleanedFilesMetadata.category,
      title: cleanedFilesMetadata.title,
      totalFields: Object.keys(cleanedFilesMetadata).length
    });

    // Process content for byteStoreCollection
    const textSplitter = new RecursiveCharacterTextSplitter({
      chunkSize: CHUNK_SIZE,
      chunkOverlap: CHUNK_OVERLAP,
      separators: ["\n\n", "\n", ". ", " ", ""]
    });

    const textChunks = await textSplitter.createDocuments([cleanedContent]);

    // Initialize Firestore store for byte storage
    const byteStoreCollection = `users/${storageUserId}/byteStoreCollection`;
    const firestoreStore = new FirestoreStore({ collectionPath: byteStoreCollection });

    // Initialize OpenAI embeddings
    const embeddings = new OpenAIEmbeddings({
      openAIApiKey: process.env.OPENAI_API_KEY
    });

    // Initialize Pinecone index
    const pineconeIndex = await getPineconeIndex();

    // Process each chunk
    for (let i = 0; i < textChunks.length; i++) {
      const chunk = textChunks[i];
      const chunkId = `${documentId}_${i + 1}`;

      // Create metadata for this chunk
      const chunkMetadata = cleanMetadata({
        doc_id: documentId,
        chunk_id: chunkId,
        document_title: options.title,
        category: options.category || 'PMO',
        file_type: 'application/pdf',
        position: i + 1,
        total_chunks: textChunks.length,
        is_summary: i === 0, // First chunk is considered the summary
        source_url: downloadUrl,
        processed_at: new Date().toISOString(),
        pmoId: options.pmoId,
        ...cleanedOptionsMetadata
      });

      // Store the raw chunk in Firestore
      await firestoreStore.mset([[chunkId, {
        pageContent: chunk.pageContent,
        metadata: chunkMetadata
      }]]);

      // Generate embedding for this chunk
      const embedding = await embeddings.embedQuery(chunk.pageContent);

      // Store the embedding in Pinecone
      await pineconeIndex.namespace(documentId).upsert([
        {
          id: chunkId,
          values: embedding,
          metadata: chunkMetadata
        }
      ]);
    }

    return {
      success: true,
      documentId,
      downloadUrl
    };
  } catch (error: any) {
    console.error('Error processing PMO document:', error);
    return {
      success: false,
      error: error.message || 'Failed to process PMO document'
    };
  }
}

/**
 * Create a PDF buffer from content
 * Uses the pdfGeneratorTool to create a PDF
 */
async function createPDFBuffer(title: string, content: string): Promise<Buffer> {
  try {
    // Create PDF content structure
    const pdfContent: PdfContent[] = [
      {
        title: title,
        content: content
      }
    ];

    // Generate PDF using the pdfGeneratorTool
    const pdfBuffer = await pdfGeneratorTool.generatePdf(pdfContent, {
      title: title,
      includeCover: true,
      includeToc: false,
      saveToByteStore: false // We'll handle storage ourselves
    });

    // The tool returns either a Buffer or a SavePdfToByteStoreResult
    // We need to ensure we're returning a Buffer
    if (Buffer.isBuffer(pdfBuffer)) {
      return pdfBuffer;
    } else {
      throw new Error('PDF generation returned a non-buffer result');
    }
  } catch (error) {
    console.error('Error creating PDF buffer:', error);

    // Fallback to creating a simple PDF using jsPDF if the PDF generator tool fails
    try {
      console.log('Attempting fallback PDF creation with jsPDF');

      // Dynamic import for jsPDF to handle server-side environment
      const jsPDF = (await import('jspdf')).jsPDF;
      const doc = new jsPDF();

      // Add title
      doc.setFontSize(16);
      doc.setFont("helvetica", "bold");
      doc.text(title, 20, 20);

      // Add content (simplified)
      doc.setFontSize(12);
      doc.setFont("helvetica", "normal");

      // Split content into lines to fit on page
      const contentLines = doc.splitTextToSize(content, 170);
      doc.text(contentLines, 20, 30);

      // Get PDF as buffer
      const pdfArrayBuffer = doc.output("arraybuffer");
      return Buffer.from(pdfArrayBuffer);
    } catch (fallbackError) {
      console.error('Fallback PDF creation also failed:', fallbackError);
      // Last resort: return a text buffer with PDF header to force PDF mime type
      const pdfHeader = "%PDF-1.4\n";
      const pdfContent = `${pdfHeader}${title}\n\n${content}`;
      return Buffer.from(pdfContent);
    }
  }
}
