/**
 * Test Firebase Admin configuration
 */

async function testFirebaseAdmin() {
  console.log('🔥 Testing Firebase Admin configuration...\n');

  try {
    // Test 1: Import Firebase admin
    console.log('📦 Test 1: Importing Firebase admin...');
    const { adminDb } = await import('../components/firebase-admin.js');
    console.log('✅ Firebase admin imported successfully');
    console.log('Admin DB type:', typeof adminDb);
    console.log('Admin DB constructor:', adminDb.constructor.name);

    // Test 2: Test basic Firestore operation
    console.log('\n📝 Test 2: Testing basic Firestore operation...');
    const testCollection = 'test_collection';
    const testDoc = 'test_doc';
    
    // Try to write a test document
    const testData = {
      message: 'Hello Firebase!',
      timestamp: new Date(),
      testId: Math.random().toString(36).substring(7)
    };

    await adminDb.collection(testCollection).doc(testDoc).set(testData);
    console.log('✅ Test document written successfully');

    // Try to read the test document
    const docSnapshot = await adminDb.collection(testCollection).doc(testDoc).get();
    if (docSnapshot.exists) {
      const data = docSnapshot.data();
      console.log('✅ Test document read successfully:', data.message);
    } else {
      throw new Error('Test document not found after writing');
    }

    // Clean up test document
    await adminDb.collection(testCollection).doc(testDoc).delete();
    console.log('✅ Test document cleaned up successfully');

    console.log('\n🎉 Firebase Admin test completed successfully!');
    console.log('✅ Firebase is properly configured and accessible');

  } catch (error) {
    console.error('❌ Firebase Admin test failed:', error.message);
    console.error('Stack trace:', error.stack);
    
    // Provide troubleshooting guidance
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Check if service_key.json exists and has valid credentials');
    console.log('2. Verify Firebase project ID and permissions');
    console.log('3. Ensure FIREBASE_STORAGE_BUCKET environment variable is set');
    console.log('4. Check network connectivity to Firebase');
    
    process.exit(1);
  }
}

// Run the test
testFirebaseAdmin();
