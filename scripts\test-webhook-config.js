/**
 * Test script to verify ElevenLabs webhook configuration
 * This simulates what the webhook should send after our updates
 */

const testWebhookPayload = {
  // ✅ CRITICAL: These should now be populated by ElevenLabs dynamic variables
  agent_id: "agent_abc123456789",
  conversation_id: "conv_xyz987654321",
  user_id: "user_123",
  agent_name: "Marketing Director",
  
  // Standard webhook fields
  summary: "User discussed marketing strategy for new product launch, focusing on target demographics and budget allocation.",
  action_items: "1. Research competitor pricing\n2. Develop social media campaign\n3. Schedule follow-up meeting",
  document_title: "Marketing Strategy Discussion",
  generate_document: true,
  
  // Web search results (if any)
  web_search_results: [
    {
      title: "Digital Marketing Trends 2024",
      snippet: "Latest trends in digital marketing including AI-powered campaigns...",
      link: "https://example.com/marketing-trends",
      query: "digital marketing trends 2024"
    }
  ],
  search_queries: ["digital marketing trends 2024", "social media ROI"],
  research_findings: "Current market trends show increased focus on AI-powered personalization and video content.",
  
  // ✅ CRITICAL: Full conversation history should be included
  conversation_history: [
    {
      role: "user",
      content: "Hello, I need help developing a marketing strategy for our new product.",
      timestamp: "2024-01-15T10:00:00Z"
    },
    {
      role: "assistant", 
      content: "I'd be happy to help you develop a marketing strategy. Can you tell me more about your product and target audience?",
      timestamp: "2024-01-15T10:00:05Z"
    },
    {
      role: "user",
      content: "It's a productivity app for remote workers. We're targeting professionals aged 25-45.",
      timestamp: "2024-01-15T10:00:30Z"
    },
    {
      role: "assistant",
      content: "Great! For a productivity app targeting remote professionals, I'd recommend focusing on LinkedIn and professional networks. What's your budget range?",
      timestamp: "2024-01-15T10:00:45Z"
    }
  ],
  
  // Metadata for document categorization
  transcript_metadata: {
    agentType: "Marketing",
    documentCategory: "PMO - Marketing Strategy",
    selectedDocuments: ["marketing-guidelines-2024"]
  },
  
  // Client data from conversation initiation
  conversation_initiation_client_data: {
    documentCategory: "PMO - Marketing Strategy",
    agentType: "Marketing",
    selectedDocuments: ["marketing-guidelines-2024"]
  },
  
  // Dynamic variables from ElevenLabs
  dynamic_variables: {
    custom_field_1: "value1",
    custom_field_2: "value2"
  }
};

async function testWebhookConfiguration() {
  console.log('🧪 Testing ElevenLabs Webhook Payload Configuration...\n');
  
  // Test 1: Verify critical fields are present
  console.log('📋 Test 1: Checking critical fields...');
  const criticalFields = ['agent_id', 'conversation_id', 'summary', 'conversation_history'];
  const missingFields = criticalFields.filter(field => !testWebhookPayload[field]);
  
  if (missingFields.length === 0) {
    console.log('✅ All critical fields are present');
  } else {
    console.log('❌ Missing critical fields:', missingFields);
  }
  
  // Test 2: Verify conversation history structure
  console.log('\n📝 Test 2: Checking conversation history...');
  const history = testWebhookPayload.conversation_history;
  if (Array.isArray(history) && history.length > 0) {
    console.log(`✅ Conversation history has ${history.length} messages`);
    
    const hasValidStructure = history.every(msg => 
      msg.role && msg.content && msg.timestamp
    );
    
    if (hasValidStructure) {
      console.log('✅ All messages have valid structure (role, content, timestamp)');
    } else {
      console.log('❌ Some messages have invalid structure');
    }
  } else {
    console.log('❌ Conversation history is empty or invalid');
  }
  
  // Test 3: Verify agent context
  console.log('\n🤖 Test 3: Checking agent context...');
  const agentFields = ['agent_id', 'agent_name', 'transcript_metadata'];
  const hasAgentContext = agentFields.every(field => testWebhookPayload[field]);
  
  if (hasAgentContext) {
    console.log('✅ Agent context fields are present');
    console.log(`   Agent ID: ${testWebhookPayload.agent_id}`);
    console.log(`   Agent Name: ${testWebhookPayload.agent_name}`);
    console.log(`   Agent Type: ${testWebhookPayload.transcript_metadata.agentType}`);
  } else {
    console.log('❌ Missing agent context fields');
  }
  
  // Test 4: Show expected webhook URL
  console.log('\n🔗 Test 4: Webhook configuration...');
  const webhookUrl = process.env.NEXTAUTH_URL || 'https://your-domain.com';
  console.log(`Webhook URL: ${webhookUrl}/api/elevenlabs/save-meeting-summary-webhook`);
  console.log(`Auth Header: Bearer ${process.env.ELEVENLABS_WEBHOOK_SECRET || 'pmo-webhook-secret-2024'}`);
  
  // Test 5: Show payload size
  console.log('\n📊 Test 5: Payload analysis...');
  const payloadSize = JSON.stringify(testWebhookPayload).length;
  console.log(`Payload size: ${payloadSize} bytes`);
  console.log(`Conversation messages: ${testWebhookPayload.conversation_history.length}`);
  console.log(`Web search results: ${testWebhookPayload.web_search_results.length}`);
  
  console.log('\n🎯 Expected Results After Webhook Update:');
  console.log('1. ✅ agent_id and conversation_id will be populated by ElevenLabs');
  console.log('2. ✅ Full conversation_history will be included');
  console.log('3. ✅ Webhook can retrieve transcript from Firestore using agent_id');
  console.log('4. ✅ Generated PDFs will contain complete conversation');
  console.log('5. ✅ No more "SUMMARY_FALLBACK" in server logs');
  
  console.log('\n📋 Next Steps:');
  console.log('1. Deploy the updated create-agent API');
  console.log('2. Create a new agent or update existing agent configuration');
  console.log('3. Test with a real voice conversation');
  console.log('4. Check server logs for successful transcript retrieval');
}

// Run the test
testWebhookConfiguration();
