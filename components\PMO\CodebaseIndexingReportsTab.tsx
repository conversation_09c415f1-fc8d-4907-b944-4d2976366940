'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '../ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Download, FileText, Clock, Database, CheckCircle, AlertCircle, RefreshCw, Eye } from 'lucide-react';
import { useAuth } from '../../app/context/AuthContext';
import { collection, query, orderBy, getDocs, where } from 'firebase/firestore';
import { db } from '../../app/lib/firebase/config';
import { StoredCodebaseIndexingReport } from '../../lib/interfaces/CodebaseIndexingReport';
import CodebaseReportViewerModal from './CodebaseReportViewerModal';

interface CodebaseIndexingReportsTabProps {
  className?: string;
}

export function CodebaseIndexingReportsTab({ className }: CodebaseIndexingReportsTabProps) {
  const { user } = useAuth();
  const [reports, setReports] = useState<StoredCodebaseIndexingReport[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Modal state for viewing reports
  const [modalOpen, setModalOpen] = useState(false);
  const [selectedReportForViewing, setSelectedReportForViewing] = useState<StoredCodebaseIndexingReport | null>(null);

  // Map files collection doc -> StoredCodebaseIndexingReport shape expected by UI
  const mapFileDocToUi = (docId: string, v: any): StoredCodebaseIndexingReport => {
    // Prefer explicit indexingSessionId stored in doc; fallback to docId for legacy runs
    const indexingSessionId = v.indexingSessionId || docId;
    const totalFiles = Number(v.totalFiles || 0);
    // Prefer explicit totalChunks; if missing/zero but condensed analysis exists, derive it
    let totalChunks = Number(v.totalChunks || 0);
    if ((!totalChunks || isNaN(totalChunks)) && Array.isArray(v.fileAnalysisCondensed)) {
      totalChunks = v.fileAnalysisCondensed.reduce((sum: number, f: any) => sum + (Number(f?.chunkCount) || 0), 0);
    }
    const size = Number(v.size || 0);
    const processingTimeMs = Number(v.processingTimeMs || 0);
    const createdAt = String(v.createdAt || new Date().toISOString());
    const completedAt = String(v.completedAt || createdAt);

    return {
      id: docId,
      projectName: v.projectName || v.title || v.name || 'Codebase Documentation',
      userId: user?.email || '',
      indexingSessionId,
      category: v.category || '',
      createdAt,
      completedAt,
      processingTimeMs,
      selectedPaths: v.selectedPaths || [],
      statistics: {
        totalFiles,
        totalChunks,
        totalSize: size,
        averageChunkSize: totalChunks > 0 ? Math.round(size / totalChunks) : 0,
        processingTimePerFile: totalFiles > 0 ? Math.round(processingTimeMs / totalFiles) : 0,
      },
      // NEW: populate fileAnalysis if condensed data exists in files doc
      fileAnalysis: Array.isArray(v.fileAnalysisCondensed) ? v.fileAnalysisCondensed.map((f: any) => ({
        filePath: f.filePath,
        fileName: f.fileName,
        language: f.language,
        fileSize: f.fileSize,
        chunkCount: f.chunkCount,
        llmSummary: f.llmSummaryPreview || '',
        codeEntityType: f.codeEntityType || 'Unknown',
        definedEntities: Array.isArray(f.definedEntities) ? f.definedEntities : [],
        usedLibraries: Array.isArray(f.usedLibraries) ? f.usedLibraries : [],
        usedComponentsOrHooks: Array.isArray(f.usedComponentsOrHooks) ? f.usedComponentsOrHooks : [],
        imports: Array.isArray(f.imports) ? f.imports : [],
        exports: Array.isArray(f.exports) ? f.exports : [],
        apiEndpoints: Array.isArray(f.apiEndpoints) ? f.apiEndpoints : [],
        processingTimeMs: f.processingTimeMs || 0,
        success: !!f.success,
        errorMessage: f.errorMessage,
        chunks: []
      })) : [],
      vectorEmbedding: {
        pineconeNamespace: v.namespace || indexingSessionId,
        totalEmbeddings: totalChunks,
        embeddingModel: v.embeddingModel || 'text-embedding-3-small',
        embeddingDimensions: v.embeddingDimensions || 1536,
        indexingComplete: typeof v.indexingComplete === 'boolean' ? v.indexingComplete : true,
      },
      firebaseStorage: {
        reportDocumentId: indexingSessionId,
        reportPdfUrl: v.downloadUrl,
        storageComplete: Boolean(v.downloadUrl),
      },
      summary: {
        successfulFiles: typeof v.successfulFiles === 'number' ? v.successfulFiles : totalFiles,
        failedFiles: typeof v.failedFiles === 'number' ? v.failedFiles : 0,
        skippedFiles: typeof v.skippedFiles === 'number' ? v.skippedFiles : 0,
        errorMessages: Array.isArray(v.errorMessages) ? v.errorMessages : [],
        warnings: Array.isArray(v.warnings) ? v.warnings : [],
      },
    };
  };

  // Map legacy doc -> StoredCodebaseIndexingReport
  const mapLegacyDocToUi = (docId: string, data: any): StoredCodebaseIndexingReport => {
    return { ...(data as StoredCodebaseIndexingReport), id: docId };
  };

  const fetchReports = async () => {
    if (!user?.email) return;

    try {
      setLoading(true);
      setError(null);

      // 1) Primary: query files collection for codebase indexing reports
      const filesCol = collection(db, `users/${user.email}/files`);
      const filesQueryRef = query(
        filesCol,
        where('type', '==', 'application/pdf'),
        where('generatedBy', '==', 'CodebaseIndexingSystem'),
        where('reportType', '==', 'codebase-indexing-completion'),
        orderBy('completedAt', 'desc')
      );
      const filesSnap = await getDocs(filesQueryRef);

      const filesReports: StoredCodebaseIndexingReport[] = [];
      filesSnap.forEach((d) => {
        filesReports.push(mapFileDocToUi(d.id, d.data()));
      });

      // Build a set of indexingSessionIds we already have (doc ids)
      const haveIds = new Set(filesReports.map(r => r.indexingSessionId));

      // 2) Fallback: read legacy collection and merge only missing
      const legacyCol = collection(db, `users/${user.email}/codebase-indexing-reports`);
      const legacyQueryRef = query(legacyCol, orderBy('completedAt', 'desc'));
      const legacySnap = await getDocs(legacyQueryRef);

      const merged: StoredCodebaseIndexingReport[] = [...filesReports];
      legacySnap.forEach((d) => {
        const data = d.data() as StoredCodebaseIndexingReport;
        const legacyKey = data.indexingSessionId || d.id;
        if (!haveIds.has(legacyKey)) {
          merged.push(mapLegacyDocToUi(d.id, data));
        }
      });

      setReports(merged);
    } catch (err) {
      console.error('Error fetching codebase indexing reports:', err);
      setError('Failed to load indexing reports');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchReports();
  }, [user?.email]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const formatBytes = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const formatTime = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${(ms / 60000).toFixed(1)}m`;
  };

  const handleDownloadReport = (report: StoredCodebaseIndexingReport) => {
    if (report.firebaseStorage.reportPdfUrl) {
      window.open(report.firebaseStorage.reportPdfUrl, '_blank');
    }
  };

  const handleViewReport = (report: StoredCodebaseIndexingReport) => {
    setSelectedReportForViewing(report);
    setModalOpen(true);
  };

  const closeModal = () => {
    setModalOpen(false);
    setSelectedReportForViewing(null);
  };



  if (loading) {
    return (
      <div className="flex flex-col justify-center items-center py-12">
        <div className="flex items-center mb-4">
          <RefreshCw className="w-6 h-6 animate-spin text-blue-400 mr-3" />
          <span className="text-gray-300">Loading indexing reports...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-900/30 border border-red-700 rounded-lg p-6 text-center">
        <AlertCircle className="w-8 h-8 text-red-400 mx-auto mb-3" />
        <p className="text-red-300 font-semibold mb-3">Error loading reports:</p>
        <p className="text-red-200 mb-4">{error}</p>
        <Button
          onClick={fetchReports}
          className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md transition-colors"
        >
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <div className={`h-full flex flex-col ${className}`}>
      {/* Modal for viewing report content */}
      <CodebaseReportViewerModal
        isOpen={modalOpen}
        onClose={closeModal}
        report={selectedReportForViewing}
      />

      <div className="flex justify-between items-center p-4 border-b border-gray-700">
        <div>
          <p className="text-gray-400 text-sm">
            Completion reports for codebase onboarding and enrichment processes
          </p>
        </div>
        <Button
          onClick={fetchReports}
          size="sm"
          className="flex items-center px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors"
        >
          <RefreshCw className="w-3 h-3 mr-1" />
          Refresh
        </Button>
      </div>

      <div className="flex-1 overflow-y-auto p-4 space-y-4">

      {reports.length === 0 ? (
        <Card className="bg-gray-800 border-gray-700">
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Database className="w-12 h-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-semibold text-gray-300 mb-2">No Indexing Reports Found</h3>
            <p className="text-gray-400 text-center max-w-md">
              Codebase indexing reports will appear here after you complete the Path A: Codebase Onboarding & Enrichment workflow.
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-6">
          {reports.map((report) => (
            <Card key={report.id} className="bg-gray-800 border-gray-700 hover:border-gray-600 transition-colors">
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-white text-xl mb-2">
                      {report.projectName}
                    </CardTitle>
                    <CardDescription className="text-gray-400">
                      Indexing Session: {report.indexingSessionId}
                    </CardDescription>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span
                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        report.vectorEmbedding.indexingComplete
                          ? "bg-green-100 text-green-800 border border-green-200"
                          : "bg-red-100 text-red-800 border border-red-200"
                      }`}
                    >
                      {report.vectorEmbedding.indexingComplete ? (
                        <>
                          <CheckCircle className="w-3 h-3 mr-1" />
                          Complete
                        </>
                      ) : (
                        <>
                          <AlertCircle className="w-3 h-3 mr-1" />
                          Failed
                        </>
                      )}
                    </span>
                    <div className="flex items-center space-x-1">
                      <Button
                        onClick={() => handleViewReport(report)}
                        size="sm"
                        variant="outline"
                        className="bg-gray-700 hover:bg-gray-600 text-white border-gray-600"
                        title="View Report"
                      >
                        <Eye className="w-4 h-4" />
                      </Button>
                      {report.firebaseStorage.reportPdfUrl && (
                        <Button
                          onClick={() => handleDownloadReport(report)}
                          size="sm"
                          className="bg-blue-600 hover:bg-blue-700 text-white"
                          title="Download PDF"
                        >
                          <Download className="w-4 h-4" />
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                  <div className="bg-gray-700 rounded-lg p-3">
                    <div className="flex items-center mb-1">
                      <FileText className="w-4 h-4 text-blue-400 mr-2" />
                      <span className="text-sm text-gray-300">Files</span>
                    </div>
                    <p className="text-lg font-semibold text-white">{report.statistics.totalFiles}</p>
                  </div>
                  <div className="bg-gray-700 rounded-lg p-3">
                    <div className="flex items-center mb-1">
                      <Database className="w-4 h-4 text-green-400 mr-2" />
                      <span className="text-sm text-gray-300">Chunks</span>
                    </div>
                    <p className="text-lg font-semibold text-white">{report.statistics.totalChunks}</p>
                  </div>
                  <div className="bg-gray-700 rounded-lg p-3">
                    <div className="flex items-center mb-1">
                      <Clock className="w-4 h-4 text-yellow-400 mr-2" />
                      <span className="text-sm text-gray-300">Duration</span>
                    </div>
                    <p className="text-lg font-semibold text-white">{formatTime(report.processingTimeMs)}</p>
                  </div>
                  <div className="bg-gray-700 rounded-lg p-3">
                    <div className="flex items-center mb-1">
                      <FileText className="w-4 h-4 text-purple-400 mr-2" />
                      <span className="text-sm text-gray-300">Size</span>
                    </div>
                    <p className="text-lg font-semibold text-white">{formatBytes(report.statistics.totalSize)}</p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="text-gray-400">Completed:</span>
                    <p className="text-white">{formatDate(report.completedAt)}</p>
                  </div>
                  <div>
                    <span className="text-gray-400">Success Rate:</span>
                    <p className="text-white">
                      {Math.round((report.summary.successfulFiles / report.statistics.totalFiles) * 100)}%
                      <span className="text-gray-400 ml-1">
                        ({report.summary.successfulFiles}/{report.statistics.totalFiles})
                      </span>
                    </p>
                  </div>
                  <div>
                    <span className="text-gray-400">Embeddings:</span>
                    <p className="text-white">{report.vectorEmbedding.totalEmbeddings} vectors</p>
                  </div>
                </div>

                {(report.summary.warnings.length > 0 || report.summary.errorMessages.length > 0) && (
                  <div className="mt-4 p-3 bg-yellow-900/30 border border-yellow-700 rounded-lg">
                    <p className="text-yellow-300 text-sm font-medium mb-2">Issues Detected:</p>
                    {report.summary.warnings.length > 0 && (
                      <p className="text-yellow-200 text-sm">
                        {report.summary.warnings.length} warning(s)
                      </p>
                    )}
                    {report.summary.errorMessages.length > 0 && (
                      <p className="text-red-200 text-sm">
                        {report.summary.errorMessages.length} error(s)
                      </p>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}
      </div>
    </div>
  );
}
