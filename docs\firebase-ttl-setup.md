# Firebase Collections Setup and TTL Configuration

## 🎯 Overview

Both the transcript state API and document category state API now use Firebase Firestore for persistent storage. This guide covers setting up the required collections and configuring TTL policies to automatically delete old data.

## 🚀 Quick Setup

### Option 1: Automated Setup Script (Recommended)
```bash
# Run the setup script to create collections automatically
node scripts/setup-firebase-collections.js
```

This script will:
- ✅ Create both required collections (`transcript_sessions` and `document_category_sessions`)
- ✅ Add sample documents to initialize the collections
- ✅ Test read/write operations
- ✅ Set up data for your user (<EMAIL>)
- ✅ Configure multiple storage keys for reliability

### Option 2: Manual Setup

#### Step 1: Access Firebase Console
1. Go to your [Firebase Console](https://console.firebase.google.com/)
2. Select your project
3. Navigate to **Firestore Database**

#### Step 2: Create Required Collections
1. Click **"Start collection"**
2. Create collection: `transcript_sessions`
3. Add a sample document (see structure below)
4. Create collection: `document_category_sessions`
5. Add a sample document (see structure below)

## ⏰ TTL Policy Configuration

### For transcript_sessions Collection
1. In the Firestore Database section, click on the **transcript_sessions** collection
2. Click the **Settings** (gear icon) in the top right
3. Select **"Configure TTL"** or **"Time-to-live"**
4. Configure with these settings:
   - **TTL Field**: `createdAt`
   - **Duration**: `1 day` (24 hours)
5. Click **"Save"**

### For document_category_sessions Collection
1. Click on the **document_category_sessions** collection
2. Click the **Settings** (gear icon) in the top right
3. Select **"Configure TTL"** or **"Time-to-live"**
4. Configure with these settings:
   - **TTL Field**: `createdAt`
   - **Duration**: `1 day` (24 hours)
5. Click **"Save"**

**Important**: It may take up to 24 hours for TTL policies to become active.

## Collection Structure

The transcript sessions are now stored in Firestore with a hierarchical structure for better organization:

```
Collection Path: users/{userId}/agent/{conversationId}/transcript_sessions
Document ID: {userId|agentId|conversationId|fallback}
Document Data: {
  userId: string,
  agentId?: string,
  conversationId?: string,
  dialogue: TranscriptMessage[],
  currentResponse?: string,
  agentName?: string,
  agentType?: string,
  timestamp: number,
  createdAt: Date  // ← This field is used for TTL
}
```

## Storage Keys

The system stores transcript data under multiple document IDs for reliable webhook access:
- Primary user ID
- Agent ID (if available)
- Conversation ID (if available)  
- Fallback user ID (`<EMAIL>`)

## Benefits of This Approach

1. **Persistent Storage**: Unlike in-memory storage, Firestore persists data across server restarts
2. **Serverless Compatible**: Works perfectly with Vercel's serverless environment
3. **Automatic Cleanup**: TTL policy prevents database bloat
4. **Multiple Access Keys**: Ensures webhooks can find transcript data even with missing parameters
5. **Real-time Sync**: Changes are immediately available across all server instances

## Troubleshooting

### TTL Policy Not Working
- Verify the collection group name is exactly `transcript_sessions` (note: now using collection group for hierarchical structure)
- Ensure the timestamp field is exactly `createdAt`
- Wait up to 24 hours for the policy to activate
- Check that documents have the `createdAt` field with a valid Date value
- Note: With the new hierarchical structure, you'll need to set up TTL policies for collection groups rather than individual collections

### Webhook Can't Find Transcripts
- Verify the ElevenLabs webhook includes `agent_id` and `conversation_id`
- Check that the transcript-state API is storing data under the expected keys
- Review server logs for transcript retrieval attempts

## Related Files

- `app/api/transcript-state/route.ts` - Main API implementation
- `app/api/elevenlabs/save-meeting-summary-webhook/route.ts` - Webhook that retrieves transcripts
- `components/firebase/admin.ts` - Firebase admin configuration
