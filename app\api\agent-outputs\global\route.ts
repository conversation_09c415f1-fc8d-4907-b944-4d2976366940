import { NextRequest, NextResponse } from 'next/server';
import { adminDb } from '../../../../components/firebase/admin';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../auth/[...nextauth]/authOptions';

/**
 * Mapping from PMO agent types to actual stored agentType values in the database
 */
const PMO_AGENT_TYPE_MAPPING: Record<string, string[]> = {
  'Marketing': [
    'strategic-director',
    'research-insights',
    'content-creator',
    'social-media-orchestrator',
    'analytics-reporting'
  ],
  'BusinessAnalysis': [
    'BusinessAnalysis'
  ],
  'InvestigativeResearch': [
    'Investigative Research',
    'Investigative Research - Consolidated Report',
    'Investigative Research - Assessment',
    'Investigative Research - Journalist Report'
  ],
  'PMO': [
    'PMO',
    'PMO_Assessment_And_Requirements'
  ],
  'Research': [
    'Research',
    'research-team',
    'research-strategic-director'
  ],
  'SoftwareDesign': [
    'SoftwareDesign',
    'software-design'
  ],
  'DocumentationGeneration': [
    'Documentation Generation'
  ]
};

/**
 * API endpoint to fetch global agent outputs
 * This endpoint is used by client-side code to access the global Agent_Output collection
 * without importing Firebase Admin SDK directly in the browser
 */
export async function GET(req: NextRequest) {
  try {
    // Get the user's session for authentication
    const session = await getServerSession(authOptions);

    // Extract query parameters
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get('userId');
    const agentType = searchParams.get('agentType');

    console.log(`[Global Agent Outputs API] Request - userId: ${userId}, agentType: ${agentType}, session email: ${session?.user?.email}`);

    // Validate authentication and user ID
    if (!session?.user?.email) {
      console.log('[Global Agent Outputs API] No session or email found');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (!userId) {
      console.log('[Global Agent Outputs API] No userId parameter provided');
      return NextResponse.json({ error: 'userId parameter is required' }, { status: 400 });
    }

    // Ensure the user can only access their own data
    if (session.user.email !== userId) {
      console.log(`[Global Agent Outputs API] Access denied - session email: ${session.user.email}, requested userId: ${userId}`);
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Check if admin DB is available
    if (!adminDb) {
      console.log('[Global Agent Outputs API] Admin database not available');
      return NextResponse.json({
        success: false,
        error: 'Admin database not available',
        outputs: []
      });
    }

    // Create a reference to the global Agent_Output collection
    const agentOutputsRef = adminDb.collection('Agent_Output');

    // First, let's check what documents exist for this user
    console.log(`[Global Agent Outputs API] Checking documents for userId: ${userId}`);
    const userDocsQuery = agentOutputsRef.where('userId', '==', userId).limit(10);
    const userDocs = await userDocsQuery.get();
    console.log(`[Global Agent Outputs API] Found ${userDocs.docs.length} documents for user ${userId}`);

    if (!userDocs.empty) {
      const sampleDoc = userDocs.docs[0].data();
      console.log(`[Global Agent Outputs API] Sample document agentType: ${sampleDoc.agentType}`);
    }

    // Create a query - filter by userId first
    let q = agentOutputsRef.where('userId', '==', userId);

    // Add agent type filter if provided
    if (agentType) {
      console.log(`[Global Agent Outputs API] Adding agentType filter: ${agentType}`);

      // Map PMO agent type to actual stored agentType values
      const mappedAgentTypes = PMO_AGENT_TYPE_MAPPING[agentType];
      if (mappedAgentTypes && mappedAgentTypes.length > 0) {
        console.log(`[Global Agent Outputs API] Mapped ${agentType} to stored types: ${mappedAgentTypes.join(', ')}`);
        // Use 'in' operator to filter by multiple agentType values
        q = q.where('agentType', 'in', mappedAgentTypes);
      } else {
        // Fallback to exact match if no mapping found
        console.log(`[Global Agent Outputs API] No mapping found for ${agentType}, using exact match`);
        q = q.where('agentType', '==', agentType);
      }
    }

    // Order by timestamp descending and limit results
    q = q.orderBy('timestamp', 'desc').limit(100);

    // Get the documents
    console.log(`[Global Agent Outputs API] Executing query with userId filter...`);
    let outputDocs = await q.get();
    console.log(`[Global Agent Outputs API] Query with userId returned ${outputDocs.docs.length} documents`);

    // If no documents found with userId filter, try without userId filter (fallback)
    if (outputDocs.empty && agentType) {
      console.log(`[Global Agent Outputs API] No documents found with userId filter, trying without userId filter...`);

      const mappedAgentTypes = PMO_AGENT_TYPE_MAPPING[agentType];
      let fallbackQuery;

      if (mappedAgentTypes && mappedAgentTypes.length > 0) {
        console.log(`[Global Agent Outputs API] Fallback query using mapped types: ${mappedAgentTypes.join(', ')}`);
        fallbackQuery = agentOutputsRef.where('agentType', 'in', mappedAgentTypes)
          .orderBy('timestamp', 'desc')
          .limit(100);
      } else {
        console.log(`[Global Agent Outputs API] Fallback query using exact match for: ${agentType}`);
        fallbackQuery = agentOutputsRef.where('agentType', '==', agentType)
          .orderBy('timestamp', 'desc')
          .limit(100);
      }

      outputDocs = await fallbackQuery.get();
      console.log(`[Global Agent Outputs API] Fallback query returned ${outputDocs.docs.length} documents`);
    }

    // Map the documents to agent outputs
    const outputs = outputDocs.docs.map(doc => {
      const data = doc.data();

      // Convert Firestore timestamps to ISO strings
      // Handle different timestamp formats (Firestore Timestamp vs plain object)
      let createdAt: string;
      if (data.timestamp && typeof data.timestamp.toDate === 'function') {
        // Firestore Timestamp object
        createdAt = data.timestamp.toDate().toISOString();
      } else if (data.timestamp && data.timestamp._seconds) {
        // Plain object with _seconds property
        createdAt = new Date(data.timestamp._seconds * 1000 + (data.timestamp._nanoseconds || 0) / 1000000).toISOString();
      } else if (data.createdAt && typeof data.createdAt.toDate === 'function') {
        // Fallback to createdAt field
        createdAt = data.createdAt.toDate().toISOString();
      } else if (data.createdAt && data.createdAt._seconds) {
        // Plain object createdAt with _seconds property
        createdAt = new Date(data.createdAt._seconds * 1000 + (data.createdAt._nanoseconds || 0) / 1000000).toISOString();
      } else {
        // Default to current time
        createdAt = new Date().toISOString();
      }
      const updatedAt = createdAt;

      // Extract title and content from the result structure
      const title = data.prompt?.substring(0, 100) || 'Agent Output';
      const content = data.result?.output || data.result?.message || '';

      return {
        id: doc.id,
        agentType: data.agentType || '',
        title,
        content,
        fileUrl: data.result?.documentUrl || undefined,
        createdAt,
        updatedAt,
        metadata: data.metadata || {},
        // Include additional fields from global collection
        pmoMetadata: data.pmoMetadata || undefined,
        category: data.category || data.metadata?.category || undefined,
        contextOptions: data.contextOptions || undefined,
        // Additional fields for voice meeting context
        summary: data.result?.summary || content.substring(0, 200) + '...',
        tags: data.tags || [],
        status: data.status,
        projectId: data.projectId || data.metadata?.pmoId,
        teamName: data.metadata?.teamName,
        assignedTeam: data.metadata?.assignedTeam
      };
    });

    console.log(`[Global Agent Outputs API] Returning ${outputs.length} outputs for agentType: ${agentType}`);

    return NextResponse.json({
      success: true,
      outputs
    });

  } catch (error) {
    console.error('Error fetching global agent outputs:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      outputs: []
    }, { status: 500 });
  }
}
