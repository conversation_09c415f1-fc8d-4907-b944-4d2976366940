"use client";

import React, { useCallback, useEffect, useMemo, useRef, useState } from "react";
import {
  collection,
  getDocs,
  query,
  where,
  limit as fsLimit,
  onSnapshot,
  getCountFromServer,
  DocumentData,
  Query,
  deleteDoc,
  doc,
} from "firebase/firestore";
import { db } from "components/firebase";
import { useSession, signIn } from "next-auth/react";
import {
  Database,
  Search,
  Download,
  ChevronRight,
  RefreshCcw,
  Loader2,
  Clipboard,
  ChevronLeft,
  Trash2,
  MoreVertical,
  Eye,
  CheckSquare,
  Square,
  AlertTriangle,
  FileText,
} from "lucide-react";

// Utility types
interface FileDoc {
  id: string; // documentId/namespace
  name: string;
  category?: string;
  namespace?: string;
  createdAt?: any;
  totalCount?: number; // Total count from Firestore for display purposes
  // Additional properties that may exist in the file documents
  size?: number;
  documentType?: string;
  type?: string;
  title?: string;
  recordTitle?: string;
  recordDescription?: string;
  recordStatus?: string;
  recordPriority?: string;
  pmoId?: string;
  agentId?: string;
  generatedBy?: string;
  generatedAt?: any;
  downloadUrl?: string;
  ref?: string;
  [key: string]: any; // Allow for additional dynamic properties
}

interface ByteDocSummary {
  docId: string;
  title: string;
  category?: string;
  chunkCount?: number;
}

interface ByteChunkDoc {
  id: string;
  content?: string;
  metadata?: any;
}

const SYS_ADMIN_EMAIL = "<EMAIL>"; // System admin can inspect any user

export default function FirestoreInspectorPage() {
  const { data: session, status } = useSession();

  // Tab state
  const [activeTab, setActiveTab] = useState<'bytestore' | 'files'>('bytestore');

  // Target user scope (default to current user; admins can change)
  const [targetUser, setTargetUser] = useState<string>("");
  const [tempUserInput, setTempUserInput] = useState<string>("");

  // Left panel state
  const [files, setFiles] = useState<FileDoc[]>([]);
  const [loadingFiles, setLoadingFiles] = useState<boolean>(false);
  const [filesError, setFilesError] = useState<string | null>(null);

  // Structured search state
  type SearchMode = 'namespace' | 'name' | 'category';
  const [searchMode, setSearchMode] = useState<SearchMode>('name');
  const [search, setSearch] = useState<string>("");

  // Sorting and filtering state for Files Collection Analysis
  const [sortBy, setSortBy] = useState<'name' | 'category' | 'createdAt'>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [letterFilter, setLetterFilter] = useState<string>('all'); // New state for letter filtering

  // Category mode results (from Firestore where query)
  const [categoryResults, setCategoryResults] = useState<FileDoc[] | null>(null);
  const [categoryLoading, setCategoryLoading] = useState<boolean>(false);
  const [categoryError, setCategoryError] = useState<string | null>(null);

  // Right panel state
  const [selectedDoc, setSelectedDoc] = useState<ByteDocSummary | null>(null);
  const [chunks, setChunks] = useState<ByteChunkDoc[]>([]);
  const [chunksLoading, setChunksLoading] = useState<boolean>(false);
  const [chunksError, setChunksError] = useState<string | null>(null);
  const [selectedChunkId, setSelectedChunkId] = useState<string | null>(null);

  // Subscription cleanup
  const unsubscribeRef = useRef<(() => void) | undefined>(undefined);

  // Files Collection Analysis state
  const [filesCollectionData, setFilesCollectionData] = useState<FileDoc[]>([]);
  const [filesCollectionLoading, setFilesCollectionLoading] = useState<boolean>(false);
  const [filesCollectionError, setFilesCollectionError] = useState<string | null>(null);
  const [selectedFiles, setSelectedFiles] = useState<Set<string>>(new Set());
  const [showDeleteModal, setShowDeleteModal] = useState<boolean>(false);
  const [deleteTarget, setDeleteTarget] = useState<{type: 'single' | 'bulk', files: FileDoc[]}>({type: 'single', files: []});
  const [isDeleting, setIsDeleting] = useState<boolean>(false);
  const [showFileModal, setShowFileModal] = useState<FileDoc | null>(null);

  // Dropdown menu state
  const [openDropdown, setOpenDropdown] = useState<string | null>(null);

  const isAuthenticated = status === "authenticated";
  const isSystemAdmin = session?.user?.email === SYS_ADMIN_EMAIL;

  // Initialize target user
  useEffect(() => {
    if (isAuthenticated && !targetUser) {
      setTargetUser(session!.user!.email || "");
      setTempUserInput(session!.user!.email || "");
    }
  }, [isAuthenticated, session, targetUser]);

  // Fetch documents list (from users/{user}/files for doc-level overview)
  const fetchFiles = useCallback(async (userEmail: string) => {
    setLoadingFiles(true);
    setFilesError(null);
    try {
      const filesRef = collection(db, "users", userEmail, "files");

      // First, get the total count
      const countSnapshot = await getCountFromServer(filesRef);
      const totalCount = countSnapshot.data().count;

      // Then fetch all documents (or a reasonable limit)
      // If there are more than 3000 files, we'll limit to 3000 for performance
      const limit = Math.min(totalCount, 3000);
      const q = query(filesRef, fsLimit(limit));
      const snap = await getDocs(q);

      const items: FileDoc[] = snap.docs.map((d) => {
        const data = d.data();
        return {
          id: (data.namespace as string) || d.id,
          name: (data.name as string) || data.title,
          category: data.category,
          namespace: data.namespace,
          createdAt: data.createdAt,
          totalCount, // Add total count for display purposes
        };
      });
      const unique = new Map<string, FileDoc>();
      for (const f of items) unique.set(f.id, f);
      setFiles(Array.from(unique.values()));
    } catch (err: any) {
      console.error("Failed to fetch files:", err);
      setFilesError(err.message || "Failed to load files");
    } finally {
      setLoadingFiles(false);
    }
  }, []);

  // Fetch files collection data for Files Collection Analysis tab
  const fetchFilesCollection = useCallback(async (userEmail: string) => {
    setFilesCollectionLoading(true);
    setFilesCollectionError(null);
    try {
      const filesRef = collection(db, "users", userEmail, "files");

      // First, get the total count
      const countSnapshot = await getCountFromServer(filesRef);
      const totalCount = countSnapshot.data().count;

      // Then fetch all documents (or a reasonable limit)
      // If there are more than 5000 files, we'll limit to 5000 for performance
      const limit = Math.min(totalCount, 5000);
      const q = query(filesRef, fsLimit(limit));
      const snap = await getDocs(q);

      const items: FileDoc[] = snap.docs.map((d) => {
        const data = d.data();
        return {
          id: d.id,
          name: (data.name as string) || data.title || d.id,
          category: data.category,
          namespace: data.namespace,
          createdAt: data.createdAt,
          totalCount, // Add total count to each item for display purposes
        };
      });
      setFilesCollectionData(items);
    } catch (err: any) {
      console.error("Failed to fetch files collection:", err);
      setFilesCollectionError(err.message || "Failed to load files collection");
    } finally {
      setFilesCollectionLoading(false);
    }
  }, []);

  // Auto-fetch data when target user or tab changes
  useEffect(() => {
    if (targetUser) {
      fetchFiles(targetUser);
    }
    if (activeTab === 'files') {
      fetchFilesCollection(targetUser);
    }
  }, [targetUser, fetchFiles, fetchFilesCollection, activeTab]);

  // Run Firestore category query when in category mode (for ByteStore doc list)
  useEffect(() => {
    if (searchMode !== 'category') {
      setCategoryResults(null);
      setCategoryError(null);
      setCategoryLoading(false);
      return;
    }
    const cat = search.trim();
    if (!cat) {
      setCategoryResults([]);
      return;
    }

    (async () => {
      setCategoryLoading(true);
      setCategoryError(null);
      try {
        const filesRef = collection(db, 'users', targetUser, 'files');
        const q = query(filesRef, where('category', '==', cat));
        const snap = await getDocs(q);
        const items: FileDoc[] = snap.docs.map((d) => {
          const data = d.data();
          return {
            id: (data.namespace as string) || d.id,
            name: (data.name as string) || data.title,
            category: data.category,
            createdAt: data.createdAt,
          };
        });
        const unique = new Map<string, FileDoc>();
        for (const f of items) unique.set(f.id, f);
        setCategoryResults(Array.from(unique.values()));
      } catch (err: any) {
        setCategoryError(err?.message || 'Category query failed');
        setCategoryResults([]);
      } finally {
        setCategoryLoading(false);
      }
    })();
  }, [search, targetUser, searchMode]);

  // Compute filtered files (for ByteStore doc list)
  const filteredFiles = useMemo(() => {
    const term = search.trim().toLowerCase();
    if (searchMode === 'category') {
      return (categoryResults || []);
    }
    if (!term) return files;
    return files.filter((f) => {
      if (searchMode === 'namespace') return (f.id || '').toLowerCase().includes(term);
      if (searchMode === 'name') return (f.name || '').toLowerCase().includes(term);
      return (
        (f.id || '').toLowerCase().includes(term) ||
        (f.name || '').toLowerCase().includes(term) ||
        (f.category || '').toLowerCase().includes(term)
      );
    });
  }, [files, search, categoryResults, searchMode]);

  // Delete single file using existing deletion architecture
  const deleteSingleFile = useCallback(async (file: FileDoc) => {
    if (!targetUser || !file.namespace) return;

    setIsDeleting(true);
    try {
      const response = await fetch('/api/deleteDocumentAndChats', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: targetUser,
          namespace: file.namespace,
        }),
      });

      if (response.ok) {
        // Update local state
        setFilesCollectionData(prev => prev.filter(f => f.id !== file.id));
        setSelectedFiles(prev => {
          const newSet = new Set(prev);
          newSet.delete(file.id);
          return newSet;
        });
      } else {
        const error = await response.json();
        setFilesCollectionError(error.error || 'Failed to delete file');
      }
    } catch (error: any) {
      setFilesCollectionError(error.message || 'Network error during deletion');
    } finally {
      setIsDeleting(false);
      setShowDeleteModal(false);
    }
  }, [targetUser]);

  // Delete multiple files using existing deletion architecture
  const deleteBulkFiles = useCallback(async (files: FileDoc[]) => {
    if (!targetUser) return;

    setIsDeleting(true);
    const errors: string[] = [];
    const successfulDeletions: string[] = [];

    try {
      for (const file of files) {
        if (!file.namespace) {
          errors.push(`${file.name}: No namespace found`);
          continue;
        }

        try {
          const response = await fetch('/api/deleteDocumentAndChats', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              userId: targetUser,
              namespace: file.namespace,
            }),
          });

          if (response.ok) {
            successfulDeletions.push(file.id);
          } else {
            const error = await response.json();
            errors.push(`${file.name}: ${error.error || 'Unknown error'}`);
          }
        } catch (error: any) {
          errors.push(`${file.name}: ${error.message || 'Network error'}`);
        }
      }

      // Update local state
      setFilesCollectionData(prev => prev.filter(f => !successfulDeletions.includes(f.id)));
      setSelectedFiles(new Set());

      if (errors.length > 0) {
        setFilesCollectionError(`Some deletions failed: ${errors.join(', ')}`);
      }
    } catch (error: any) {
      console.error('Error in bulk deletion:', error);
      setFilesCollectionError(error.message || 'Bulk deletion failed');
    } finally {
      setIsDeleting(false);
      setShowDeleteModal(false);
    }
  }, [targetUser]);

  // Get unique categories for filter buttons
  const uniqueCategories = useMemo(() => {
    const categories = new Set<string>();
    filesCollectionData.forEach(f => {
      if (f.category) categories.add(f.category);
    });
    return Array.from(categories).sort();
  }, [filesCollectionData]);

  // Compute filtered and sorted files for Files Collection Analysis tab
  const filteredFilesCollection = useMemo(() => {
    let filtered = filesCollectionData;

    // Apply category filter
    if (categoryFilter !== 'all') {
      filtered = filtered.filter(f => f.category === categoryFilter);
    }

    // Apply letter filter (filter by first letter of name)
    if (letterFilter !== 'all') {
      filtered = filtered.filter(f => {
        const fileName = f.name || f.id || '';
        return fileName.toLowerCase().startsWith(letterFilter.toLowerCase());
      });
    }

    // Apply search filter
    const term = search.trim().toLowerCase();
    if (term) {
      filtered = filtered.filter((f) => {
        if (searchMode === 'namespace') return (f.namespace || '').toLowerCase().includes(term);
        if (searchMode === 'name') return (f.name || '').toLowerCase().includes(term);
        if (searchMode === 'category') return (f.category || '').toLowerCase().includes(term);
        // default fallback
        return (
          (f.namespace || '').toLowerCase().includes(term) ||
          (f.name || '').toLowerCase().includes(term) ||
          (f.category || '').toLowerCase().includes(term)
        );
      });
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aVal: string | number = '';
      let bVal: string | number = '';

      switch (sortBy) {
        case 'name':
          aVal = (a.name || a.id || '').toLowerCase();
          bVal = (b.name || b.id || '').toLowerCase();
          break;
        case 'category':
          aVal = (a.category || '').toLowerCase();
          bVal = (b.category || '').toLowerCase();
          break;
        case 'createdAt':
          aVal = a.createdAt?.toMillis?.() || a.createdAt?.seconds * 1000 || 0;
          bVal = b.createdAt?.toMillis?.() || b.createdAt?.seconds * 1000 || 0;
          break;
      }

      if (sortOrder === 'asc') {
        return aVal < bVal ? -1 : aVal > bVal ? 1 : 0;
      } else {
        return aVal > bVal ? -1 : aVal < bVal ? 1 : 0;
      }
    });

    return filtered;
  }, [filesCollectionData, search, searchMode, categoryFilter, letterFilter, sortBy, sortOrder]);

  // Selection helpers for Files Collection Analysis
  const toggleFileSelection = useCallback((fileId: string) => {
    setSelectedFiles(prev => {
      const newSet = new Set(prev);
      if (newSet.has(fileId)) {
        newSet.delete(fileId);
      } else {
        newSet.add(fileId);
      }
      return newSet;
    });
  }, []);

  const selectAllFiles = useCallback(() => {
    setSelectedFiles(new Set(filteredFilesCollection.map(f => f.id)));
  }, [filteredFilesCollection]);

  const deselectAllFiles = useCallback(() => {
    setSelectedFiles(new Set());
  }, []);

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (!bytes) return 'Unknown';
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  // Format date
  const formatDate = (timestamp: any) => {
    if (!timestamp) return 'Unknown';
    let date: Date;
    if (timestamp.toDate) {
      date = timestamp.toDate();
    } else if (timestamp.seconds) {
      date = new Date(timestamp.seconds * 1000);
    } else {
      date = new Date(timestamp);
    }
    return date.toLocaleString();
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (openDropdown) {
        setOpenDropdown(null);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [openDropdown]);

  // Modal handlers
  const handleDeleteSingle = useCallback((file: FileDoc) => {
    setDeleteTarget({type: 'single', files: [file]});
    setShowDeleteModal(true);
  }, []);

  const handleDeleteBulk = useCallback(() => {
    const filesToDelete = filesCollectionData.filter(f => selectedFiles.has(f.id));
    setDeleteTarget({type: 'bulk', files: filesToDelete});
    setShowDeleteModal(true);
  }, [filesCollectionData, selectedFiles]);

  const confirmDelete = useCallback(() => {
    if (deleteTarget.type === 'single') {
      deleteSingleFile(deleteTarget.files[0]);
    } else {
      deleteBulkFiles(deleteTarget.files);
    }
  }, [deleteTarget, deleteSingleFile, deleteBulkFiles]);

  // Copy helper
  const copyToClipboard = useCallback(async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      alert("Copied to clipboard");
    } catch {}
  }, []);

  // Helper: get chunk count for a docId (tries multiple field paths)
  const getChunkCount = useCallback(async (userEmail: string, docId: string) => {
    const fieldsToTry = [
      "metadata.doc_id",
      "metadata.namespace",
      "metadata.documentId",
      "doc_id",
      "namespace",
      "metadata.fileId",
      "metadata.file_id",
    ];
    try {
      const col = collection(db, "users", userEmail, "byteStoreCollection");
      for (const field of fieldsToTry) {
        try {
          const q = query(col, where(field as any, "==", docId));
          const countSnap = await getCountFromServer(q as unknown as Query<DocumentData>);
          const count = (countSnap.data() as any).count || 0;
          if (count > 0) {
            return count;
          }
        } catch {}
      }
      return 0;
    } catch (e) {
      return 0;
    }
  }, []);

  // Select a document (namespace) and load its chunks
  const selectDocument = useCallback(
    async (docInfo: FileDoc) => {
      const summary: ByteDocSummary = {
        docId: docInfo.id,
        title: docInfo.name,
        category: docInfo.category,
      };
      setSelectedDoc(summary);
      setSelectedChunkId(null);
      setChunks([]);
      setChunksError(null);
      setChunksLoading(true);

      // Clean up previous subscription
      if (unsubscribeRef.current) {
        try { unsubscribeRef.current(); } catch {}
        unsubscribeRef.current = undefined;
      }

      try {
        // Fetch chunk count lazily
        getChunkCount(targetUser, docInfo.id).then((count) =>
          setSelectedDoc((prev) => (prev ? { ...prev, chunkCount: count } : prev))
        );

        const col = collection(db, "users", targetUser, "byteStoreCollection");
        const fieldsToTry = [
          "metadata.doc_id",
          "metadata.namespace",
          "metadata.documentId",
          "doc_id",
          "namespace",
          "metadata.fileId",
          "metadata.file_id",
        ];
        let firstSnap: any = null;
        for (const field of fieldsToTry) {
          try {
            const qAny = query(col, where(field as any, "==", docInfo.id));
            const snap = await getDocs(qAny);
            if (!snap.empty) {
              firstSnap = snap;
              // subscribe to updates
              unsubscribeRef.current = onSnapshot(qAny, (ss) => {
                const updated: ByteChunkDoc[] = ss.docs.map((d) => ({ id: d.id, ...(d.data() as any) }));
                setChunks(updated);
              });
              break;
            }
          } catch {}
        }
        if (firstSnap) {
          const chunkDocs: ByteChunkDoc[] = firstSnap.docs.map((d: any) => ({ id: d.id, ...(d.data() as any) }));
          setChunks(chunkDocs);
        } else {
          setChunks([]);
          setChunksError(`No chunks found for ${docInfo.id}`);
        }
      } catch (err: any) {
        setChunksError(err.message || "Failed to load chunks");
      } finally {
        setChunksLoading(false);
      }
    },
    [getChunkCount, targetUser]
  );

  // Cleanup subscription on unmount
  useEffect(() => {
    return () => {
      if (unsubscribeRef.current) {
        try { unsubscribeRef.current(); } catch {}
      }
    };
  }, []);

  // Export selected document (all currently loaded chunks) as JSON
  const handleExport = useCallback(() => {
    if (!selectedDoc) return;
    const data = {
      docId: selectedDoc.docId,
      title: selectedDoc.title,
      category: selectedDoc.category,
      chunks: chunks.map((c) => ({ id: c.id, content: c.content, metadata: c.metadata })),
    };
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: "application/json" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `${selectedDoc.docId}.json`;
    a.click();
    URL.revokeObjectURL(url);
  }, [selectedDoc, chunks]);

  // Render helpers
  const renderValue = (value: any, depth = 0) => {
    const pad = depth * 8;
    if (value === null || value === undefined) return <span className="text-zinc-400">null</span>;
    if (typeof value === "string") return <span className="break-words text-zinc-100">{value}</span>;
    if (typeof value === "number" || typeof value === "boolean") return <span className="text-emerald-300">{String(value)}</span>;
    if (Array.isArray(value)) {
      return (
        <div className="space-y-1">
          {value.map((v, i) => (
            <div key={i} style={{ paddingLeft: pad + 8 }} className="pl-2 border-l border-zinc-700">
              <span className="text-zinc-400 mr-2">[{i}]</span>
              {renderValue(v, depth + 1)}
            </div>
          ))}
        </div>
      );
    }
    if (typeof value === "object") {
      return (
        <div className="space-y-1">
          {Object.entries(value).map(([k, v]) => (
            <div key={k} style={{ paddingLeft: pad + 8 }} className="pl-2 border-l border-zinc-700">
              <span className="text-blue-300 mr-2">{k}</span>
              {renderValue(v, depth + 1)}
            </div>
          ))}
        </div>
      );
    }
    return <span className="text-zinc-400">{String(value)}</span>;
  };

  // Test basic functionality first
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-zinc-950 text-zinc-100 flex items-center justify-center p-8">
        <div className="bg-zinc-900 p-8 rounded-lg border border-zinc-700 max-w-lg w-full text-center">
          <Database className="mx-auto text-blue-400 mb-3" size={36} />
          <h1 className="text-2xl font-bold mb-2 text-white">Database Inspector</h1>
          <p className="text-zinc-400 mb-6">Please sign in to access the Firestore inspector.</p>
          <button
            onClick={() => signIn("google", { callbackUrl: "/services/tools/db" })}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-500 rounded-md"
          >
            Sign in with Google
          </button>
        </div>
      </div>
    );
  }

  return (
    <main className="min-h-screen bg-zinc-950 text-zinc-100 p-4 md:p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <Database className="text-blue-400" size={28} />
            <div>
              <h1 className="text-2xl font-bold text-white">Firestore DB Inspector</h1>
              <p className="text-sm text-zinc-400">
                Path: users/{targetUser || "?"}/{activeTab === 'bytestore' ? 'byteStoreCollection' : 'files'}
              </p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <button 
              onClick={() => targetUser && fetchFiles(targetUser)} 
              className="flex items-center gap-2 px-3 py-2 bg-zinc-800 hover:bg-zinc-700 rounded-md"
            >
              <RefreshCcw size={16} /> Refresh
            </button>
          </div>
        </div>

        {/* Target user selector for admins */}
        {isSystemAdmin && (
          <div className="bg-zinc-900 border border-zinc-700 rounded-md p-4 mb-4">
            <div className="flex items-center gap-2">
              <input
                type="text"
                placeholder="Enter user email to inspect"
                value={tempUserInput}
                onChange={(e) => setTempUserInput(e.target.value)}
                disabled={!isSystemAdmin}
                className="flex-1 bg-zinc-800 border border-zinc-600 rounded-md px-3 py-2 text-sm"
              />
              <button
                disabled={!isSystemAdmin}
                onClick={() => setTargetUser(tempUserInput.trim())}
                className={`px-3 py-2 rounded-md ${isSystemAdmin ? "bg-blue-600 hover:bg-blue-500" : "bg-zinc-700"}`}
              >
                Switch User
              </button>
            </div>
            {!isSystemAdmin && (
              <p className="text-xs text-zinc-500 mt-2">Admin access required to inspect other users</p>
            )}
          </div>
        )}

        {/* Tab Navigation */}
        <div className="bg-zinc-900 border border-zinc-700 rounded-md p-1 mb-4 flex">
          <button
            onClick={() => setActiveTab('bytestore')}
            className={`flex-1 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'bytestore'
                ? 'bg-blue-600 text-white'
                : 'text-zinc-400 hover:text-white hover:bg-zinc-800'
            }`}
          >
            ByteStore Collection Analysis
          </button>
          <button
            onClick={() => setActiveTab('files')}
            className={`flex-1 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'files'
                ? 'bg-blue-600 text-white'
                : 'text-zinc-400 hover:text-white hover:bg-zinc-800'
            }`}
          >
            Files Collection Analysis
          </button>
        </div>

        {/* Tab Content */}
        {activeTab === 'bytestore' && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Left: Document list */}
            <div className="md:col-span-1 bg-zinc-900 border border-zinc-700 rounded-md overflow-hidden">
              <div className="p-3 border-b border-zinc-800">
                <div className="flex items-center gap-2">
                  <Search size={16} className="text-zinc-400" />
                  <select
                    value={searchMode}
                    onChange={(e) => { setSearchMode(e.target.value as any); setCategoryResults(null); }}
                    className="px-2 py-2 bg-zinc-800 border border-amber-400 rounded-md text-sm italic text-amber-400"
                  >
                    <option value="name">Name</option>
                    <option value="category">Category</option>
                    <option value="namespace">Namespace</option>
                  </select>
                  <input
                    value={search}
                    onChange={(e) => setSearch(e.target.value)}
                    placeholder={searchMode === 'category' ? 'Enter category (exact match)' : (searchMode === 'namespace' ? 'Search namespace' : 'Search name')}
                    className="flex-1 bg-transparent outline-none text-sm"
                  />
                </div>
                {searchMode === 'category' && categoryLoading && (
                  <div className="text-xs text-zinc-400 mt-2 flex items-center gap-2"><Loader2 size={14} className="animate-spin" /> Searching…</div>
                )}
                {searchMode === 'category' && categoryError && (
                  <div className="text-xs text-red-400 mt-2">{categoryError}</div>
                )}
              </div>

              {loadingFiles && (
                <div className="p-4 text-sm text-zinc-400 flex items-center gap-2"><Loader2 className="animate-spin" size={16} /> Loading documents…</div>
              )}
              {filesError && (
                <div className="p-4 text-sm text-red-400">{filesError}</div>
              )}

              <div className="overflow-y-auto custom-scrollbar" style={{ maxHeight: "calc(100vh - 280px)" }}>
                {filteredFiles.map((f) => (
                  <button
                    key={f.id}
                    onClick={() => selectDocument(f)}
                    className={`w-full text-left px-4 py-3 border-b border-zinc-800 hover:bg-zinc-800/60 ${selectedDoc?.docId === f.id ? "bg-zinc-800" : ""}`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="min-w-0">
                        <div className="text-white font-medium text-sm truncate">{f.name || f.id}</div>
                        <div className="text-xs text-zinc-400 truncate">{f.id}</div>
                        {selectedDoc?.docId === f.id && selectedDoc?.chunkCount !== undefined && (
                          <div className="text-xs text-zinc-500 mt-1">Chunks: {selectedDoc.chunkCount}</div>
                        )}
                      </div>
                      <ChevronRight size={16} className="text-zinc-500" />
                    </div>
                  </button>
                ))}

                {!loadingFiles && filteredFiles.length === 0 && (
                  <div className="p-4 text-sm text-zinc-400">No documents found.</div>
                )}
              </div>

              {/* ByteStore Collection Summary */}
              {!loadingFiles && files.length > 0 && (
                <div className="border-t border-zinc-800 p-3 bg-zinc-900/50">
                  <div className="text-xs text-zinc-400 mb-2">Collection Summary</div>
                  <div className="space-y-1 text-xs">
                    <div className="flex justify-between">
                      <span className="text-zinc-500">Total Documents:</span>
                      <span className="text-white">
                        {files[0].totalCount ? files[0].totalCount : files.length}
                      </span>
                    </div>
                    {files[0].totalCount && files[0].totalCount > files.length && (
                      <div className="flex justify-between">
                        <span className="text-zinc-500">Loaded:</span>
                        <span className="text-yellow-400">{files.length}</span>
                      </div>
                    )}
                    <div className="flex justify-between">
                      <span className="text-zinc-500">Filtered:</span>
                      <span className="text-white">{filteredFiles.length}</span>
                    </div>
                  </div>

                  {/* Warning if showing limited results */}
                  {files[0].totalCount && files[0].totalCount > files.length && (
                    <div className="mt-2 p-2 bg-yellow-900/20 border border-yellow-600/30 rounded text-xs">
                      <div className="flex items-center gap-1 text-yellow-400">
                        <AlertTriangle size={12} />
                        <span>Showing {files.length} of {files[0].totalCount} documents</span>
                      </div>
                      <div className="text-yellow-300/80 mt-1">
                        Limited to {files.length} documents for performance
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Right: Field inspector */}
            <div className="md:col-span-2 bg-zinc-900 border border-zinc-700 rounded-md">
              {!selectedDoc ? (
                <div className="p-8 text-center text-zinc-400">Select a document to view its chunks and metadata.</div>
              ) : (
                <div className="p-4">
                  {/* Breadcrumb */}
                  <div className="flex items-center gap-2 text-xs text-zinc-500 mb-3">
                    <span>users</span>
                    <ChevronRight size={12} />
                    <span>{targetUser}</span>
                    <ChevronRight size={12} />
                    <span>byteStoreCollection</span>
                    <ChevronRight size={12} />
                    <span className="text-blue-400">{selectedDoc.docId}</span>
                  </div>

                  {/* Selected summary */}
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <div className="text-white text-lg font-semibold">{selectedDoc.title || selectedDoc.docId}</div>
                      <div className="text-xs text-zinc-500">Doc ID: {selectedDoc.docId} {selectedDoc.chunkCount !== undefined && `• ${selectedDoc.chunkCount} chunks`}</div>
                    </div>
                    <button onClick={handleExport} className="flex items-center gap-2 px-3 py-2 bg-zinc-800 hover:bg-zinc-700 rounded-md text-sm">
                      <Download size={14} className="text-green-400" /> Export
                    </button>
                  </div>

                  {/* Chunks list and inspector */}
                  <div className="grid grid-cols-1 xl:grid-cols-3 gap-4">
                    {/* Chunks list */}
                    <div className="xl:col-span-1 border-r border-zinc-800 overflow-y-auto custom-scrollbar" style={{ maxHeight: "calc(100vh - 340px)" }}>
                      {chunksLoading && (
                        <div className="p-4 text-sm text-zinc-400 flex items-center gap-2"><Loader2 className="animate-spin" size={16} /> Loading chunks…</div>
                      )}
                      {chunksError && <div className="p-4 text-sm text-red-400">{chunksError}</div>}
                      {chunks.map((c) => (
                        <button
                          key={c.id}
                          onClick={() => setSelectedChunkId(c.id)}
                          className={`w-full text-left px-4 py-3 border-b border-zinc-800 hover:bg-zinc-800/60 ${selectedChunkId === c.id ? "bg-zinc-800" : ""}`}
                        >
                          <div className="flex items-center justify-between">
                            <div className="min-w-0">
                              <div className="text-white text-sm font-medium">{c.id}</div>
                              <div className="text-xs text-zinc-500 truncate">{(c as any)?.metadata?.chunk_index !== undefined ? `Index: ${(c as any).metadata.chunk_index}` : ''}</div>
                            </div>
                            <ChevronRight size={16} className="text-zinc-500" />
                          </div>
                        </button>
                      ))}
                      {!chunksLoading && chunks.length === 0 && (
                        <div className="p-4 text-sm text-zinc-400">No chunks found.</div>
                      )}
                    </div>

                    {/* Inspector */}
                    <div className="xl:col-span-2 overflow-y-auto custom-scrollbar p-4" style={{ maxHeight: "calc(100vh - 340px)" }}>
                      {!selectedChunkId ? (
                        <div className="text-sm text-zinc-400">Select a chunk to view its content and metadata.</div>
                      ) : (
                        (() => {
                          const chunk = chunks.find((c) => c.id === selectedChunkId)!;
                          return (
                            <div className="space-y-4">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                  <button onClick={() => setSelectedChunkId(null)} className="px-2 py-1 bg-zinc-800 hover:bg-zinc-700 rounded-md text-xs flex items-center gap-1">
                                    <ChevronLeft size={14} /> Back
                                  </button>
                                  <h3 className="text-white font-semibold">Chunk: {chunk.id}</h3>
                                </div>
                                <div className="flex items-center gap-2">
                                  <button onClick={() => copyToClipboard(chunk.content || "")} className="px-2 py-1 bg-zinc-800 hover:bg-zinc-700 rounded-md text-xs flex items-center gap-1">
                                    <Clipboard size={14} /> Copy content
                                  </button>
                                  <button onClick={() => copyToClipboard(JSON.stringify(chunk.metadata || {}, null, 2))} className="px-2 py-1 bg-zinc-800 hover:bg-zinc-700 rounded-md text-xs flex items-center gap-1">
                                    <Clipboard size={14} /> Copy metadata
                                  </button>
                                </div>
                              </div>

                              <section>
                                <h4 className="text-sm text-zinc-400 mb-2">Content</h4>
                                <div className="bg-zinc-950 border border-zinc-800 rounded-md p-3 text-sm whitespace-pre-wrap">
                                  {chunk.content ? chunk.content : <span className="text-zinc-500">(empty)</span>}
                                </div>
                              </section>

                              <section>
                                <h4 className="text-sm text-zinc-400 mb-2">Metadata</h4>
                                <div className="bg-zinc-950 border border-zinc-800 rounded-md p-3 text-sm">
                                  {renderValue(chunk.metadata || {})}
                                </div>
                              </section>
                            </div>
                          );
                        })()
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Files Collection Analysis Tab */}
        {activeTab === 'files' && (
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
            {/* Left: Files list with search and selection */}
            <div className="lg:col-span-3 bg-zinc-900 border border-zinc-700 rounded-md overflow-hidden flex flex-col">
              <div className="p-6 border-b border-zinc-800 space-y-4">
                <div className="flex items-center gap-2">
                  <Search size={16} className="text-zinc-400" />
                  <select
                    value={searchMode}
                    onChange={(e) => setSearchMode(e.target.value as any)}
                    className="px-2 py-2 bg-zinc-800 border border-amber-400 rounded-md text-sm italic text-amber-400"
                  >
                    <option value="name">Name</option>
                    <option value="category">Category</option>
                    <option value="namespace">Namespace</option>
                  </select>
                  <input
                    value={search}
                    onChange={(e) => setSearch(e.target.value)}
                    placeholder={`Search by ${searchMode}`}
                    className="flex-1 bg-transparent outline-none text-sm"
                  />
                </div>

                {/* Sort and Filter Controls */}
                <div className="flex items-center gap-4 flex-wrap">
                  {/* Sort Controls */}
                  <div className="flex items-center gap-2">
                    <span className="text-xs text-zinc-400">Sort:</span>
                    <select
                      value={sortBy}
                      onChange={(e) => setSortBy(e.target.value as 'name' | 'category' | 'createdAt')}
                      className="px-2 py-1 bg-zinc-800 border border-zinc-700 rounded text-white text-xs"
                    >
                      <option value="name">Name</option>
                      <option value="category">Category</option>
                      <option value="createdAt">Date</option>
                    </select>
                    <button
                      onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                      className="px-2 py-1 bg-zinc-800 hover:bg-zinc-700 border border-zinc-700 rounded text-white text-xs"
                      title={`Sort ${sortOrder === 'asc' ? 'Descending' : 'Ascending'}`}
                    >
                      {sortOrder === 'asc' ? '↑' : '↓'}
                    </button>
                  </div>

                  {/* Category Filter */}
                  <div className="flex items-center gap-2">
                    <span className="text-xs text-zinc-400">Filter:</span>
                    <select
                      value={categoryFilter}
                      onChange={(e) => setCategoryFilter(e.target.value)}
                      className="px-2 py-1 bg-zinc-800 border border-zinc-700 rounded text-white text-xs"
                    >
                      <option value="all">All Categories</option>
                      {uniqueCategories.map(category => (
                        <option key={category} value={category}>{category}</option>
                      ))}
                    </select>
                  </div>

                </div>

                {/* Quick Letter Filter Buttons */}
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <span className="text-xs text-zinc-400">Filter by Name:</span>
                    <button
                      onClick={() => setLetterFilter('all')}
                      className={`px-3 py-1 rounded text-xs font-medium ${
                        letterFilter === 'all'
                          ? 'bg-blue-600 text-white'
                          : 'bg-zinc-800 hover:bg-zinc-700 text-zinc-300'
                      }`}
                    >
                      All
                    </button>
                  </div>
                  <div className="flex items-center gap-1 flex-wrap">
                    {['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z'].map(letter => (
                      <button
                        key={letter}
                        onClick={() => setLetterFilter(letter)}
                        className={`px-2 py-1 rounded text-xs ${
                          letterFilter === letter
                            ? 'bg-blue-600 text-white'
                            : 'bg-zinc-800 hover:bg-zinc-700 text-zinc-300'
                        }`}
                        title={`Filter files starting with "${letter.toUpperCase()}"`}
                      >
                        {letter.toUpperCase()}{letter.toLowerCase()}
                      </button>
                    ))}
                  </div>
                </div>

                {/* Selection controls */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <button
                      onClick={selectAllFiles}
                      className="px-2 py-1 bg-zinc-800 hover:bg-zinc-700 rounded text-xs"
                    >
                      Select All
                    </button>
                    <button
                      onClick={deselectAllFiles}
                      className="px-2 py-1 bg-zinc-800 hover:bg-zinc-700 rounded text-xs"
                    >
                      Deselect All
                    </button>
                    <span className="text-xs text-zinc-400">
                      {selectedFiles.size} selected
                    </span>
                  </div>

                  {selectedFiles.size > 0 && (
                    <button
                      onClick={handleDeleteBulk}
                      disabled={isDeleting}
                      className="flex items-center gap-1 px-3 py-1 bg-red-600 hover:bg-red-700 disabled:bg-red-800 rounded text-xs"
                    >
                      <Trash2 size={12} />
                      Delete Selected ({selectedFiles.size})
                    </button>
                  )}
                </div>
              </div>

              {filesCollectionLoading && (
                <div className="p-4 text-sm text-zinc-400 flex items-center gap-2">
                  <Loader2 className="animate-spin" size={16} /> Loading files...
                </div>
              )}

              {filesCollectionError && (
                <div className="p-4 text-sm text-red-400">{filesCollectionError}</div>
              )}

              <div className="overflow-y-auto flex-1 custom-scrollbar" style={{ maxHeight: "calc(100vh - 380px)" }}>
                {filteredFilesCollection.map((file) => (
                  <div
                    key={file.id}
                    className={`flex items-center gap-3 p-3 border-b border-zinc-800 hover:bg-zinc-800/60 ${
                      selectedFiles.has(file.id) ? 'bg-zinc-800' : ''
                    }`}
                  >
                    <button
                      onClick={() => toggleFileSelection(file.id)}
                      className="text-blue-400 hover:text-blue-300"
                    >
                      {selectedFiles.has(file.id) ? (
                        <CheckSquare size={16} />
                      ) : (
                        <Square size={16} />
                      )}
                    </button>

                    <div className="flex-1 min-w-0">
                      <div className="text-white font-medium text-sm truncate">
                        {file.name || file.id}
                      </div>
                      <div className="text-xs text-zinc-400 truncate">
                        Category: {file.category || 'None'} • Namespace: {file.namespace || 'None'}
                      </div>
                    </div>

                    <div className="relative">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          setOpenDropdown(openDropdown === file.id ? null : file.id);
                        }}
                        className="p-1 text-zinc-400 hover:text-white"
                        title="Actions"
                      >
                        <MoreVertical size={14} />
                      </button>

                      {openDropdown === file.id && (
                        <div
                          className="absolute right-0 top-6 bg-zinc-800 border border-zinc-700 rounded-md shadow-lg z-10 min-w-[120px]"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              setShowFileModal(file);
                              setOpenDropdown(null);
                            }}
                            className="w-full text-left px-3 py-2 text-sm text-zinc-300 hover:bg-zinc-700 flex items-center gap-2"
                          >
                            <Eye size={12} />
                            View Details
                          </button>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteSingle(file);
                              setOpenDropdown(null);
                            }}
                            disabled={isDeleting}
                            className="w-full text-left px-3 py-2 text-sm text-red-400 hover:bg-zinc-700 disabled:text-red-600 flex items-center gap-2"
                          >
                            <Trash2 size={12} />
                            Delete
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                ))}

                {!filesCollectionLoading && filteredFilesCollection.length === 0 && (
                  <div className="p-4 text-sm text-zinc-400">No files found.</div>
                )}
              </div>
            </div>

            {/* Right: Summary and actions */}
            <div className="lg:col-span-1 bg-zinc-900 border border-zinc-700 rounded-md p-4">
              <h3 className="text-white font-semibold mb-4">Collection Summary</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-zinc-400">Total Files:</span>
                  <span className="text-white">
                    {filesCollectionData.length > 0 && filesCollectionData[0].totalCount
                      ? filesCollectionData[0].totalCount
                      : filesCollectionData.length}
                  </span>
                </div>
                {filesCollectionData.length > 0 && filesCollectionData[0].totalCount &&
                 filesCollectionData[0].totalCount > filesCollectionData.length && (
                  <div className="flex justify-between">
                    <span className="text-zinc-400">Loaded:</span>
                    <span className="text-yellow-400">{filesCollectionData.length}</span>
                  </div>
                )}
                <div className="flex justify-between">
                  <span className="text-zinc-400">Filtered:</span>
                  <span className="text-white">{filteredFilesCollection.length}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-zinc-400">Selected:</span>
                  <span className="text-white">{selectedFiles.size}</span>
                </div>
              </div>

              {/* Warning if showing limited results */}
              {filesCollectionData.length > 0 && filesCollectionData[0].totalCount &&
               filesCollectionData[0].totalCount > filesCollectionData.length && (
                <div className="mt-3 p-3 bg-yellow-900/20 border border-yellow-600/30 rounded-md">
                  <div className="flex items-center gap-2 text-yellow-400 text-xs">
                    <AlertTriangle size={14} />
                    <span>Showing {filesCollectionData.length} of {filesCollectionData[0].totalCount} files</span>
                  </div>
                  <div className="text-yellow-300/80 text-xs mt-1">
                    Limited to {filesCollectionData.length} files for performance
                  </div>
                </div>
              )}

              <div className="mt-6">
                <button
                  onClick={() => fetchFilesCollection(targetUser)}
                  disabled={filesCollectionLoading}
                  className="w-full flex items-center justify-center gap-2 px-3 py-2 bg-zinc-800 hover:bg-zinc-700 disabled:bg-zinc-800 rounded-md text-sm"
                >
                  <RefreshCcw size={16} className={filesCollectionLoading ? 'animate-spin' : ''} />
                  Refresh
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Delete Confirmation Modal */}
        {showDeleteModal && (
          <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
            <div className="bg-zinc-900 border border-zinc-700 p-6 rounded-lg shadow-lg max-w-md w-full mx-4">
              <div className="flex items-center gap-2 mb-4">
                <AlertTriangle className="text-red-400" size={20} />
                <h2 className="text-xl font-bold text-white">Confirm Deletion</h2>
              </div>

              <p className="text-zinc-300 mb-2">
                Are you sure you want to delete {deleteTarget.type === 'single' ? 'this file' : `${deleteTarget.files.length} files`}?
              </p>

              <div className="bg-zinc-800 p-3 rounded mb-4 max-h-32 overflow-y-auto">
                {deleteTarget.files.map((file, index) => (
                  <div key={file.id} className="text-sm text-zinc-300">
                    {index + 1}. {file.name || file.id}
                  </div>
                ))}
              </div>

              <p className="text-red-400 text-sm mb-6">
                Warning: This action will permanently delete the file(s) and all associated data including chunks, vectors, and chat history.
              </p>

              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setShowDeleteModal(false)}
                  disabled={isDeleting}
                  className="px-4 py-2 bg-zinc-700 text-zinc-300 rounded hover:bg-zinc-600 disabled:bg-zinc-800"
                >
                  Cancel
                </button>
                <button
                  onClick={confirmDelete}
                  disabled={isDeleting}
                  className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 disabled:bg-red-800 flex items-center gap-2"
                >
                  {isDeleting && <Loader2 size={16} className="animate-spin" />}
                  Delete
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Enhanced File Details Modal */}
        {showFileModal && (
          <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-60 z-50 p-4">
            <div className="bg-gradient-to-br from-zinc-900 to-zinc-800 border border-zinc-600 rounded-xl shadow-2xl max-w-4xl w-full max-h-[95vh] overflow-hidden">
              {/* Header */}
              <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                      <FileText size={18} className="text-white" />
                    </div>
                    <div>
                      <h2 className="text-xl font-bold text-white">File Details</h2>
                      <p className="text-blue-100 text-sm opacity-90">
                        {showFileModal.name || showFileModal.id}
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() => setShowFileModal(null)}
                    className="w-8 h-8 bg-white/20 hover:bg-white/30 rounded-lg flex items-center justify-center text-white transition-colors"
                  >
                    ×
                  </button>
                </div>
              </div>

              {/* Content */}
              <div className="p-6 overflow-y-auto custom-scrollbar max-h-[calc(95vh-140px)]">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Primary Information */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-white mb-3 flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      Primary Information
                    </h3>

                    {/* File Name */}
                    <div className="bg-zinc-800/50 rounded-lg p-4 border border-zinc-700">
                      <div className="text-xs text-green-400 font-medium mb-1">FILE NAME</div>
                      <div className="text-white font-mono text-sm break-all">
                        {showFileModal.name || 'Unnamed File'}
                      </div>
                    </div>

                    {/* Category */}
                    <div className="bg-zinc-800/50 rounded-lg p-4 border border-zinc-700">
                      <div className="text-xs text-blue-400 font-medium mb-1">CATEGORY</div>
                      <div className="flex items-center gap-2">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          showFileModal.category === 'Meeting Transcript' ? 'bg-purple-500/20 text-purple-300' :
                          showFileModal.category === 'High level Docs' ? 'bg-blue-500/20 text-blue-300' :
                          showFileModal.category === 'exercise library' ? 'bg-green-500/20 text-green-300' :
                          'bg-gray-500/20 text-gray-300'
                        }`}>
                          {showFileModal.category || 'Uncategorized'}
                        </span>
                      </div>
                    </div>

                    {/* File Size */}
                    {showFileModal.size && (
                      <div className="bg-zinc-800/50 rounded-lg p-4 border border-zinc-700">
                        <div className="text-xs text-orange-400 font-medium mb-1">FILE SIZE</div>
                        <div className="text-white text-sm">
                          {formatFileSize(showFileModal.size)}
                        </div>
                      </div>
                    )}

                    {/* Document Type */}
                    {showFileModal.type && (
                      <div className="bg-zinc-800/50 rounded-lg p-4 border border-zinc-700">
                        <div className="text-xs text-cyan-400 font-medium mb-1">FILE TYPE</div>
                        <div className="text-white text-sm">
                          {showFileModal.type}
                        </div>
                      </div>
                    )}

                    {/* Title */}
                    {showFileModal.title && (
                      <div className="bg-zinc-800/50 rounded-lg p-4 border border-zinc-700">
                        <div className="text-xs text-emerald-400 font-medium mb-1">TITLE</div>
                        <div className="text-white text-sm">
                          {showFileModal.title}
                        </div>
                      </div>
                    )}

                    {/* Record Title */}
                    {showFileModal.recordTitle && (
                      <div className="bg-zinc-800/50 rounded-lg p-4 border border-zinc-700">
                        <div className="text-xs text-teal-400 font-medium mb-1">RECORD TITLE</div>
                        <div className="text-white text-sm">
                          {showFileModal.recordTitle}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Technical Information */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-white mb-3 flex items-center gap-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      Technical Information
                    </h3>

                    {/* ID */}
                    <div className="bg-zinc-800/50 rounded-lg p-4 border border-zinc-700">
                      <div className="text-xs text-yellow-400 font-medium mb-1">DOCUMENT ID</div>
                      <div className="text-white font-mono text-xs break-all">
                        {showFileModal.id}
                      </div>
                    </div>

                    {/* Namespace */}
                    <div className="bg-zinc-800/50 rounded-lg p-4 border border-zinc-700">
                      <div className="text-xs text-indigo-400 font-medium mb-1">NAMESPACE</div>
                      <div className="text-white font-mono text-sm break-all">
                        {showFileModal.namespace || 'Default'}
                      </div>
                    </div>

                    {/* Timestamps */}
                    <div className="bg-zinc-800/50 rounded-lg p-4 border border-zinc-700">
                      <div className="text-xs text-pink-400 font-medium mb-2">TIMESTAMPS</div>
                      <div className="space-y-2">
                        {showFileModal.createdAt && (
                          <div>
                            <div className="text-xs text-zinc-400">Created</div>
                            <div className="text-white text-sm">{formatDate(showFileModal.createdAt)}</div>
                          </div>
                        )}
                        {showFileModal.generatedAt && (
                          <div>
                            <div className="text-xs text-zinc-400">Generated</div>
                            <div className="text-white text-sm">{formatDate(showFileModal.generatedAt)}</div>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* PMO Information */}
                    {showFileModal.pmoId && (
                      <div className="bg-zinc-800/50 rounded-lg p-4 border border-zinc-700">
                        <div className="text-xs text-violet-400 font-medium mb-1">PMO ID</div>
                        <div className="text-white text-sm font-mono break-all">
                          {showFileModal.pmoId}
                        </div>
                      </div>
                    )}

                    {/* Record Status & Priority */}
                    {(showFileModal.recordStatus || showFileModal.recordPriority) && (
                      <div className="bg-zinc-800/50 rounded-lg p-4 border border-zinc-700">
                        <div className="text-xs text-amber-400 font-medium mb-2">RECORD STATUS</div>
                        <div className="space-y-2">
                          {showFileModal.recordStatus && (
                            <div>
                              <div className="text-xs text-zinc-400">Status</div>
                              <div className="text-white text-sm">
                                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                  showFileModal.recordStatus === 'Draft' ? 'bg-yellow-500/20 text-yellow-300' :
                                  showFileModal.recordStatus === 'Published' ? 'bg-green-500/20 text-green-300' :
                                  showFileModal.recordStatus === 'Archived' ? 'bg-gray-500/20 text-gray-300' :
                                  'bg-blue-500/20 text-blue-300'
                                }`}>
                                  {showFileModal.recordStatus}
                                </span>
                              </div>
                            </div>
                          )}
                          {showFileModal.recordPriority && (
                            <div>
                              <div className="text-xs text-zinc-400">Priority</div>
                              <div className="text-white text-sm">
                                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                  showFileModal.recordPriority === 'High' ? 'bg-red-500/20 text-red-300' :
                                  showFileModal.recordPriority === 'Medium' ? 'bg-orange-500/20 text-orange-300' :
                                  showFileModal.recordPriority === 'Low' ? 'bg-green-500/20 text-green-300' :
                                  'bg-gray-500/20 text-gray-300'
                                }`}>
                                  {showFileModal.recordPriority}
                                </span>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Agent Information */}
                    {(showFileModal.agentId || showFileModal.generatedBy) && (
                      <div className="bg-zinc-800/50 rounded-lg p-4 border border-zinc-700">
                        <div className="text-xs text-emerald-400 font-medium mb-2">AGENT INFO</div>
                        <div className="space-y-2">
                          {showFileModal.agentId && (
                            <div>
                              <div className="text-xs text-zinc-400">Agent ID</div>
                              <div className="text-white text-sm font-mono">{showFileModal.agentId}</div>
                            </div>
                          )}
                          {showFileModal.generatedBy && (
                            <div>
                              <div className="text-xs text-zinc-400">Generated By</div>
                              <div className="text-white text-sm">{showFileModal.generatedBy}</div>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Record Description */}
                {showFileModal.recordDescription && (
                  <div className="mt-6">
                    <h3 className="text-lg font-semibold text-white mb-3 flex items-center gap-2">
                      <div className="w-2 h-2 bg-cyan-500 rounded-full"></div>
                      Record Description
                    </h3>
                    <div className="bg-zinc-800/50 rounded-lg p-4 border border-zinc-700">
                      <div className="text-white text-sm leading-relaxed">
                        {showFileModal.recordDescription}
                      </div>
                    </div>
                  </div>
                )}

                {/* URLs and References */}
                {(showFileModal.downloadUrl || showFileModal.ref) && (
                  <div className="mt-6">
                    <h3 className="text-lg font-semibold text-white mb-3 flex items-center gap-2">
                      <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                      URLs & References
                    </h3>
                    <div className="grid grid-cols-1 gap-4">
                      {showFileModal.downloadUrl && (
                        <div className="bg-zinc-800/50 rounded-lg p-4 border border-zinc-700">
                          <div className="text-xs text-purple-400 font-medium mb-1">DOWNLOAD URL</div>
                          <div className="text-white text-sm font-mono break-all bg-zinc-900 p-2 rounded">
                            {showFileModal.downloadUrl}
                          </div>
                        </div>
                      )}
                      {showFileModal.ref && (
                        <div className="bg-zinc-800/50 rounded-lg p-4 border border-zinc-700">
                          <div className="text-xs text-purple-400 font-medium mb-1">REFERENCE</div>
                          <div className="text-white text-sm font-mono break-all bg-zinc-900 p-2 rounded">
                            {showFileModal.ref}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Additional Properties */}
                <div className="mt-6">
                  <h3 className="text-lg font-semibold text-white mb-3 flex items-center gap-2">
                    <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                    All Properties
                  </h3>
                  <div className="bg-zinc-800/30 rounded-lg p-4 border border-zinc-700">
                    <div className="grid grid-cols-1 gap-2 max-h-60 overflow-y-auto custom-scrollbar">
                      {Object.entries(showFileModal).map(([key, value]) => (
                        <div key={key} className="flex justify-between items-start py-1 border-b border-zinc-800/50 last:border-b-0">
                          <div className="text-xs text-zinc-400 font-medium min-w-0 flex-shrink-0 mr-3">
                            {key.toUpperCase()}
                          </div>
                          <div className="text-zinc-200 text-xs break-words text-right">
                            {renderValue(value)}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* Footer */}
              <div className="bg-zinc-800/50 border-t border-zinc-700 p-4 flex justify-between items-center">
                <div className="text-xs text-zinc-400">
                  {Object.keys(showFileModal).length} properties
                </div>
                <button
                  onClick={() => copyToClipboard(JSON.stringify(showFileModal, null, 2))}
                  className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 rounded-lg text-sm text-white transition-all"
                >
                  <Clipboard size={14} />
                  Copy JSON
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </main>
  );
}
