import { NextRequest, NextResponse } from 'next/server';
import { internetSearchTool } from '../../../../lib/tools/internetSearchTool';
import { InternetSearchTool } from '../../../../lib/tools/internet-search';

/**
 * API endpoint for internet search functionality
 * Can be called directly by ElevenLabs or other external services
 */
export async function POST(request: NextRequest) {
  try {
    console.log('[INTERNET_SEARCH_API] Received search request');

    // Parse request body
    const body = await request.json();
    console.log('[INTERNET_SEARCH_API] Request body:', JSON.stringify(body, null, 2));

    const {
      query,
      numResults = 5,
      context,
      searchPurpose = 'general'
    } = body;

    // Validate required parameters
    if (!query) {
      console.error('[INTERNET_SEARCH_API] Missing required parameter: query');
      return NextResponse.json({
        success: false,
        error: 'Missing required parameter: query is required',
        message: 'Please provide a search query to search the web.'
      }, { status: 400 });
    }

    console.log('[INTERNET_SEARCH_API] Performing web search:', {
      query,
      numResults,
      searchPurpose,
      hasContext: !!context
    });

    // Perform the web search using our existing internet search tool
    const searchOptions = {
      numResults: Math.min(numResults, 10) // Limit to max 10 results
    };

    // Try the primary search tool first, fallback to secondary if needed
    let searchResult;
    try {
      searchResult = await internetSearchTool.search(query, searchOptions);
    } catch (primaryError) {
      console.log('[INTERNET_SEARCH_API] Primary search tool failed, trying fallback:', primaryError);
      // Fallback to the alternative search tool
      const fallbackTool = new InternetSearchTool();
      searchResult = await fallbackTool.search(query, searchOptions);
    }

    if (!searchResult.success) {
      console.error('[INTERNET_SEARCH_API] Search failed:', searchResult.metadata?.error);
      return NextResponse.json({
        success: false,
        error: searchResult.metadata?.error || 'Web search failed',
        message: 'I apologize, but I encountered an error while searching the web. Please try rephrasing your search query.'
      }, { status: 500 });
    }

    // Format the search results for the response
    const formattedResults = formatSearchResults(searchResult.results, searchPurpose, context);

    console.log('[INTERNET_SEARCH_API] Search completed successfully:', {
      resultsCount: searchResult.results.length,
      query,
      searchPurpose
    });

    // Prepare response
    const response = {
      success: true,
      results: searchResult.results,
      formattedResults: formattedResults,
      message: formattedResults,
      searchDetails: {
        query: query,
        resultsCount: searchResult.results.length,
        searchPurpose: searchPurpose,
        searchTime: searchResult.metadata?.searchTime,
        sources: searchResult.results.map(result => ({
          title: result.title,
          url: result.link
        }))
      },
      metadata: searchResult.metadata
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('[INTERNET_SEARCH_API] Unexpected error:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      message: 'I apologize, but I encountered an unexpected error while searching the web. Please try again later.'
    }, { status: 500 });
  }
}

/**
 * Format search results for response
 */
function formatSearchResults(
  results: any[], 
  searchPurpose: string, 
  context?: string
): string {
  if (!results || results.length === 0) {
    return "I couldn't find any relevant information for your search query. You might want to try different keywords or a more specific search term.";
  }

  let formattedMessage = '';

  // Add context-aware introduction
  switch (searchPurpose) {
    case 'research':
      formattedMessage = `I found ${results.length} research sources for your query. Here's what I discovered:\n\n`;
      break;
    case 'verification':
      formattedMessage = `I've verified the information and found ${results.length} relevant sources:\n\n`;
      break;
    case 'examples':
      formattedMessage = `I found ${results.length} examples that might be helpful:\n\n`;
      break;
    case 'best_practices':
      formattedMessage = `Here are ${results.length} sources about best practices:\n\n`;
      break;
    case 'current_trends':
      formattedMessage = `I found ${results.length} sources about current trends:\n\n`;
      break;
    case 'technical_info':
      formattedMessage = `Here's the technical information I found from ${results.length} sources:\n\n`;
      break;
    default:
      formattedMessage = `I found ${results.length} relevant results for your search:\n\n`;
  }

  // Add the search results (limit to top 3 for readability)
  results.slice(0, 3).forEach((result, index) => {
    formattedMessage += `${index + 1}. **${result.title}**\n`;
    formattedMessage += `   ${result.snippet || 'No description available.'}\n`;
    formattedMessage += `   Source: ${result.link}\n\n`;
  });

  // Add summary if more results available
  if (results.length > 3) {
    formattedMessage += `I found ${results.length - 3} additional sources that might be relevant. `;
  }

  // Add context-aware conclusion
  if (context) {
    formattedMessage += `\nThis information should help with ${context}. Would you like me to search for more specific information or generate a document incorporating these findings?`;
  } else {
    formattedMessage += `\nWould you like me to search for more specific information or use these findings for further analysis?`;
  }

  return formattedMessage;
}

// Handle GET requests for testing
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const query = searchParams.get('query');
  
  if (!query) {
    return NextResponse.json({
      error: 'Query parameter is required',
      usage: 'GET /api/tools/internet-search?query=your+search+terms'
    }, { status: 400 });
  }

  // Convert GET to POST format and process
  const body = {
    query,
    numResults: parseInt(searchParams.get('numResults') || '5'),
    searchPurpose: searchParams.get('searchPurpose') || 'general'
  };

  // Create a new request object with POST method and body
  const postRequest = new NextRequest(request.url, {
    method: 'POST',
    headers: request.headers,
    body: JSON.stringify(body)
  });

  return POST(postRequest);
}

// Handle OPTIONS for CORS if needed
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
