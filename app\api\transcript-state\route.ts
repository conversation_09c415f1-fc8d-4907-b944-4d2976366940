/**
 * API endpoint for storing and retrieving transcript state from UI
 * This uses Firebase Firestore as a persistent, shared storage medium.
 *
 * Storage Structure: Collection 'transcript_sessions' with document IDs as lookup keys.
 */

import { NextRequest, NextResponse } from 'next/server';
import { adminDb } from '../../../components/firebase-admin'; // ✅ FIX: Import Firestore admin instance

// Define TranscriptMessage interface locally
interface TranscriptMessage {
  role: 'user' | 'assistant' | 'agent';
  content: string;
  timestamp: Date;
}

interface TranscriptState {
  userId: string;
  agentId?: string;
  conversationId?: string;
  dialogue: TranscriptMessage[];
  currentResponse?: string;
  agentName?: string;
  agentType?: string;
  timestamp: number;
  createdAt: Date; // ✅ FIX: Add timestamp for TTL
}

// Updated to use hierarchical structure: users/{userId}/agent/{conversationId}/transcript_sessions
const getTranscriptCollectionPath = (userId: string, conversationId: string) => {
  return `users/${userId}/agent/${conversationId}/transcript_sessions`;
};



/**
 * Store transcript state from UI into Firestore
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      userId,
      agentId,
      conversationId,
      dialogue,
      currentResponse,
      agentName,
      agentType,
      timestamp
    } = body;

    if (!userId || !Array.isArray(dialogue)) {
      return NextResponse.json({
        error: 'Invalid request body. userId and dialogue are required.'
      }, { status: 400 });
    }

    // Require conversationId for the new hierarchical structure
    if (!conversationId) {
      return NextResponse.json({
        error: 'conversationId is required for the new hierarchical storage structure.'
      }, { status: 400 });
    }

    // ✅ FIX: Filter out undefined values to prevent Firestore errors
    const transcriptState = {
      userId,
      ...(agentId !== undefined && { agentId }),
      ...(conversationId !== undefined && { conversationId }),
      dialogue: dialogue.map((msg: any) => ({
        ...msg,
        timestamp: new Date(msg.timestamp)
      })),
      ...(currentResponse !== undefined && { currentResponse }),
      ...(agentName !== undefined && { agentName }),
      ...(agentType !== undefined && { agentType }),
      timestamp: timestamp || Date.now(),
      // ✅ FIX: Add a server-side timestamp for Firestore's TTL policy
      createdAt: new Date(),
    };

    const storeKeys = [
      userId,
      '<EMAIL>',
      ...(agentId ? [agentId] : []),
      ...(conversationId ? [conversationId] : [])
    ].filter((key, index, arr) => key && arr.indexOf(key) === index);

    console.log('[TRANSCRIPT_STATE_API] 🔍 Debug - Transcript state before Firestore write:', {
      hasUserId: !!transcriptState.userId,
      hasAgentId: !!transcriptState.agentId,
      hasConversationId: !!transcriptState.conversationId,
      dialogueLength: transcriptState.dialogue?.length,
      storeKeys: storeKeys,
      undefinedFields: Object.entries(transcriptState).filter(([, value]) => value === undefined).map(([key]) => key)
    });

    // ✅ FIX: Use the new hierarchical structure for storage
    const collectionPath = getTranscriptCollectionPath(userId, conversationId);
    const batch = adminDb.batch();

    // Store under multiple keys for backward compatibility and lookup flexibility
    for (const key of storeKeys) {
      const docRef = adminDb.collection(collectionPath).doc(key);
      batch.set(docRef, transcriptState);
    }
    await batch.commit();

    console.log('[TRANSCRIPT_STATE_API] ✅ Transcript state stored successfully in Firestore:', {
      userId,
      agentId,
      conversationId,
      dialogueLength: dialogue.length,
      storeKeys,
    });

    return NextResponse.json({
      success: true,
      message: 'Transcript state stored in Firestore',
      storeKeys
    });

  } catch (error) {
    console.error('[TRANSCRIPT_STATE_API] Error storing transcript state in Firestore:', error);
    return NextResponse.json({ error: 'Failed to store transcript state' }, { status: 500 });
  }
}

/**
 * Retrieve transcript state from Firestore for webhook
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const agentId = searchParams.get('agentId');
    const conversationId = searchParams.get('conversationId');

    console.log('[TRANSCRIPT_STATE_API] 🔍 GET request:', { userId, agentId, conversationId });

    // For the new hierarchical structure, we need userId and conversationId
    if (!userId || !conversationId) {
      return NextResponse.json({
        success: false,
        message: 'userId and conversationId are required for the new hierarchical structure'
      });
    }

    const lookupKeys = [
      agentId, // Highest priority lookup
      conversationId,
      userId,
      '<EMAIL>'
    ].filter((key): key is string => Boolean(key));

    let transcriptState: any | null = null;
    let foundKey: string | undefined;

    // ✅ FIX: Use the new hierarchical structure for retrieval
    const collectionPath = getTranscriptCollectionPath(userId, conversationId);

    for (const key of lookupKeys) {
      const docRef = adminDb.collection(collectionPath).doc(key);
      const docSnap = await docRef.get();
      if (docSnap.exists) {
        transcriptState = docSnap.data();
        foundKey = key;
        break;
      }
    }

    if (!transcriptState) {
      console.log(`[TRANSCRIPT_STATE_API] ⚠️ No transcript state found in Firestore for keys:`, lookupKeys);
      return NextResponse.json({ success: false, message: 'No transcript state found' });
    }

    // Firestore Timestamps need to be converted for JSON serialization
    if (transcriptState.createdAt) {
        transcriptState.createdAt = transcriptState.createdAt.toDate().toISOString();
    }
    transcriptState.dialogue = transcriptState.dialogue.map((msg: any) => ({
        ...msg,
        timestamp: msg.timestamp.toDate().toISOString()
    }));

    console.log('[TRANSCRIPT_STATE_API] ✅ Retrieved transcript state from Firestore:', {
      foundKey,
      dialogueLength: transcriptState.dialogue.length,
    });

    return NextResponse.json({ success: true, transcriptState, foundKey });

  } catch (error) {
    console.error('[TRANSCRIPT_STATE_API] Error retrieving transcript state from Firestore:', error);
    return NextResponse.json({ error: 'Failed to retrieve transcript state' }, { status: 500 });
  }
}

/**
 * Clear transcript state
 */
export async function DELETE(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, agentId, conversationId } = body;

    // For the new hierarchical structure, we need userId and conversationId
    if (!userId || !conversationId) {
      return NextResponse.json({
        error: 'userId and conversationId are required for the new hierarchical structure.'
      }, { status: 400 });
    }

    const clearKeys = [
      userId,
      agentId,
      conversationId,
      '<EMAIL>'
    ].filter((key): key is string => Boolean(key));

    // ✅ FIX: Use the new hierarchical structure for deletion
    const collectionPath = getTranscriptCollectionPath(userId, conversationId);
    const batch = adminDb.batch();
    for (const key of clearKeys) {
      const docRef = adminDb.collection(collectionPath).doc(key);
      batch.delete(docRef);
    }
    await batch.commit();

    console.log('[TRANSCRIPT_STATE_API] ✅ Cleared transcript state from Firestore:', { clearKeys });

    return NextResponse.json({ success: true, message: 'Transcript state cleared' });

  } catch (error) {
    console.error('[TRANSCRIPT_STATE_API] Error clearing transcript state from Firestore:', error);
    return NextResponse.json({ error: 'Failed to clear transcript state' }, { status: 500 });
  }
}
