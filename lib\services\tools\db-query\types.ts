import type { QueryCodebaseRequest as BaseRequest, QueryCodebaseResponse as BaseResponse } from 'lib/tools/queryCodebaseStorage';

// Re-export to guarantee compatibility across callers
export type QueryCodebaseRequest = BaseRequest;
export type QueryCodebaseResponse = BaseResponse;

// Strategy identifiers supported by the interface
export type QueryStrategyId = 'standalone' | 'content-selector' | 'two-stage' | 'hybrid-inverted';

export interface PerformanceMetrics {
  durationMs: number;
  embeddingMs?: number;
  pineconeQueryMs?: number;
  firestoreFetchMs?: number;
  prefilterMs?: number; // for two-stage
  namespacesCount?: number;
  candidateCount?: number;
  notes?: string[];
  timingsMs?: Record<string, number>; // passthrough
}

export interface QualityScores {
  avgScore?: number; // average vector score
  diversity?: number; // unique file paths / total results
  coverage?: number; // namespaces with matches / namespaces queried
}

export interface StrategyRun {
  strategyId: QueryStrategyId;
  response: QueryCodebaseResponse;
  metrics: PerformanceMetrics;
  quality?: QualityScores;
  error?: string;
}

export interface ABTestResult {
  userId: string;
  category: string;
  query: string;
  runs: StrategyRun[];
}

export interface StrategyExecutor {
  id: QueryStrategyId;
  execute: (req: QueryCodebaseRequest) => Promise<StrategyRun>;
}

export interface DBQueryConfig {
  defaultStrategy: QueryStrategyId;
  enabledStrategies: QueryStrategyId[];
  abTest?: {
    enabled: boolean;
    strategies: QueryStrategyId[];
  };
}

