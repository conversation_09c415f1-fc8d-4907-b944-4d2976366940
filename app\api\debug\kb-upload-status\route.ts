/**
 * Debug endpoint to check knowledge base upload status for agents
 * This helps verify that meeting summaries are being uploaded and RAG indexed
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]/authOptions';

// Type definitions for ElevenLabs API responses
interface KnowledgeBaseDocument {
  id: string;
  name: string;
  size?: number;
  created_at?: string;
  [key: string]: any;
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const agentId = searchParams.get('agentId');

    if (!agentId) {
      return NextResponse.json({ 
        error: 'agentId parameter is required' 
      }, { status: 400 });
    }

    const apiKey = process.env.NEXT_PUBLIC_ELEVENLABS_COMPANY_API_KEY;
    if (!apiKey) {
      return NextResponse.json({ 
        error: 'ElevenLabs API key not configured' 
      }, { status: 500 });
    }

    console.log('[KB_DEBUG] Checking knowledge base status for agent:', agentId);

    // Get agent configuration
    const agentResponse = await fetch(`https://api.elevenlabs.io/v1/convai/agents/${agentId}`, {
      headers: {
        'xi-api-key': apiKey,
        'Content-Type': 'application/json'
      }
    });

    if (!agentResponse.ok) {
      return NextResponse.json({ 
        error: `Failed to fetch agent: ${agentResponse.statusText}` 
      }, { status: agentResponse.status });
    }

    const agentData = await agentResponse.json();

    // Get knowledge base documents
    let knowledgeBaseDocuments: KnowledgeBaseDocument[] = [];
    try {
      const kbResponse = await fetch(`https://api.elevenlabs.io/v1/convai/agents/${agentId}/knowledge-base`, {
        headers: {
          'xi-api-key': apiKey,
          'Content-Type': 'application/json'
        }
      });

      if (kbResponse.ok) {
        const kbData = await kbResponse.json();
        knowledgeBaseDocuments = kbData.documents || [];
      }
    } catch (kbError) {
      console.warn('[KB_DEBUG] Could not fetch knowledge base documents:', kbError);
    }

    // Filter for meeting-related documents
    const meetingDocuments = knowledgeBaseDocuments.filter((doc: KnowledgeBaseDocument) =>
      doc.name?.toLowerCase().includes('meeting') ||
      doc.name?.toLowerCase().includes('transcript') ||
      doc.name?.toLowerCase().includes('summary')
    );

    const status = {
      agent: {
        id: agentId,
        name: agentData.name,
        hasKnowledgeBase: !!agentData.knowledge_base_id,
        knowledgeBaseId: agentData.knowledge_base_id
      },
      knowledgeBase: {
        totalDocuments: knowledgeBaseDocuments.length,
        meetingDocuments: meetingDocuments.length,
        documents: knowledgeBaseDocuments.map((doc: KnowledgeBaseDocument) => ({
          id: doc.id,
          name: doc.name,
          size: doc.size,
          createdAt: doc.created_at,
          isMeetingRelated: doc.name?.toLowerCase().includes('meeting') ||
                           doc.name?.toLowerCase().includes('transcript') ||
                           doc.name?.toLowerCase().includes('summary')
        }))
      },
      uploadStatus: {
        isConfigured: !!agentData.knowledge_base_id,
        canUpload: !!apiKey && !!agentData.knowledge_base_id,
        lastCheck: new Date().toISOString()
      }
    };

    console.log('[KB_DEBUG] Knowledge base status:', {
      agentId,
      totalDocs: status.knowledgeBase.totalDocuments,
      meetingDocs: status.knowledgeBase.meetingDocuments,
      hasKB: status.agent.hasKnowledgeBase
    });

    return NextResponse.json({
      success: true,
      status
    });

  } catch (error) {
    console.error('[KB_DEBUG] Error checking knowledge base status:', error);
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { agentId, action } = body;

    if (!agentId || !action) {
      return NextResponse.json({ 
        error: 'agentId and action are required' 
      }, { status: 400 });
    }

    const apiKey = process.env.NEXT_PUBLIC_ELEVENLABS_COMPANY_API_KEY;
    if (!apiKey) {
      return NextResponse.json({ 
        error: 'ElevenLabs API key not configured' 
      }, { status: 500 });
    }

    console.log('[KB_DEBUG] Performing action:', { agentId, action });

    if (action === 'test_upload') {
      // Test upload a simple document to verify KB upload is working
      const testContent = `Test document uploaded at ${new Date().toISOString()}\n\nThis is a test to verify knowledge base upload functionality.`;
      const testBlob = new Blob([testContent], { type: 'text/plain' });
      const testFile = new File([testBlob], 'kb_test_document.txt', { type: 'text/plain' });

      try {
        const uploadResponse = await fetch('https://api.elevenlabs.io/v1/convai/knowledge-base', {
          method: 'POST',
          headers: {
            'xi-api-key': apiKey
          },
          body: (() => {
            const formData = new FormData();
            formData.append('file', testFile);
            formData.append('name', 'KB Test Document');
            return formData;
          })()
        });

        if (!uploadResponse.ok) {
          throw new Error(`Upload failed: ${uploadResponse.statusText}`);
        }

        const uploadResult = await uploadResponse.json();

        // Associate with agent
        const associateResponse = await fetch(`https://api.elevenlabs.io/v1/convai/agents/${agentId}/knowledge-base`, {
          method: 'PATCH',
          headers: {
            'xi-api-key': apiKey,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            knowledge_base_ids: [uploadResult.id]
          })
        });

        if (!associateResponse.ok) {
          throw new Error(`Association failed: ${associateResponse.statusText}`);
        }

        return NextResponse.json({
          success: true,
          message: 'Test upload successful',
          testDocumentId: uploadResult.id
        });

      } catch (uploadError) {
        return NextResponse.json({
          success: false,
          error: 'Test upload failed',
          details: uploadError instanceof Error ? uploadError.message : 'Unknown error'
        }, { status: 500 });
      }
    }

    return NextResponse.json({ 
      error: 'Unknown action' 
    }, { status: 400 });

  } catch (error) {
    console.error('[KB_DEBUG] Error performing action:', error);
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
