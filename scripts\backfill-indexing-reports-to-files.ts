/*
  Backfill migration utility
  - Iterates users/{userId}/codebase-indexing-reports
  - Creates corresponding users/{userId}/files/{indexingSessionId} docs when missing
  - Deletes legacy doc after successful write
  - Idempotent and safe to run multiple times

  Usage (Node):
    ts-node scripts/backfill-indexing-reports-to-files.ts
  or compile & run with ts-node/register. Requires service_key.json and env FIREBASE_STORAGE_BUCKET.
*/

import { getApps, initializeApp, cert, App } from 'firebase-admin/app';
import { getFirestore } from 'firebase-admin/firestore';
import * as fs from 'fs';

// Load service account (relative to repo root)
const serviceKeyPath = './service_key.json';
if (!fs.existsSync(serviceKeyPath)) {
  console.error('Missing service_key.json in project root. Aborting.');
  process.exit(1);
}
const serviceAccount = JSON.parse(fs.readFileSync(serviceKeyPath, 'utf-8'));

let app: App;
if (!getApps().length) {
  app = initializeApp({
    credential: cert(serviceAccount as any),
    storageBucket: process.env.FIREBASE_STORAGE_BUCKET,
  });
} else {
  app = getApps()[0]!;
}

const adminDb = getFirestore(app);

// Minimal mapper: legacy StoredCodebaseIndexingReport -> files doc structure
function mapLegacyToFilesDoc(legacy: any) {
  // files doc id should be indexingSessionId
  const id = legacy.indexingSessionId || legacy.id;

  const totalFiles = Number(legacy?.statistics?.totalFiles || 0);
  const totalChunks = Number(legacy?.statistics?.totalChunks || 0);
  const size = Number(legacy?.statistics?.totalSize || 0);
  const processingTimeMs = Number(legacy?.processingTimeMs || 0);

  const doc = {
    // Canonical file document fields
    name: `codebase-indexing-report-${legacy.projectName || id}.pdf`,
    title: legacy.category || `Codebase Documentation - ${legacy.projectName || id}`,
    type: 'application/pdf',
    category: legacy.category || 'Codebase Indexing',
    generatedBy: 'CodebaseIndexingSystem',
    reportType: 'codebase-indexing-completion',
    namespace: id,
    downloadUrl: legacy?.firebaseStorage?.reportPdfUrl || '',
    size: size,

    // Additional helpful fields mirrored for UI
    projectName: legacy.projectName || id,
    createdAt: legacy.createdAt,
    completedAt: legacy.completedAt || legacy.createdAt,
    processingTimeMs,
    totalFiles,
    totalChunks,

    // Embedding metadata (best-effort)
    indexingComplete: legacy?.vectorEmbedding?.indexingComplete ?? true,
    embeddingModel: legacy?.vectorEmbedding?.embeddingModel || 'text-embedding-3-small',
    embeddingDimensions: legacy?.vectorEmbedding?.embeddingDimensions || 1536,

    // Option 3 backfill: include condensed file analysis so UI can render details
    fileAnalysisCondensed: Array.isArray(legacy?.fileAnalysis) ? legacy.fileAnalysis.map((f: any) => {
      const truncateAtSentence = (text: string, max: number) => {
        if (!text) return '';
        if (text.length <= max) return text;
        const slice = text.slice(0, max);
        const lastPeriod = slice.lastIndexOf('.');
        const lastNewline = slice.lastIndexOf('\n');
        const lastSpace = slice.lastIndexOf(' ');
        const cut = Math.max(lastPeriod, lastNewline, lastSpace);
        return (cut > max * 0.6 ? slice.slice(0, cut) : slice).trim() + '…';
      };

      return {
        filePath: f.filePath,
        fileName: f.fileName,
        language: f.language,
        fileSize: f.fileSize,
        chunkCount: f.chunkCount,
        processingTimeMs: f.processingTimeMs,
        success: f.success,
        errorMessage: f.errorMessage || undefined,
        llmSummaryPreview: truncateAtSentence(f.llmSummary || '', 600),
        // Include trimmed code intelligence and counts where present
        codeEntityType: f.codeEntityType || 'Unknown',
        definedEntities: Array.isArray(f.definedEntities) ? f.definedEntities.slice(0, 10) : [],
        imports: Array.isArray(f.imports) ? f.imports.slice(0, 10) : [],
        exports: Array.isArray(f.exports) ? f.exports.slice(0, 10) : [],
        apiEndpoints: Array.isArray(f.apiEndpoints) ? f.apiEndpoints.slice(0, 10) : [],
        definedEntitiesCount: Array.isArray(f.definedEntities) ? f.definedEntities.length : 0,
        importsCount: Array.isArray(f.imports) ? f.imports.length : 0,
        exportsCount: Array.isArray(f.exports) ? f.exports.length : 0,
        apiEndpointsCount: Array.isArray(f.apiEndpoints) ? f.apiEndpoints.length : 0,
      };
    }) : [],
  };

  return { id, data: doc };
}

async function backfillUser(userId: string) {
  const legacyCol = adminDb.collection('users').doc(userId).collection('codebase-indexing-reports');
  const filesCol = adminDb.collection('users').doc(userId).collection('files');

  const legacySnap = await legacyCol.orderBy('completedAt', 'desc').get();
  if (legacySnap.empty) {
    console.log(`No legacy documents for user: ${userId}`);
    return;
  }

  console.log(`Found ${legacySnap.size} legacy documents for ${userId}`);

  for (const doc of legacySnap.docs) {
    const legacy = doc.data();
    const { id, data } = mapLegacyToFilesDoc(legacy);

    const targetRef = filesCol.doc(id);
    const existing = await targetRef.get();

    if (existing.exists) {
      console.log(`Skipping existing files doc: users/${userId}/files/${id}`);
      continue; // idempotent
    }

    try {
      await targetRef.set(data, { merge: true });
      console.log(`Created files doc: users/${userId}/files/${id}`);

      // After successful migration, delete legacy doc
      await doc.ref.delete();
      console.log(`Deleted legacy doc: users/${userId}/codebase-indexing-reports/${doc.id}`);
    } catch (err) {
      console.error(`Failed to migrate ${doc.id} for ${userId}:`, err);
      // Do not delete legacy on failure
    }
  }
}

async function main() {
  // Enumerate all users by listing top-level users collection
  const usersSnap = await adminDb.collection('users').get();
  if (usersSnap.empty) {
    console.log('No users found. Nothing to backfill.');
    return;
  }

  for (const userDoc of usersSnap.docs) {
    await backfillUser(userDoc.id);
  }

  console.log('Backfill completed.');
}

main().catch((e) => {
  console.error('Backfill script failed:', e);
  process.exit(1);
});

