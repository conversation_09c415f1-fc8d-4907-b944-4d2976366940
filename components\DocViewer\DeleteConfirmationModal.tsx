// DeleteConfirmationModal.tsx
import React from 'react';
import { FileData } from './FileManagerstructure';

interface DeleteConfirmationModalProps {
  selectedFile: FileData;
  onConfirm: () => void;
  onCancel: () => void;
  truncateFileName: (fileName: string, maxLength: number) => string;
}

const DeleteConfirmationModal: React.FC<DeleteConfirmationModalProps> = ({
  selectedFile,
  onConfirm,
  onCancel,
  truncateFileName,
}) => {
  const formatDate = (value: any): string => {
    try {
      if (!value) return 'Unknown';
      // Firestore Timestamp
      if (typeof value?.toDate === 'function') return value.toDate().toLocaleString();
      // Firestore admin timestamp object {seconds, nanoseconds}
      if (typeof value?.seconds === 'number') return new Date(value.seconds * 1000).toLocaleString();
      // ISO string or Date or millis
      const d = value instanceof Date ? value : new Date(value);
      return isNaN(d.getTime()) ? 'Unknown' : d.toLocaleString();
    } catch {
      return 'Unknown';
    }
  };

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
      <div className="bg-white text-gray-700 p-6 rounded-lg shadow-lg max-w-md w-full mx-4">
        <h2 className="text-xl font-bold mb-4 text-red-600">Confirm Deletion</h2>
        <p className="mb-4">
          Are you sure you want to delete this document and associated chats?
        </p>
        <div className="space-y-2 mb-6 bg-gray-50 p-4 rounded">
          <p>
            <strong className="text-gray-600">Document:</strong>{' '}
            <span className="text-gray-800">{truncateFileName(selectedFile.name, 40)}</span>
          </p>
          <p>
            <strong className="text-gray-600">Date Created:</strong>{' '}
            <span className="text-gray-800">{formatDate((selectedFile as any).createdAt)}</span>
          </p>
        </div>
        <div className="flex justify-end space-x-3">
          <button
            onClick={onCancel}
            className="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors duration-200"
          >
            Cancel
          </button>
          <button
            onClick={onConfirm}
            className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors duration-200"
          >
            Delete
          </button>
        </div>
      </div>
    </div>
  );
};

export default DeleteConfirmationModal;