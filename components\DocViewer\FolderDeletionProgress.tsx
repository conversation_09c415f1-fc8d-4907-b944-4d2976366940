import React from 'react';

interface ProgressItem {
  name?: string;
  namespace: string;
  status: 'pending' | 'success' | 'failed';
  error?: string;
}

interface FolderDeletionProgressProps {
  category: string;
  items: ProgressItem[];
}

const FolderDeletionProgress: React.FC<FolderDeletionProgressProps> = ({ category, items }) => {
  const total = items.length;
  const completed = items.filter(i => i.status !== 'pending').length;
  return (
    <div className="fixed top-4 right-4 bg-gray-900 text-gray-100 border border-gray-700 rounded p-3 w-96 shadow-lg z-50">
      <div className="font-semibold mb-2">Deleting folder "{category}"</div>
      <div className="text-xs mb-2">Progress: {completed} / {total}</div>
      <div className="max-h-60 overflow-y-auto space-y-1">
        {items.map((i, idx) => (
          <div key={idx} className="flex justify-between text-xs">
            <span className="truncate mr-2">{i.name || i.namespace}</span>
            <span className={
              i.status === 'success' ? 'text-green-400' : i.status === 'failed' ? 'text-red-400' : 'text-gray-300'
            }>
              {i.status}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
};

export type { ProgressItem };
export default FolderDeletionProgress;

