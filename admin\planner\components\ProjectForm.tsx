'use client';

import React, { useState, useEffect } from 'react';
import { Project, User } from '../types';
import { Calendar, AlertCircle } from 'lucide-react';
import { getCategoryColorClass } from '../../../app/utils/categoryColors';

interface ProjectFormProps {
  initialValues?: Partial<Project>;
  users: User[];
  onSubmit: (project: Omit<Project, 'id'>) => void;
  onCancel: () => void;
}

const ProjectForm: React.FC<ProjectFormProps> = ({
  initialValues,
  users,
  onSubmit,
  onCancel
}) => {
  // Debugging log for initial values
  console.log('Initial values received:', initialValues);

  /**
   * Normalizes array data to ensure consistent format regardless of input type
   * @param data - The data to normalize (can be array, string, object, etc.)
   * @returns Normalized array of strings
   */
  const normalizeArrayData = (data: any): string[] => {
    if (!data) return [];

    if (Array.isArray(data)) {
      return data;
    } else if (typeof data === 'string') {
      try {
        const parsed = JSON.parse(data);
        if (Array.isArray(parsed)) {
          return parsed;
        }
        return data.split(',').map(item => item.trim());
      } catch (e) {
        return data.split(',').map(item => item.trim());
      }
    } else if (typeof data === 'object') {
      return Object.values(data);
    }

    return [];
  };

  // Process categories consistently
  const processedCategories = normalizeArrayData(initialValues?.categories);
  console.log('Processed categories:', processedCategories);

  // Process members consistently
  const processedMembers = normalizeArrayData(initialValues?.members);
  console.log('Processed members:', processedMembers);

  // Define a type that extends Project but allows string for status during form editing
  type ProjectFormValues = Omit<Project, 'status'> & {
    status: 'Active' | 'Completed' | 'On Hold' | 'Cancelled' | string;
  };

  // Initialize form values with defaults and processed initial values
  const defaultValues: Partial<ProjectFormValues> = {
    name: '',
    description: '',
    startDate: new Date(),
    endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // Default to 30 days from now
    owner: users.length > 0 ? users[0].id : '',
    members: [],
    categories: [],
    status: 'Active'
  };

  // Merge defaults with processed initial values
  const initialFormValues: Partial<ProjectFormValues> = {
    ...defaultValues,
    ...initialValues,
    categories: processedCategories,
    members: processedMembers
  };

  console.log('Initial form values:', initialFormValues);

  const [formValues, setFormValues] = useState<Partial<ProjectFormValues>>(initialFormValues);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [categoryInput, setCategoryInput] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Re-sync form values if initialValues changes (e.g., when editing different projects)
  useEffect(() => {
    if (initialValues) {
      setFormValues({
        ...defaultValues,
        ...initialValues,
        categories: normalizeArrayData(initialValues.categories),
        members: normalizeArrayData(initialValues.members)
      } as Partial<ProjectFormValues>);
    }
  }, [initialValues?.id]); // Only re-sync when project ID changes

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;

    // Debug logging for field changes
    console.log(`Field "${name}" changed to:`, value);

    setFormValues(prev => ({ ...prev, [name]: value }));

    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>, field: 'startDate' | 'endDate') => {
    const date = new Date(e.target.value);
    console.log(`${field} changed to:`, date);

    setFormValues(prev => ({ ...prev, [field]: date }));

    // Clear error when field is edited
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const handleMultiSelect = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const options = Array.from(e.target.selectedOptions).map(option => option.value);
    console.log('Members selected:', options);

    setFormValues(prev => ({ ...prev, members: options }));

    // Clear error when field is edited
    if (errors.members) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors.members;
        return newErrors;
      });
    }
  };

  const handleAddCategory = (e: React.MouseEvent | React.KeyboardEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (categoryInput.trim()) {
      const newCategory = categoryInput.trim();
      console.log('Adding category:', newCategory);

      // Ensure current categories is an array
      const currentCategories = normalizeArrayData(formValues.categories);

      // Add the new category if it doesn't already exist
      if (!currentCategories.includes(newCategory)) {
        const updatedCategories = [...currentCategories, newCategory];
        console.log('Updated categories:', updatedCategories);

        // Update form values directly with the new array
        setFormValues(prev => ({
          ...prev,
          categories: updatedCategories
        }));
      }

      // Clear the input field
      setCategoryInput('');
    }
  };

  const removeCategory = (category: string) => {
    console.log('Removing category:', category);

    // Ensure categories is consistently processed
    const currentCategories = normalizeArrayData(formValues.categories);
    const updatedCategories = currentCategories.filter(c => c !== category);

    console.log('Categories after removal:', updatedCategories);

    setFormValues(prev => ({
      ...prev,
      categories: updatedCategories
    }));
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formValues.name?.trim()) {
      newErrors.name = 'Project name is required';
    }

    if (!formValues.startDate) {
      newErrors.startDate = 'Start date is required';
    }

    if (!formValues.endDate) {
      newErrors.endDate = 'End date is required';
    } else if (formValues.startDate && formValues.endDate && formValues.endDate < formValues.startDate) {
      newErrors.endDate = 'End date must be after start date';
    }

    if (!formValues.owner) {
      newErrors.owner = 'Project owner is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  /**
   * Handles form submission with proper validation and data processing
   */
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    console.log('DIRECT SUBMIT - Form submitted');

    // Log current state of all form values
    console.log('DIRECT SUBMIT - Form values at submission:', formValues);
    console.log('DIRECT SUBMIT - Description value:', formValues.description);
    console.log('DIRECT SUBMIT - Categories value:', formValues.categories);

    if (validateForm()) {
      try {
        setIsSubmitting(true);

        // Create a clean object with all fields explicitly included
        const finalFormValues: Omit<Project, 'id'> = {
          name: formValues.name || '',
          // Explicitly include description exactly as it is in the form
          description: formValues.description !== undefined ? formValues.description : '',
          startDate: formValues.startDate || new Date(),
          endDate: formValues.endDate || new Date(),
          owner: formValues.owner || '',
          // Ensure status is one of the valid values
          status: ((formValues.status || 'Active') as 'Active' | 'Completed' | 'On Hold' | 'Cancelled'),
          // Consistently process array data
          categories: normalizeArrayData(formValues.categories),
          members: normalizeArrayData(formValues.members)
        };

        // Log each field explicitly for debugging
        console.log('DIRECT SUBMIT - Final form values being submitted:');
        console.log('DIRECT SUBMIT - Name:', finalFormValues.name);
        console.log('DIRECT SUBMIT - Description:', finalFormValues.description);
        console.log('DIRECT SUBMIT - Start Date:', finalFormValues.startDate);
        console.log('DIRECT SUBMIT - End Date:', finalFormValues.endDate);
        console.log('DIRECT SUBMIT - Owner:', finalFormValues.owner);
        console.log('DIRECT SUBMIT - Status:', finalFormValues.status);
        console.log('DIRECT SUBMIT - Categories:', finalFormValues.categories);
        console.log('DIRECT SUBMIT - Members:', finalFormValues.members);

        // Submit the form data
        onSubmit(finalFormValues);

        // Log successful submission and trigger form closure
        console.log('DIRECT SUBMIT - Form submitted successfully, closing form');
        onCancel(); // Explicitly close the form after successful submission
      } catch (error) {
        console.error('DIRECT SUBMIT - Error submitting form:', error);
      } finally {
        setIsSubmitting(false);
      }
    } else {
      console.log('DIRECT SUBMIT - Form validation failed');
    }
  };

  // Format date for input
  const formatDateForInput = (date: Date | undefined) => {
    if (!date) return '';
    return date instanceof Date
      ? date.toISOString().split('T')[0]
      : new Date(date).toISOString().split('T')[0];
  };

  return (
    <form
      onSubmit={handleSubmit}
      className="space-y-3 text-gray-200"
    >
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
        {/* Project Name */}
        <div className="col-span-2">
          <label className="flex items-center text-sm font-medium text-gray-300 mb-1">
            <span className="inline-block w-2 h-2 rounded-full bg-purple-500 mr-2"></span>
            Project Name*
          </label>
          <input
            type="text"
            name="name"
            value={formValues.name || ''}
            onChange={handleChange}
            className={`w-full p-2 border rounded-md bg-gray-700 text-white ${errors.name ? 'border-red-500' : 'border-gray-600'} focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent`}
            placeholder="Enter project name (will be shown in italics)"
          />
          {errors.name && (
            <p className="mt-1 text-sm text-red-400 flex items-center">
              <AlertCircle className="w-4 h-4 mr-1" />
              {errors.name}
            </p>
          )}
        </div>

        {/* Start Date */}
        <div>
          <label className="flex items-center text-sm font-medium text-gray-300 mb-1">
            <span className="inline-block w-2 h-2 rounded-full bg-blue-500 mr-2"></span>
            Start Date*
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Calendar className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="date"
              value={formatDateForInput(formValues.startDate)}
              onChange={(e) => handleDateChange(e, 'startDate')}
              className={`w-full p-2 pl-10 border rounded-md bg-gray-700 text-white ${errors.startDate ? 'border-red-500' : 'border-gray-600'} focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
            />
          </div>
          {errors.startDate && (
            <p className="mt-1 text-sm text-red-400 flex items-center">
              <AlertCircle className="w-4 h-4 mr-1" />
              {errors.startDate}
            </p>
          )}
        </div>

        {/* End Date */}
        <div>
          <label className="flex items-center text-sm font-medium text-gray-300 mb-1">
            <span className="inline-block w-2 h-2 rounded-full bg-blue-500 mr-2"></span>
            End Date*
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Calendar className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="date"
              value={formatDateForInput(formValues.endDate)}
              onChange={(e) => handleDateChange(e, 'endDate')}
              className={`w-full p-2 pl-10 border rounded-md bg-gray-700 text-white ${errors.endDate ? 'border-red-500' : 'border-gray-600'} focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
            />
          </div>
          {errors.endDate && (
            <p className="mt-1 text-sm text-red-400 flex items-center">
              <AlertCircle className="w-4 h-4 mr-1" />
              {errors.endDate}
            </p>
          )}
        </div>

        {/* Project Owner */}
        <div>
          <label className="flex items-center text-sm font-medium text-gray-300 mb-1">
            <span className="inline-block w-2 h-2 rounded-full bg-green-500 mr-2"></span>
            Project Owner*
          </label>
          <select
            name="owner"
            value={formValues.owner || ''}
            onChange={handleChange}
            className={`w-full p-2 border rounded-md bg-gray-700 text-white ${errors.owner ? 'border-red-500' : 'border-gray-600'} focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent`}
          >
            <option value="">Select Project Owner</option>
            {users.map(user => (
              <option key={user.id} value={user.id}>
                {user.name} ({user.role})
              </option>
            ))}
          </select>
          {errors.owner && (
            <p className="mt-1 text-sm text-red-400 flex items-center">
              <AlertCircle className="w-4 h-4 mr-1" />
              {errors.owner}
            </p>
          )}
        </div>

        {/* Project Status */}
        <div>
          <label className="flex items-center text-sm font-medium text-gray-300 mb-1">
            <span className="inline-block w-2 h-2 rounded-full bg-yellow-500 mr-2"></span>
            Status
          </label>
          <select
            name="status"
            value={formValues.status || 'Active'}
            onChange={handleChange}
            className="w-full p-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
          >
            <option value="Active">Active</option>
            <option value="On Hold">On Hold</option>
            <option value="Completed">Completed</option>
            <option value="Cancelled">Cancelled</option>
          </select>
        </div>

        {/* Team Members */}
        <div className="col-span-2">
          <label className="flex items-center text-sm font-medium text-gray-300 mb-1">
            <span className="inline-block w-2 h-2 rounded-full bg-indigo-500 mr-2"></span>
            Team Members
          </label>
          <select
            multiple
            value={normalizeArrayData(formValues.members)}
            onChange={handleMultiSelect}
            className="w-full p-2 border border-gray-600 rounded-md bg-gray-700 text-white h-20 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
          >
            {users.map(user => (
              <option key={user.id} value={user.id}>
                {user.name} ({user.role})
              </option>
            ))}
          </select>
          <p className="mt-1 text-xs text-gray-400">Hold Ctrl/Cmd to select multiple team members</p>
        </div>

        {/* Categories */}
        <div className="col-span-2">
          <label className="flex items-center text-sm font-medium text-gray-300 mb-1">
            <span className="inline-block w-2 h-2 rounded-full bg-pink-500 mr-2"></span>
            Categories
          </label>
          <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0">
            <input
              type="text"
              value={categoryInput}
              onChange={(e) => setCategoryInput(e.target.value)}
              className="w-full p-2 border border-gray-600 rounded-md sm:rounded-r-none bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent focus:z-10"
              placeholder="Add categories (e.g., Marketing, Design)"
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleAddCategory(e);
                }
              }}
            />
            <button
              type="button"
              onClick={handleAddCategory}
              className="px-4 py-2 bg-pink-600 text-white rounded-md sm:rounded-l-none hover:bg-pink-700 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-offset-1 focus:ring-offset-gray-800"
            >
              Add
            </button>
          </div>
          <p className="mt-1 text-xs text-gray-400">Type a category name and click Add or press Enter</p>

          {/* Categories display */}
          <div className="mt-2">
            {(() => {
              // Process categories consistently
              const categoryArray = normalizeArrayData(formValues.categories);

              if (categoryArray.length > 0) {
                return (
                  <div className="flex flex-wrap gap-2">
                    {categoryArray.map((category, index) => {
                      // Get consistent color for this category
                      const colorClass = getCategoryColorClass(category);

                      return (
                        <span
                          key={`category-${index}`}
                          className={`${colorClass} px-3 py-1.5 rounded-full text-xs font-medium flex items-center shadow-sm`}
                        >
                          {category}
                          <button
                            type="button"
                            onClick={() => removeCategory(category)}
                            className="ml-1.5 hover:text-white flex items-center justify-center w-4 h-4 rounded-full bg-black bg-opacity-20 text-xs"
                            aria-label={`Remove ${category} category`}
                            title="Remove category"
                          >
                            ×
                          </button>
                        </span>
                      );
                    })}
                  </div>
                );
              }
              return null;
            })()}
          </div>
        </div>

        {/* Description */}
        <div className="col-span-2">
          <label className="flex items-center text-sm font-medium text-gray-300 mb-1">
            <span className="inline-block w-2 h-2 rounded-full bg-teal-500 mr-2"></span>
            Description
          </label>
          <textarea
            name="description"
            value={formValues.description !== undefined ? formValues.description : ''}
            onChange={(e) => {
              const newValue = e.target.value;
              console.log('DIRECT FORM - Description changed to:', newValue);
              // Ensure the description is properly updated in the form state
              setFormValues(prev => {
                const updated = {
                  ...prev,
                  description: newValue
                };
                console.log('DIRECT FORM - Updated form values:', updated);
                return updated;
              });
            }}
            rows={4}
            className="w-full p-2 border border-gray-600 rounded-md bg-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent"
            placeholder="Enter project description"
          />
        </div>
      </div>

      <div className="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-3 space-y-3 space-y-reverse sm:space-y-0 pt-3 border-t border-gray-700 sticky bottom-0 bg-gray-800 pb-1 mt-3">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 border border-gray-600 rounded-md text-gray-300 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 w-full sm:w-auto"
          disabled={isSubmitting}
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={isSubmitting}
          className={`px-4 py-2 ${isSubmitting ? 'bg-purple-700' : 'bg-purple-600 hover:bg-purple-700'} text-white rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-1 focus:ring-offset-gray-800 flex items-center justify-center w-full sm:w-auto`}
        >
          {isSubmitting ? (
            <>
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Processing...
            </>
          ) : (
            initialValues?.id ? 'Update Project' : 'Create Project'
          )}
        </button>
      </div>
    </form>
  );
};

export default ProjectForm;