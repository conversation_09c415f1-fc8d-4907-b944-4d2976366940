import { adminDb, adminStorage } from 'components/firebase-admin';
import { Pinecone } from '@pinecone-database/pinecone';

// Initialize Pinecone client
const pineconeClient = new Pinecone({
  apiKey: process.env.PINECONE_API_KEY || '',
});

// Get the Pinecone index with the fixed name 'ikedia'
const getPineconeIndex = async () => {
  const indexName = 'ikedia';
  return pineconeClient.index(indexName);
};

export const deleteDocumentAndChatsByNamespace = async (userId: string, namespace: string) => {
  console.log(`[DELETE DOC] Starting deletion process for namespace: ${namespace}, userId: ${userId}`);
  try {
    // 1. Delete the document metadata from Firestore (user's files collection)
    console.log(`[DELETE DOC] Deleting document metadata from Firestore`);
    const filesRef = adminDb.collection('users').doc(userId).collection('files');

    // Collect docs to delete (support legacy schemas and direct docId)
    let docsToDelete: any[] = [];

    // Primary: namespace field
    try {
      const primarySnapshot = await filesRef.where('namespace', '==', namespace).get();
      if (!primarySnapshot.empty) {
        docsToDelete = primarySnapshot.docs;
      }
    } catch (e) {
      console.warn(`[DELETE DOC] Error querying by namespace field:`, e);
    }

    // Fallback 1: direct docId equals namespace
    if (docsToDelete.length === 0) {
      try {
        const directSnap = await filesRef.doc(namespace).get();
        if (directSnap.exists) {
          docsToDelete = [directSnap];
        }
      } catch (e) {
        console.warn(`[DELETE DOC] Error fetching direct docId ${namespace}:`, e);
      }
    }

    // Fallback 2: legacy field 'fileId'
    if (docsToDelete.length === 0) {
      try {
        const legacySnapshot = await filesRef.where('fileId', '==', namespace).get();
        if (!legacySnapshot.empty) {
          docsToDelete = legacySnapshot.docs;
        }
      } catch (e) {
        console.warn(`[DELETE DOC] Error querying by legacy fileId field:`, e);
      }
    }

    if (docsToDelete.length === 0) {
      console.warn(`[DELETE DOC] No document metadata found for namespace: ${namespace}. Proceeding with other cleanup steps.`);
    } else {
      console.log(`[DELETE DOC] Found ${docsToDelete.length} document(s) to delete`);
    }

    const fileDocIds: string[] = [];
    for (const fileDoc of docsToDelete) {
      const fileDocumentId = fileDoc.id;
      fileDocIds.push(fileDocumentId);
      try {
        console.log(`[DELETE DOC] Deleting document with ID: ${fileDocumentId}`);
        await fileDoc.ref.delete();
      } catch (e) {
        console.error(`[DELETE DOC] Error deleting document ${fileDocumentId}:`, e);
      }
    }

    // Delete associated chats for found docIds or fallback to using namespace
    if (fileDocIds.length > 0) {
      for (const fileDocumentId of fileDocIds) {
        try {
          console.log(`[DELETE DOC] Deleting associated chats for document: ${fileDocumentId}`);
          await deleteAssociatedChats(userId, fileDocumentId);
        } catch (e) {
          console.error(`[DELETE DOC] Error deleting chats for document ${fileDocumentId}:`, e);
        }
      }
    } else {
      // Fallback: in our architecture, docId is typically equal to namespace
      try {
        console.log(`[DELETE DOC] No docId found; attempting chat cleanup using namespace as fileDocumentId`);
        await deleteAssociatedChats(userId, namespace);
      } catch (e) {
        console.warn(`[DELETE DOC] Chat cleanup by namespace fallback failed:`, e);
      }
    }

    // 2. Delete the embeddings from Pinecone
    console.log(`[DELETE DOC] Attempting to delete vectors from Pinecone for namespace: ${namespace}`);
    try {
      await deleteVectorsFromPinecone(namespace);
      console.log(`[DELETE DOC] Successfully deleted vectors from Pinecone`);
    } catch (pineconeError) {
      console.error(`[DELETE DOC] Error deleting vectors from Pinecone, but continuing:`, pineconeError);
      // Continue with the deletion process even if Pinecone deletion fails
    }

    // 3. Delete the document chunks from Firestore (byteStoreCollection)
    console.log(`[DELETE DOC] Deleting document chunks from Firestore`);
    await deleteDocumentChunks(namespace, userId);

    // 4. Delete the file from Firebase Storage
    console.log(`[DELETE DOC] Deleting file from Firebase Storage`);
    try {
      await deleteFileFromStorage(namespace, userId);
      console.log(`[DELETE DOC] Successfully deleted file from Firebase Storage`);
    } catch (storageError) {
      console.error(`[DELETE DOC] Error deleting file from storage, but continuing:`, storageError);
      // Continue with the process even if storage deletion fails
    }

    // 5. Add a notification after the document and associated chats have been deleted
    console.log(`[DELETE DOC] Adding notification for user: ${userId}`);
    await addNotification(userId, `Document with namespace: ${namespace} has been successfully removed.`);

    console.log(`[DELETE DOC] Deletion process completed successfully`);
    return `Document and associated chats with namespace: ${namespace} have been successfully deleted.`;
  } catch (error) {
    console.error('[DELETE DOC] Error deleting document and chats by namespace:', error);
    throw new Error('Error deleting document and chats.');
  }
};

export const deleteFolderAndChatsByCategory = async (userId: string, category: string) => {
  console.log(`[DELETE FOLDER] Starting bulk deletion for category: ${category}, userId: ${userId}`);
  const summary: { total: number; successes: Array<{ namespace: string; name?: string }>; failures: Array<{ namespace: string; name?: string; error: string }>; } = {
    total: 0,
    successes: [],
    failures: []
  };
  try {
    const filesRef = adminDb.collection('users').doc(userId).collection('files');
    const q = filesRef.where('category', '==', category);
    const snap = await q.get();

    if (snap.empty) {
      console.warn(`[DELETE FOLDER] No files found in category '${category}' for user ${userId}`);
      await addNotification(userId, `No files found in folder '${category}'. Nothing to delete.`);
      return summary;
    }

    summary.total = snap.docs.length;
    console.log(`[DELETE FOLDER] Found ${summary.total} file(s) in category '${category}'`);

    for (const d of snap.docs) {
      const data = d.data() as any;
      const namespace = data?.namespace || d.id || data?.fileId;
      const name = data?.name || data?.fileName || d.id;
      if (!namespace) {
        console.warn(`[DELETE FOLDER] Skipping a file with no resolvable namespace. DocId=${d.id}`);
        summary.failures.push({ namespace: d.id, name, error: 'No resolvable namespace' });
        continue;
      }

      try {
        console.log(`[DELETE FOLDER] Deleting by namespace ${namespace} (${name})`);
        await deleteDocumentAndChatsByNamespace(userId, namespace);
        summary.successes.push({ namespace, name });
      } catch (e: any) {
        console.error(`[DELETE FOLDER] Failed to delete ${namespace}:`, e);
        summary.failures.push({ namespace, name, error: e?.message || 'Unknown error' });
      }
    }

    const successCount = summary.successes.length;
    const failCount = summary.failures.length;
    await addNotification(userId, `Deleted folder '${category}': ${successCount} succeeded, ${failCount} failed.`);
    console.log(`[DELETE FOLDER] Completed bulk deletion for '${category}'. Successes: ${successCount}, Failures: ${failCount}`);

    return summary;
  } catch (error) {
    console.error('[DELETE FOLDER] Error deleting folder by category:', error);
    throw new Error('Error deleting folder and chats.');
  }
};



const deleteVectorsFromPinecone = async (namespace: string) => {
  try {
    console.log(`[DELETE VECTORS] Using fixed Pinecone index name: 'ikedia'`);
    console.log(`[DELETE VECTORS] Attempting to delete all vectors in namespace: ${namespace}`);

    try {
      // Get the index using our helper function
      const index = await getPineconeIndex();
      console.log(`[DELETE VECTORS] Retrieved Pinecone index`);

      // Try to delete all vectors in the namespace
      await index.namespace(namespace).deleteAll();
      console.log(`[DELETE VECTORS] Vectors in namespace '${namespace}' have been deleted from Pinecone.`);
    } catch (pineconeError) {
      console.error('[DELETE VECTORS] Error accessing Pinecone index:', pineconeError);

      // Try an alternative approach - delete by filter
      try {
        console.log('[DELETE VECTORS] Trying alternative approach - delete by filter');
        const index = await getPineconeIndex();
        await index.deleteMany({
          filter: { namespace: namespace }
        });
        console.log(`[DELETE VECTORS] Vectors deleted using filter approach`);
      } catch (filterError) {
        console.error('[DELETE VECTORS] Error with filter approach:', filterError);
        // Continue execution
      }
    }
  } catch (error) {
    console.error('[DELETE VECTORS] Error deleting vectors from Pinecone:', error);
    console.error('[DELETE VECTORS] Error details:', JSON.stringify(error, null, 2));

    // Continue execution instead of throwing
    console.log('[DELETE VECTORS] Continuing execution despite Pinecone error');
  }
};

const deleteAssociatedChats = async (userId: string, fileDocumentId: string) => {
  try {
    const chatsRef = adminDb.collection('users').doc(userId).collection('chats');
    const chatsQuery = chatsRef.where('fileDocumentId', '==', fileDocumentId);
    const chatSnapshot = await chatsQuery.get();

    if (!chatSnapshot.empty) {
      for (const chatDoc of chatSnapshot.docs) {
        await deleteAssociatedMessages(userId, chatDoc.id);
        await chatDoc.ref.delete();
      }
    }
  } catch (error) {
    console.error("Error deleting associated chats:", error);
    throw new Error("Error deleting associated chats.");
  }
};

const deleteAssociatedMessages = async (userId: string, chatId: string) => {
  try {
    const messagesRef = adminDb.collection('users').doc(userId).collection('chats').doc(chatId).collection('messages');
    const messagesSnapshot = await messagesRef.get();
    for (const messageDoc of messagesSnapshot.docs) {
      await messageDoc.ref.delete();
    }
  } catch (error) {
    console.error("Error deleting associated messages:", error);
    throw new Error("Error deleting associated messages.");
  }
};

const deleteDocumentChunks = async (namespace: string, userId: string ) => {
  try {
    const byteCollection = `users/${userId}/byteStoreCollection`;
    const byteStoreCollectionRef = adminDb.collection(byteCollection);

    // Try primary field path 'metadata.doc_id'
    let chunksSnapshot = await byteStoreCollectionRef.where('metadata.doc_id', '==', namespace).get();

    // Fallback: some writers may have used 'namespace' in metadata
    if (chunksSnapshot.empty) {
      try {
        chunksSnapshot = await byteStoreCollectionRef.where('metadata.namespace', '==', namespace).get();
      } catch {}
    }

    if (!chunksSnapshot.empty) {
      const batch = adminDb.batch();
      chunksSnapshot.docs.forEach((doc) => batch.delete(doc.ref));
      await batch.commit();
      console.log(`Document chunks for namespace ${namespace} deleted from Firestore.`);
    } else {
      console.warn(`No document chunks found for namespace ${namespace} in Firestore.`);
    }
  } catch (error) {
    console.error('Error deleting document chunks from Firestore:', error);
    // Do not throw; continue with overall deletion
  }
};

const deleteFileFromStorage = async (namespace: string, userId: string) => {
  try {
    const bucketName = process.env.FIREBASE_STORAGE_BUCKET;
    if (!bucketName) {
      console.warn('[DELETE STORAGE] FIREBASE_STORAGE_BUCKET not set; skipping storage deletion.');
      return;
    }

    const bucket = adminStorage.bucket(bucketName);

    // Try canonical path first
    const primaryPath = `uploads/${userId}/${namespace}`;
    try {
      await bucket.file(primaryPath).delete();
      console.log(`[DELETE STORAGE] Deleted file at ${primaryPath}`);
      return;
    } catch (e) {
      console.warn(`[DELETE STORAGE] Primary path not found: ${primaryPath}`);
    }

    // Fallbacks: try common legacy paths
    const fallbackPaths = [
      `uploads/${namespace}`,
      `users/${userId}/uploads/${namespace}`
    ];

    for (const p of fallbackPaths) {
      try {
        await bucket.file(p).delete();
        console.log(`[DELETE STORAGE] Deleted file at ${p}`);
        return;
      } catch {}
    }

    console.warn(`[DELETE STORAGE] No storage file found for namespace ${namespace}`);
  } catch (error) {
    console.error('Error deleting file from Firebase Storage:', error);
    // Do not throw; continue overall deletion
  }
};


const addNotification = async (userId: string, message: string) => {
  try {
    const notificationsRef = adminDb.collection('users').doc(userId).collection('notifications');
    await notificationsRef.add({
      message: message,
      timestamp: new Date(),
      read: false
    });
    console.log(`Notification added for user: ${userId} - ${message}`);
  } catch (error) {
    console.error('Error adding notification:', error);
    throw new Error('Error adding notification.');
  }
};